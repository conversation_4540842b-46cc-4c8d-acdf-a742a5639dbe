<?xml version="1.0" encoding="UTF-8"?>
<phpunit xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="https://schema.phpunit.de/10.5/phpunit.xsd" bootstrap="vendor/autoload.php" colors="true" cacheDirectory=".phpunit.cache">
  <testsuites>
    <testsuite name="Feature">
      <directory suffix="Test.php">./tests/Feature</directory>
    </testsuite>
    <testsuite name="Actions">
      <!-- <directory suffix="Test.php">./tests/Actions</directory> -->
      <directory suffix="Test.php">./tests/Actions/Mails</directory>
      <directory suffix="Test.php">./tests/Actions/React</directory>
      <directory suffix="Test.php">./tests/Actions/V2</directory>
    </testsuite>
  </testsuites>
  <php>
    <env name="APP_ENV" value="testing"/>
    <env name="APP_URL" value="http://c-hisab-kitab.test"/>
    <env name="BCRYPT_ROUNDS" value="4"/>
    <env name="CACHE_DRIVER" value="array"/>
    <env name="DB_CONNECTION" value="testing"/>
    <env name="DB_DATABASE" value="hisab-kitab-testing"/>
    <env name="MAIL_MAILER" value="array"/>
    <env name="QUEUE_CONNECTION" value="sync"/>
    <env name="SESSION_DRIVER" value="array"/>
    <env name="TELESCOPE_ENABLED" value="false"/>
    <env name="OTP_TESTING" value="true"/>
    <env name="IS_CHECK_VALIDATION" value="false"/>
  </php>
  <source>
    <include>
      <directory suffix=".php">./app</directory>
    </include>
  </source>
</phpunit>
