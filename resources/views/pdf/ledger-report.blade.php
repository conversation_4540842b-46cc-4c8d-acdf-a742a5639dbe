<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport"
        content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>{{ ucfirst($reportType) }} Report</title>
    <style>
        /* @font-face {
            font-family: Poppins;
            src: url(../fonts/Poppins-regular.ttf);
        }

        body {
            font-family: 'Poppins', sans-serif;
        } */

        .line-height-85 {
            line-height: 85% !important;
        }

        .page-break {
            page-break-after: always;
        }

        @page {
            margin-top: 10px;
            margin-bottom: 10px;
        }

        .mb-0 {
            margin-bottom: 0;
        }

        .section-heading {
            margin-bottom: 15px;
        }

        .table-bordered {
            border: 1px solid gray !important;
        }

        .text-center {
            text-align: center;
        }

        .text-right {
            text-align: right;
        }

        .w-100 {
            width: 100%
        }

        thead tr th,
        td {
            font-size: 11px;
            font-weight: normal !important;
        }

        tbody tr td {
            font-size: 11px;
            font-weight: normal !important
        }

        tfoot tr td {
            font-size: 11px;
            font-weight: normal !important;
        }

        .heading {
            font-weight: bold;
        }

        .fw-lighter {
            font-weight: lighter !important;
        }

        td,
        tr,
        th {
            border: 1px solid gray;
        }

        td,
        th {
            padding: 3px 5px 5px 5px;
        }

        .fs-12 {
            font-size: 12px;
        }

        .fs-10 {
            font-size: 10px;
        }

        .fs-13 {
            font-size: 13px;
        }

        table {
            border-collapse: collapse;
        }

        footer {
            font-weight: lighter !important;
            height: 20px;
            position: fixed;
            bottom: 0;
        }

        /* .custom-font-family {
            font-family: DejaVu Sans, Poppins, "Helvetica", Arial, "Liberation Sans", sans-serif !important;
        } */

        .text-start {
            text-align: left !important;
        }

        .border-top-0 {
            border-top: none !important;
        }

        .border-bottom-0 {
            border-bottom: none !important;
        }

        .border-start-0 {
            border-left: none !important;
        }

        .border-end-0 {
            border-right: none !important;
        }

        .vertical-align-top {
            vertical-align: top;
        }

        .company-logo-img {
            width: 140px;
            height: 40px;
        }

        .w-100 {
            width: 100%;
        }

        .h-100 {
            height: 100%;
        }

        .object-fit-contain {
            object-fit: contain !important;
        }

        .text-end {
            text-align: right !important;
        }

        .d-inline-block {
            display: inline-block !important;
        }

        .border-0 {
            border-top: 0 !important;
            border-right: 0 !important;
            border-left: 0 !important;
        }

        .my-0 {
            margin-top: 0;
            margin-bottom: 0;
        }
    </style>
</head>

<body>
    @foreach ($ledgerReportDatas as $ledgerReportData)
        <div class="section-heading">
            <table width="100%">
                <thead>
                    <th class="text-primary text-start border-bottom-0 border-0 border-top-0 line-height-85">
                        <p class=" mb-0 heading">{{ $company?->trade_name }}</p>
                    </th>
                    <th class="border-bottom-0 border-0"></th>
                </thead>
                <tbody>
                    <tr class="border-0 vertical-align-top">
                        <td class="border-0 border-end-0" style="width: 40%">
                            <span class="fs-13 fw-lighter mb-0 line-height-85">
                                @if (!empty($companyAddress))
                                    {{ $companyAddress->address_1 ?? null }},<br>
                                    {{ $companyAddress->address_2 ?? null }},<br>
                                    {{ getCityName($companyAddress->city_id ?? null) }},
                                    {{ getStateName($companyAddress->state_id ?? null) . ' -' . $companyAddress->pin_code ?? null }}
                                    <br>
                                @endif
                                @if ($company->is_gst_applicable)
                                    <p class="fs-13 my-0">GSTIN : {{ $company->companyTax->gstin ?? null }}</p>
                                @endif
                                <p class="fs-13 my-0">{{ $company->companyTax?->pan_number ? 'PAN : ' . $company->companyTax->pan_number : null }}</p>
                                @if (getCustomInvoiceSetting('email'))
                                    <p class="fs-13 my-0">Email: {{ (isset(getCompanySettings()['alternate_email']) ? getCompanySettings()['alternate_email'] : $company->user->email) ?? null }}</p>
                                @endif
                                @if (getCustomInvoiceSetting('mobile_number'))
                                    <p class="fs-13 my-0">Contact : {{ '  ' . (isset(getCompanySettings()['alternate_phone']) ? getCompanySettings()['alternate_phone'] : $company->phone) ?? null }}</p>
                                @endif
                            </span>
                        </td>
                        <td class="vertical-align-top border-0 ps-2 text-end" style="margin-top:0;">
                            @if(isset($company->company_logo) && getCustomInvoiceSetting('logo') &&  $company->company_logo != asset('assets/images/company-logo.png'))
                                <img src={{ $company->company_logo ?? '' }} alt="Logo" style="object-fit: contain; margin-bottom:10px" width="100">
                            @endif
                            <div class="vertical-align-top text-end line-height-85">
                                <p class="fs-13 mb-0 heading">{{ ucfirst($reportType) }} Report</p>
                                <span class="fs-12">{{ $durationDate }}</span>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        <div class="text-center" style="margin-bottom: 2px">
            <span class="fs-13"><b>Ledger Name: {{ $ledgerReportData['ledgerName'] ?? '' }}</b></span>
        </div>
        @if (isset($ledgerReportData['billing_address']))
            <div class="text-center fs-12">
                <span>{{ $ledgerReportData['billing_address'] ?? '' }}</span>
            </div>
        @endif
        <table class="table table-bordered w-100">
            <thead>
                <tr>

                    <th scope="col" class="text-center"><b>Date</b></th>
                    <th scope="col" class="text-center"><b>Ledger Name</b></th>
                    <th scope="col" class="text-center"><b>Transaction Type</b></th>
                    {{-- @if ($checkNarration == "true")
                        <th scope="col" class="text-center"><b>Narration</b></th>
                    @endif --}}
                    <th scope="col" class="text-center" style="min-width: 45px !important;"><b>Vch. No</b></th>
                    {{-- @if ($reportType != 'cashBank')
                        <th scope="col" class="text-center"><b>Inv. No</b></th>
                    @endif --}}
                    <th scope="col" class="text-right" style="min-width: 60px !important;"><b>Debit Amt.</b></th>
                    <th scope="col" class="text-right" style="min-width: 60px !important;"><b>Credit Amt.</b></th>
                    <th scope="col" class="text-right"><b>Balance</b></th>
                </tr>
            </thead>
            <tbody>
                {{--    @foreach ($ledgerReportDatas as $ledgerReportData) --}}
                @php
                    $ledgerReportBalanceType = $ledgerReportData['closing_balance']['balanceType'] ?? null;
                    $ledgerReportBalance = $ledgerReportData['closing_balance']['balance'] ?? 0;
                    $ledgerDetails = $ledgerReportData['data'] ?? [];
                    $debitAmountTotal = 0;
                    $creditAmountTotal = 0;
                @endphp

                @if (count($ledgerReportData['closing_balance'] ?? []))


                    <tr>
                        <td
                            colspan="6">
                            <b>Opening Balance</b>
                        </td>
                        @php
                            $debitAndCreditType = \App\Models\Ledger::OPENING_BALANCE_DR_CR[$ledgerReportBalanceType];
                            $balance = getCurrencyFormat($ledgerReportBalance) . ' ' . $debitAndCreditType;
                        @endphp
                        <td class="text-right fs-10"><b>{!! $balance !!}</b></td>
                    </tr>
                @endif
                @foreach ($ledgerReportData['data'] as $item)
                    <tr>
                        <td class="text-center" style="min-width: 50px;">{{ $item['date'] }}</td>
                        @if ($config == 1)
                            <td>
                                {{ $item['ledger_name'] }}
                                @php
                                    $narration = $item['narration'] ?? null;
                                @endphp
                                @if(!empty($narration))
                                <br>
                                    <span style="font-style: italic;">Narration :{!! nl2br(str_replace("&", "",$narration)) !!}</span>
                                @endif
                            </td>
                        @else
                            <td>
                                @if (isset($item['tcs_amount']))
                                    <div>
                                        @foreach ($item['ledger_list'] as $ledgerItem)
                                            <span class="custom-font-family">{{ $ledgerItem['name'] }} (Taxable Val)
                                                :{{  getCurrencyFormat($ledgerItem['amount']) }}</span>
                                            <br>
                                        @endforeach
                                        <span class="custom-font-family">TCS Amount
                                            :{{  getCurrencyFormat($item['tcs_amount']) }}</span><br>
                                        @if (isCompanyGstApplicable())
                                            <span class="custom-font-family">CGST
                                                :{{  getCurrencyFormat($item['cgst']) }}</span>
                                            <br>
                                            <span
                                                class="custom-font-family">IGST:{{  getCurrencyFormat($item['igst']) }}</span>
                                            <br>
                                            <span
                                                class="custom-font-family">SGST:{{  getCurrencyFormat($item['sgst']) }}</span>
                                            <br>

                                            @foreach ($item['additional_charges_addless'] as $additionalChargeAndAddless)
                                                <span>{{ $additionalChargeAndAddless['name'] }}:{{ getCurrencyFormat($additionalChargeAndAddless['amount']) }}</span>
                                                <br>
                                            @endforeach
                                            <span
                                                class="custom-font-family">Cess:{{  getCurrencyFormat($item['cess']) }}</span>
                                            <br>
                                        @endif
                                        <span class="custom-font-family">Round
                                            off:{{  getCurrencyFormat($item['rounding_amount']) }}</span><br>
                                            @php
                                    $narration = $item['narration'] ?? null;
                                @endphp
                                @if(!empty($narration))
                                <span style="font-style: italic;">Narration :{!! nl2br(str_replace("&", "",$narration)) !!}</span>
                                @endif

                                </div>
                                @else
                                    {{ $item['ledger_name'] }}
                                    @php
                                    $narration = $item['narration'] ?? null;
                                @endphp

                                @if(!empty($narration))
                                <br>
                                <span style="font-style: italic;">Narration :{!! nl2br(str_replace("&", "",$narration)) !!}</span>
                                @endif
                                @endif
                                @php
                                    $settleInvoiceNo = $item['settle_invoice_no'] ?? [];
                                @endphp
                                @if(!empty($settleInvoiceNo) && is_array($settleInvoiceNo))
                                    <div>
                                        @foreach($settleInvoiceNo as $type => $invoices)
                                            <div style="font-size: 10px">{{ $type }}: {{ implode(', ', $invoices) }}</div>
                                        @endforeach
                                    </div>
                                @endif
                                @if (isset($item['transaction_item_list']) && !empty($item['transaction_item_list']))
                                    <div>
                                        <div style="font-size: 10px">Transaction Items:<br>{!! $item['transaction_item_list'] !!}</div>
                                    </div>
                                @endif
                            </td>
                        @endif
                        <td class="text-center">{{ $item['transaction_type'] }}</td>
                        {{-- @if ($checkNarration == "true")
                            @php
                                $narration = $item['narration'] ?? null;
                            @endphp
                            <td>{!! nl2br(str_replace('&', '', $narration)) !!}</td>
                        @endif --}}
                        <td class="text-center">{{ $item['transaction_type'] == 'Purchase' ? ($item['invoice_no'] ?? $item['voucher_no']) : $item['voucher_no'] }}</td>
                        {{-- @if ($reportType != 'cashBank')
                            <td class="text-center">{{ $item['invoice_no'] }}</td>
                        @endif --}}
                        <td class="text-right">{{ getCurrencyFormat($item['debit_amount']) }}
                        </td>
                        <td class="text-right">{{ getCurrencyFormat($item['credit_amount']) }}
                        </td>
                        @php
                            $debitAmountTotal += $item['debit_amount'];
                            $creditAmountTotal += $item['credit_amount'];

                            if ($ledgerReportBalanceType == 'undefined' || $ledgerReportBalanceType == null) {
                                if ($item['credit_amount'] > 0) {
                                    $ledgerReportBalanceType = 2;
                                } else {
                                    $ledgerReportBalanceType = 1;
                                }
                            }
                            if ($item['credit_amount'] != 0) {
                                if ($ledgerReportBalanceType == 2) {
                                    $ledgerReportBalance += $item['credit_amount'];
                                } else {
                                    $ledgerReportBalance -= $item['credit_amount'];
                                }
                            } else {
                                if ($ledgerReportBalanceType == 1) {
                                    $ledgerReportBalance += $item['debit_amount'];
                                } else {
                                    $ledgerReportBalance -= $item['debit_amount'];
                                }
                            }
                            if ($ledgerReportBalance < 0) {
                                if ($ledgerReportBalanceType == 1) {
                                    $ledgerReportBalanceType = 2;
                                } elseif ($ledgerReportBalanceType == 2) {
                                    $ledgerReportBalanceType = 1;
                                }
                            }
                            $debitAndCreditType = \App\Models\Ledger::OPENING_BALANCE_DR_CR[$ledgerReportBalanceType];
                            $ledgerReportBalance = abs($ledgerReportBalance);
                            $balance = getCurrencyFormat($ledgerReportBalance) . ' ' . $debitAndCreditType;
                        @endphp
                        <td class="text-right" style="min-width: 85px;">{!! $balance !!}</td>
                    </tr>
                @endforeach

                @if (isset($balance) && !empty($balance))
                    <tr>
                        <td
                            colspan="4">
                            <b>Closing Balance</b>
                        </td>
                        <td class="text-right fs-10"><b>{{ getCurrencyFormat($debitAmountTotal) }}</b>
                        </td>
                        <td class="text-right fs-10"><b>{{ getCurrencyFormat($creditAmountTotal) }}</b>
                        </td>
                        <td class="text-right"><b>{!! $balance !!}</b></td>
                    </tr>
                @endif
            </tbody>
        </table>
        @if (!$loop->last)
            <div class="page-break"></div>
        @endif
    @endforeach
</body>

</html>
