<div>
    <div wire:loading.class="page-loader">
    </div>
    <div class="d-flex justify-content-end">

    </div>
    <div class="d-flex flex-wrap align-items-center justify-content-between w-auto mt-2">
        <div class="d-flex  align-items-center">
            <div class="custom-align-block-table float-start fw-bold ms-0">
            GSTIN: <span class="text-primary">{{ getCurrentCompany()->companyTax->gstin }}</span>
        </div>
        <div class="d-flex custom-align-block-table mx-1 ms-2">
            <select name="payment_status" id="changeFinancialYear" data-control="select2" class="form-select">
                <option value="0" default selected>Select Financial Year</option>
                @foreach (checkFinancialYearForGST() as $key => $value)
                    <option value="{{ $key }}" @if ($rows['financialYears'] == $key) selected="" @endif>
                        {{ $value }}</option>
                @endforeach
            </select>
        </div>
        </div>
      <div class="d-flex align-items-center ms-auto flex-wrap justify-content-end">
          <div class="p-2 rounded custom-align-block-table ms-auto me-2"
            style="background-color: #ffffff;border: 1px solid #8E92A2;font-weight: 500;">
            Last Status Updated :
            {{ Illuminate\Support\Carbon::parse(getLastStatusUpdateGstDate('updated_at'))->format('jS M Y h:i A') }}
        </div>
        @if (getGstDashboardLogin('status'))
            {{-- <div class="py-2 rounded custom-align-block-table me-2">
                <a href="javascript:void(0)" class="fetch-data-gstr {{ getGstDashboardLogin('synced') ? 'disabled' : '' }}" title="sync"  style="{{ getGstDashboardLogin('synced') ? 'pointer-events: none; opacity: 0.5;' : '' }}">
                    <i class="fa-solid fa-repeat text-gray fs-1 text-primary px-1 {{ getGstDashboardLogin('synced') ? 'rotate-animation' : '' }}"></i>
                </a>
            </div> --}}
            @if($syncCompleted == \App\Models\GstrLogin::SYNC_NOT_STARTED)
                <div class="sync-box custom-align-block-table">
                    <button id="syncBtn" class="btn btn-sm fetch-data-gstr">Start GST Sync</button>
                </div>
            @elseif($syncCompleted == \App\Models\GstrLogin::SYNC_IN_PROGRESS)
                <div class="sync-box custom-align-block-table">
                    <button id="syncBtn" class="p-2 rounded ms-auto running fetch-data-gstr" disabled><span class="spin">⏳</span> Sync Running...</button>
                </div>
            @elseif($syncCompleted == \App\Models\GstrLogin::SYNC_COMPLETED)
                <div class="sync-box custom-align-block-table">
                    <button id="syncBtn" class="btn btn-sm fetch-data-gstr">🔄 Sync GST Data</button>
                </div>
            @endif
        @endif
        <div class="custom-align-block-table ms-1">
            @if (getLoginUser()->can('company_import_export_ageing_report'))
                @if (!getGstDashboardLogin('status'))
                    <a class="btn btn-sm transaction-shortcut-btn connect-gstin-btn fw-bold" href="#">
                        Connect GSTIN
                    </a>
                @else
                    <a class="btn btn-sm transaction-shortcut-btn fw-bold" href="javascript:void(0);">
                        GSTIN Connected
                    </a>
                @endif
            @endif
        </div>
        {{-- @if (getLoginUser()->can('company_import_export_ageing_report')) --}}
            <div class="d-flex custom-align-block-table ms-1 align-items-center justify-content-end">
                <a href="{{ route('company.export-gstr1-report', ['type' => 'pdf']) }}" class="text-hover-orange mx-2" target="_blank">
                    <i class="far fa-file-pdf text-gray fs-1 text-orange"></i>
                </a>
                <a href="{{ route('company.export-gstr1-report', ['type' => 'excel']) }}" class="text-hover-orange ms-2" target="_blank">
                    <i class="far fa-file-excel text-gray fs-1 text-success"></i>
                </a>
            </div>
        {{-- @endif --}}
      </div>
    </div>
    <div class="row d-flex border-0 pt-4 justify-content-end">
        <div class="card card-border-1 p-0">
            <div class="card-body py-4 px-5">
                <div class="flipped">
                    <div class="px-4 custom-livewire-table table-drop-select-top" style="overflow: auto;">
                        <table class="table table-responsive-sm text-nowrap flipped-table gst-dashboard-table">
                            <thead>
                                <tr>
                                    <th></th>
                                    <th colspan="2" class="text-center">
                                        <div class="d-flex justify-content-center mb-1">
                                            GST Filing Status
                                        </div>
                                    </th>
                                    <th colspan="3">
                                        <div class="d-flex align-items-center">
                                            <div class="ms-auto">Sales Reconciliation</div>
                                            <div class="ms-auto">
                                                <a href="{{ route('company.gstr1-view-detailed', ['section' => 'sales']) }}"
                                                    class="btn-outline-primary btn btn-sm border border-1 border-primary py-1 px-3"
                                                    type="button">Details</a>
                                            </div>
                                        </div>
                                    </th>
                                    <th colspan="3" class="text-center">
                                        <div class="d-flex align-items-center">
                                            <div class="ms-auto">ITC Reconciliation</div>
                                            <div class="ms-auto">
                                                <a href="{{ route('company.gstr1-view-detailed', ['section' => 'itc']) }}"
                                                    class="btn-outline-primary btn btn-sm border border-1 border-primary py-1 px-3"
                                                    type="button">Details</a>
                                            </div>
                                        </div>
                                    </th>
                                </tr>

                                <tr>
                                    @include('livewire.custom.header', ['fields' => $rows['fields']])
                                </tr>
                            </thead>
                            <tbody>
                                @foreach ($rows['data'] as $row)
                                    <tr>
                                        <td>
                                            <a href="" class="text-primary-800 mb-1 d-block" target="_blank">
                                                {{ $row['month'] }}
                                            </a>
                                        </td>
                                        <td>
                                            @if ($row['gstr_1_filed'])
                                                <div class="btn-group w-100">
                                                    <button class=" btn-sucess-border form-select w-100 dropdown-toggle"
                                                        type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                                        Filed
                                                    </button>
                                                    <ul class="dropdown-menu drop-down-show-left">
                                                        <li class="px-5 mb-2">
                                                            <p class="mb-1">Filed on
                                                                {{ Illuminate\Support\Carbon::parse($row['gstr_1_filed_date'])->isoFormat('Do MMM YYYY') }}
                                                            </p>
                                                            <p class="mb-1">ARN: {{ $row['gstr_1_filed_arn'] }}</p>
                                                        </li>
                                                        <li>
                                                            <hr class="dropdown-divider">
                                                        </li>
                                                        <li><a class="dropdown-item py-2 view-summary"
                                                                href="javascript:void(0);"
                                                                data-id="{{ $row['id'] }}">View Summary</a></li>
                                                        <li><a class="dropdown-item py-2 download-pdf-gstr1" data-id="{{ $row['id'] }}" href="{{ route('company.download-filed-gstr1-report', ['id' => $row['id'], 'type' => 'pdf']) }}">Download PDF
                                                                Summary</a></li>
                                                        <li><a class="dropdown-item py-2 download-excel-gstr1" data-id="{{ $row['id'] }}" href="{{ route('company.download-filed-gstr1-report', ['id' => $row['id'], 'type' => 'excel']) }}">Download Excel
                                                                Summary</a></li>

                                                    </ul>
                                                </div>
                                            @else
                                                <a class="btn btn-danger-border w-100 fs-12" style="font-size: 12px; padding-left: 0.8rem !important; ">Not Prepare</a>
                                            @endif
                                        </td>
                                        <td>
                                            @if ($row['gstr_3b_filed'])
                                                <div class="btn-group w-100">
                                                    <button class=" btn-sucess-border form-select w-100 dropdown-toggle"
                                                        type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                                        Filed
                                                    </button>
                                                    <ul class="dropdown-menu drop-down-show-left" style="z-index: 1050;">
                                                        <li class="px-5 mb-2">
                                                            <p class="mb-1">Filed on
                                                                {{ Illuminate\Support\Carbon::parse($row['gstr_3b_filed_date'])->isoFormat('Do MMM YYYY') }}
                                                            </p>
                                                            <p class="mb-1">ARN: {{ $row['gstr_3b_filed_arn'] }}</p>
                                                        </li>
                                                        <li>
                                                            <hr class="dropdown-divider">
                                                        </li>
                                                        <li><a class="dropdown-item py-2 view-gstr3b-summary"
                                                                href="javascript:void(0);" data-id="{{ $row['id'] }}" >View Summary</a></li>
                                                        <li><a class="dropdown-item py-2" data-id="{{ $row['id'] }}" href="{{ route('company.download-filed-gstr3b-report', ['id' => $row['id'], 'type' => 'pdf']) }}">Download PDF
                                                                Summary</a></li>
                                                        <li><a class="dropdown-item py-2" data-id="{{ $row['id'] }}" href="{{ route('company.download-filed-gstr3b-report', ['id' => $row['id'], 'type' => 'excel']) }}">Download Excel
                                                                Summary</a></li>

                                                    </ul>
                                                </div>
                                            @else
                                                <a class="btn btn-danger-border w-100" style="font-size: 12px; padding-left: 0.8rem !important;">Not Prepare</a>
                                            @endif
                                        </td>
                                        <td>{{ isset($row['sales']) ? getCurrencyFormat($row['sales']) : 'Not Filed' }}
                                        </td>
                                        <td class="">
                                            {{ isset($row['gstr_1_sum']) ? getCurrencyFormat($row['gstr_1_sum']) : 'Not Filed' }}
                                        </td>
                                        <td>{{ isset($row['gstr_3b_sum']) ? getCurrencyFormat($row['gstr_3b_sum']) : 'Not Filed' }}
                                        </td>
                                        <td>{{ isset($row['itc']) ? getCurrencyFormat($row['itc']) : 'Not Filed' }}
                                        </td>
                                        <td>{{ isset($row['itc_gstr_2a_sum']) ? getCurrencyFormat($row['itc_gstr_2a_sum']) : 'Not Filed' }}
                                        </td>
                                        <td>{{ isset($row['itc_gstr_3b_sum']) ? getCurrencyFormat($row['itc_gstr_3b_sum']) : 'Not Filed' }}
                                        </td>
                                    </tr>
                                @endforeach
                                <tr>
                                    @include('livewire.custom.footer_total', ['rows' => $rows])
                                </tr>
                            </tbody>

                        </table>
                    </div>
                </div>
            </div>

        </div>
    </div>
</div>
