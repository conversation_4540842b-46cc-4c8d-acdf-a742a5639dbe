@php
    use App\Models\LockTransaction;
@endphp
<div>
    <div wire:loading.class="page-loader">
    </div>
    <div class="d-flex flex-wrap align-items-center w-auto justify-content-end" id="ledgerReportIndex">
        @if($rows['reportType'] == 'ledger')
        <div class="d-flex custom-align-block-table mx-1">
            <div class="d-flex justify-content-end">
                <div class="form-check d-flex align-items-center mb-0 mt-2">
                    {{ Form::radio('ledger_config_wise',1, true, ['class' => 'form-check-input ledger-config-wise', 'id'
                    => 'ledgerSummeryConfigWise']) }}
                    <label class="form-label mb-0 ms-2" for="ledgerSummeryConfigWise">Summary</label>
                </div>
                <div class="form-check d-flex align-items-center mx-2 mb-0 mt-2">
                    {{ Form::radio('ledger_config_wise',2, false, ['class' => 'form-check-input ledger-config-wise',
                    'id' => 'ledgerDetailConfigWise']) }}
                    <label class="form-label mb-0 ms-2" for="ledgerDetailConfigWise">Detail</label>
                </div>
            </div>
        </div>
        @endif
        <div class="d-flex custom-align-block-table mx-1">
            <div class="form-check form-switch form-check-custom mt-1">
                {{ Form::label('show_narration_sale_report', 'Show Narration:', ['class' => 'form-label fs-6 fw-bolder
                text-gray-900 mb-0']) }}
                <div class="form-check form-check-custom mx-2">
                    {{ Form::checkbox('show_narration_sale_report', 1, null , ['class' => 'form-check-input
                    show-narration-ledger-report','data-bs-toggle'=>"tooltip", 'data-bs-placement'=>"bottom",
                    'title'=>"Shortcut Key : Shift + N"]) }}
                </div>
            </div>
        </div>
        @if($rows['reportType'] == 'ledger')
        <div class="d-flex custom-align-block-table mx-1">
            <div class="d-flex justify-content-sm-end">
                <div class="form-check d-flex align-items-center mb-0 mt-2">
                    {{ Form::radio('ledger_report_option',1, ($rows['filterType'] == 1 ), ['class' => 'form-check-input
                    ledger-report-filter', 'id' => 'allLedgerReportOption',($rows['filterType'] == 1) ? 'checked' : ''])
                    }}
                    <label class="form-label mb-0 ms-2" for="allLedgerReportOption">All</label>
                </div>
                <div class="form-check d-flex align-items-center mx-2 mb-0 mt-2">
                    {{ Form::radio('ledger_report_option',2, ($rows['filterType'] == 2), ['class' => 'form-check-input
                    ledger-report-filter', 'id' => 'groupLedgerReportOption',($rows['filterType'] == 2) ? 'checked' :
                    '']) }}
                    <label class="form-label mb-0 ms-2" for="groupLedgerReportOption">Groups</label>
                </div>
                <div class="form-check d-flex align-items-center mx-2 mb-0 mt-2">
                    {{ Form::radio('ledger_report_option',3, ($rows['filterType'] == 3|| $rows['filterType'] == ''),
                    ['class' => 'form-check-input ledger-report-filter', 'id' =>
                    'ledgersReportOption',($rows['filterType'] == 3|| $rows['filterType'] == '') ? 'checked' : '']) }}
                    <label class="form-label mb-0 ms-2" for="ledgersReportOption">Ledgers</label>
                </div>
            </div>
        </div>
        <div
            class="d-flex justify-content-end me-2 group-filter {{ ($rows['filterType'] == ''|| $rows['filterType'] == 1 || $rows['filterType'] == 3)  ? 'd-none' : ''}}">
            <select class="form-select form-select-multiple print-pdf-ledger-id" id='ledgerGroupReportLists'
                data-control='select2' multiple="multiple">
                <option value="0" {{ in_array(0 ,$rows['groupId']) || in_array('' ,$rows['groupId'])
                    ? 'default selected' : '' }}>
                    Select Groups
                </option>
                @foreach(getGroupLists() as $key => $value)
                <option value="{{ $key }}" {{ in_array($key ,$rows['groupId']) ? 'selected' : '' }}>{{ $value }}
                </option>
                @endforeach
            </select>
        </div>
        <div
            class="d-flex justify-content-end me-2 ledger-filter {{ ($rows['filterType'] == 1 || $rows['filterType'] == 2)  ? 'd-none' : ''}}">
            <select class="form-select form-select-multiple print-pdf-ledger-id" id='ledgerReportLists'
                data-control='select2' multiple="multiple">
                <option value="0" {{ in_array(0 ,$rows['ledgerId']) || in_array('' ,$rows['ledgerId'])
                    ? 'default selected' : '' }}>
                    Select Ledgers
                </option>
                @foreach(getAllLedgerLists() as $key => $value)
                <option value="{{ $key }}" {{ in_array($key ,$rows['ledgerId']) ? 'selected' : '' }}>{{ $value }}
                </option>
                @endforeach
            </select>
        </div>
        @else

        <div class="d-flex justify-content-end me-2">
            <select class="form-select form-select-multiple print-pdf-ledger-id" id='ledgerReportLists'
                data-control='select2' multiple="multiple">
                <option value="0" {{ in_array(0 ,$rows['ledgerId']) || in_array('' ,$rows['ledgerId'])
                    ? 'default selected' : '' }}>
                    Select Ledgers
                </option>

                @foreach(getBandAndCashLedgers() as $key => $value)
                <option value="{{ $key }}" {{ in_array($key ,$rows['ledgerId']) ? 'selected' : '' }}>{{ $value }}
                </option>
                @endforeach
            </select>
        </div>
        @endif
        <div class="d-flex custom-align-block-table date-input-div mx-1">
            <input name="date_range" class="form-control" placeholder="dd-mm-yyyy - dd-mm-yyyy" value="{{ \Carbon\Carbon::parse($rows['start_date'])->format('d-m-Y').' - '.
                           \Carbon\Carbon::parse($rows['end_date'])->format('d-m-Y') }}"
                id="ledgerReportDateRangeFilter" />
        </div>

        @if (getLoginUser()->can('company_import_export_ledger_report') ||
        getLoginUser()->can('company_import_export_cash_bank_report'))
        <div class="d-flex custom-align-block-table">
            <div class="file-download-section d-flex justify-content-end ">
                <div class="d-flex justify-content-center align-items-center pdf-icon mt-0">
                    <form action="{{ route('company.ledger-report-pdf') }}" id="downloadLedgerReportPDFForm"
                        method="post" data-turbo="false" target="_blank">
                        @csrf
                        <input type="hidden" class="ledger-report-start-date" name="start_date"
                            value="{{ $rows['start_date'] }}">
                        <input type="hidden" class="ledger-report-end-date" name="end_date"
                            value="{{ $rows['end_date'] }}">
                        <input type="hidden" class="ledger-id" name="ledger_id"
                            value="{{ implode(',' , $rows['ledgerId']) }}">
                        <input type="hidden" class="group-id" name="group_id"
                            value="{{ implode(',' , $rows['groupId']) }}">
                        <input type="hidden" class="shortcut-ledger-pdf-export">
                        <input type="hidden" class="filter-type" name="filter_type" value="{{ $rows['filterType'] }}">
                        <input type="hidden" class="check-narration" name="check_narration"
                            value="{{ $rows['checkNarration'] }}">
                        <input type="hidden" name="config" value="{{ $rows['config'] }}">
                        <input type="hidden" name="report_type"
                            value="{{ $rows['reportType'] == 'ledger' ? 'ledger' : 'cashBank' }}">
                        <button type="submit" id="downloadLedgerReportPDFButton"
                            class="text-hover-gray pe-0  border-0 bg-transparent " disabled data-bs-toggle="tooltip"
                            data-bs-placement="bottom" title="PDF Export, Shortcut Key : Alt + D">
                            <i class="far fa-file-pdf text-gray fs-1 text-orange"></i>
                        </button>
                    </form>
                </div>
                <div class="d-flex justify-content-center align-items-center excel-icon mt-0">
                    <form action="{{ route('company.ledger-report-export-excel') }}" id="downloadLedgerReportExcelForm"
                        method="post" data-turbo="false" target="blank">
                        @csrf
                        <input type="hidden" class="ledger-report-start-date" name="start_date"
                            value="{{ $rows['start_date'] }}">
                        <input type="hidden" class="ledger-report-end-date" name="end_date"
                            value="{{ $rows['end_date'] }}">
                        <input type="hidden" class="ledger-id" name="ledger_id"
                            value="{{ implode(',' , $rows['ledgerId']) }}">
                        <input type="hidden" class="group-id" name="group_id"
                            value="{{ implode(',' , $rows['groupId']) }}">
                        <input type="hidden" class="filter-type" name="filter_type" value="{{ $rows['filterType'] }}">
                        <input type="hidden" class="shortcut-ledger-excel-export">
                        <input type="hidden" class="check-narration" name="check_narration"
                            value="{{ $rows['checkNarration'] }}">
                        <input type="hidden" name="config" value="{{ $rows['config'] }}">
                        <input type="hidden" name="report_type"
                            value="{{ $rows['reportType'] == 'ledger' ? 'ledger' : 'cashBank' }}">
                        <button type="submit" id="downloadLedgerReportExcelButton"
                            class="text-hover-gray me-2 border-0 bg-transparent " disabled data-bs-toggle="tooltip"
                            data-bs-placement="bottom" title="Excel Export, Shortcut Key : Alt + E">
                            <i class="far fa-file-excel text-success fs-1"></i>
                        </button>
                    </form>
                </div>
            </div>
        </div>
        @endif
    </div>
    <div class="row d-flex border-0 pt-4 justify-content-end">
        <div class="card card-border-1 p-0">
            <div class="card-body pt-0 pb-4 px-0" style="max-height: 650px;
            overflow-y: hidden;">
                <div class="px-4 custom-livewire-table" style="max-height: 650px; overflow-y: auto;">
                    <table class="table table-responsive-sm text-nowrap" id="ledgerReportDatatable">
                        <thead class="fixed-header">
                            <tr>
                                @include('livewire.ledger-report-table-header', ['rows' => $rows])
                            </tr>
                        </thead>
                        {{--
                    </table> --}}
                    {{--
                </div>
                <div class="flipped">
                    <div class=" px-4 custom-livewire-table fixTableHead">
                        <table class="table table-responsive-sm text-nowrap flipped-table" id="ledgerReportDatatable">
                            --}}
                            <tbody>

                                @foreach($rows['data'] as $row)
                                @php
                                $ledgerReportBalanceType = $row['closing_balance']['balanceType'] ?? null;
                                $ledgerReportBalance = $row['closing_balance']['balance'] ?? 0;
                                $ledgerDetails = $row['data'] ?? [];
                                @endphp
                                @if(count($row['closing_balance'] ?? []))

                                    @if(count($row['closing_balance'] ?? []))
                                        <tr>
                                            <td style="border-bottom: 1px solid #4f158c" class="text-center" colspan="8">
                                                <b>{{ $row['ledgerName'] ?? '' }}</b>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td></td>
                                            <td><b>Opening Balance</b></td>
                                            <td></td>
                                            {{-- @if($rows['checkNarration'])
                                            <td></td>
                                            @endif --}}
                                            <td></td>
                                            {{-- @if($rows['reportType'] != 'cashBank')
                                            <td></td>
                                            @endif --}}
                                            <td></td>
                                            <td></td>
                                            @php
                                                $debitAndCreditType =
                                                \App\Models\Ledger::OPENING_BALANCE_DR_CR[$ledgerReportBalanceType];
                                                $balance = getCurrencySymbol().getCurrencyFormat($ledgerReportBalance).' '.$debitAndCreditType
                                            @endphp
                                            <td><b>{{ $balance }}</b></td>
                                            <td></td>
                                        </tr>
                                    @endif

                                @endif
                                    @php
                                        $debitAmountTotal = 0;
                                        $creditAmountTotal = 0;
                                    @endphp

                                @foreach($ledgerDetails as $row)
                                <tr class="align-top">
                                    <td>{{ $row['date'] }}</td>
                                    @if($rows['config'] == 1 )
                                        <td>
                                            {{ $row['ledger_name'] }}
                                            @if($rows['checkNarration'] && !empty($row['narration']))
                                            <br>
                                            <p class="text-primary fst-italic fs-12" style="text-wrap: wrap">( {!! $row['narration'] !!})</p>
                                            @endif
                                        </td>
                                    @else
                                        <td>
                                            @if(isset($row['tcs_amount']))
                                                <div>
                                                    @foreach($row['ledger_list'] as $ledgerItem)
                                                    <span>{{ $ledgerItem['name'] }} (Taxable Val)
                                                        :{{getCurrencySymbol().getCurrencyFormat($ledgerItem['amount']) }}</span>
                                                    <br>
                                                    @endforeach
                                                    <span>TCS Amount :{{ getCurrencySymbol().getCurrencyFormat($row['tcs_amount']) }}</span><br>
                                                    @if(isCompanyGstApplicable())
                                                    <span>CGST :{{ getCurrencySymbol().getCurrencyFormat($row['cgst']) }}</span>
                                                    <br>
                                                    <span>SGST:{{ getCurrencySymbol().getCurrencyFormat($row['sgst']) }}</span>
                                                    <br>
                                                    <span>IGST:{{ getCurrencySymbol().getCurrencyFormat($row['igst']) }}</span>
                                                    <br>
                                                    <span>Cess:{{ getCurrencySymbol().getCurrencyFormat($row['cess']) }}</span>
                                                    <br>
                                                    @foreach ($row['additional_charges_addless'] as $additionalChargeAndAddless)
                                                        <span>{{ $additionalChargeAndAddless['name'] }}:{{ getCurrencySymbol().getCurrencyFormat($additionalChargeAndAddless['amount']) }}</span>
                                                        <br>
                                                    @endforeach
                                                    @endif
                                                    <span>Round off:{{ getCurrencySymbol().getCurrencyFormat($row['rounding_amount']) }}</span><br>
                                                </div>
                                            @else
                                                {{ $row['ledger_name'] }}
                                            @endif
                                            @if($rows['checkNarration'] && !empty($row['narration']))
                                                <br>
                                                <p class="text-primary fst-italic fs-12" style="text-wrap: wrap">( {!! $row['narration'] !!})</p>
                                            @endif
                                            @if(!empty($row['settle_invoice_no']) && is_array($row['settle_invoice_no']))
                                                <div class="text-primary fst-italic fs-12 text-wrap">
                                                    @foreach($row['settle_invoice_no'] as $type => $invoices)
                                                        <div>{{ $type }}: {{ implode(', ', $invoices) }}</div>
                                                    @endforeach
                                                </div>
                                            @endif
                                            @if (!empty($row['transaction_item_list']))
                                                <div class="text-primary fs-12">
                                                    <div>Transaction Items:<br>{!! $row['transaction_item_list'] !!}</div>
                                                </div>
                                            @endif
                                        </td>
                                    @endif
                                    <td>{{ $row['transaction_type'] }}</td>
                                    {{-- @if($rows['checkNarration'])
                                    <td>
                                        <a href="javascript:void(0)" class="text-dark" data-bs-toggle="tooltip"
                                            data-bs-placement="top" title="{{ $row['narration'] ?? null }}">{!!
                                            !empty($row['narration']) ? substr($row['narration'],0, 40).'...' : ''
                                            !!}</a>
                                    </td>
                                    @endif --}}
                                    @php
                                        preg_match('/^\s+/', $row['voucher_no'], $matches);
                                        $leadingSpacesCount = isset($matches[0]) ? strlen($matches[0]) : 0;
                                    @endphp
                                    <td>
                                        @if ($row['is_locked'])
                                            <a class="open-lock-transaction-alert cursor-pointer" data-lock-date="{{ $row['lock_date'] }}">
                                                {{ $row['transaction_type'] == 'Purchase' ? ($row['invoice_no'] ?? $row['voucher_no']) : $row['voucher_no'] }}
                                            </a>
                                        @else
                                            <a href="{{ route('company.check-transaction-type', ['voucher_no' => $row['voucher_no'],'transaction_type' => $row['transaction_type'], 'space_count' => $leadingSpacesCount]) }}"
                                                target="_blank">
                                                {{ $row['transaction_type'] == 'Purchase' ? ($row['invoice_no'] ?? $row['voucher_no']) : $row['voucher_no'] }}
                                            </a>
                                        @endif
                                    </td>
                                    {{-- @if($rows['reportType'] != 'cashBank')
                                    <td>{{ $row['invoice_no'] }}</td>
                                    @endif --}}
                                    <td>{{ getCurrencySymbol().getCurrencyFormat($row['debit_amount']) }}</td>
                                    <td>{{ getCurrencySymbol().getCurrencyFormat($row['credit_amount']) }}</td>

                                    @php
                                        $debitAmountTotal += $row['debit_amount'];
                                        $creditAmountTotal += $row['credit_amount'];

                                        if ($ledgerReportBalanceType == 'undefined' || $ledgerReportBalanceType == null) {
                                            if ($row['credit_amount'] > 0) {
                                                $ledgerReportBalanceType = 2;
                                            } else {
                                                $ledgerReportBalanceType = 1;
                                            }
                                        }
                                        if ($row['credit_amount'] != 0) {
                                            if ($ledgerReportBalanceType == 2) {
                                                $ledgerReportBalance += $row['credit_amount'];
                                            } else {
                                                $ledgerReportBalance -= $row['credit_amount'];
                                            }
                                        } else {
                                            if ($ledgerReportBalanceType == 1) {
                                                $ledgerReportBalance += $row['debit_amount'];
                                            } else {
                                                $ledgerReportBalance -= $row['debit_amount'];
                                            }
                                        }
                                        if ($ledgerReportBalance < 0) {
                                            if ($ledgerReportBalanceType==1) {
                                                $ledgerReportBalanceType=2;
                                            } else if ($ledgerReportBalanceType==2) {
                                                $ledgerReportBalanceType=1;
                                            }
                                        }
                                            $debitAndCreditType=\App\Models\Ledger::OPENING_BALANCE_DR_CR[$ledgerReportBalanceType];
                                            $ledgerReportBalance=round(abs($ledgerReportBalance),
                                            getCompanyFixedDigitNumber() ?? 2);
                                            $balance=getCurrencySymbol().getCurrencyFormat($ledgerReportBalance).' '.$debitAndCreditType
                                    @endphp
                                    <td>{{ $balance }}</td>
                                    <td>
                                        @if ($row['is_locked'])
                                        <a href="javascript:void(0)" class="text-hover-primary me-1 edit-btn open-lock-transaction-alert"
                                            data-bs-toggle="tooltip" title="Edit" data-lock-date="{{ $row['lock_date'] }}">
                                            <i class="fas fa-edit text-primary"></i>
                                        </a>
                                        <a href="javascript:void(0)" data-lock-date="{{ $row['lock_date'] }}"
                                            class="open-lock-transaction-alert text-hover-danger me-1" data-bs-toggle="tooltip">
                                            <i class="fas fa-trash text-danger"></i>
                                        </a>
                                        @else
                                        <a href="{{ route('company.check-transaction-type', ['voucher_no'=> $row['voucher_no'],
                                            'transaction_type' => $row['transaction_type'], 'space_count' => $leadingSpacesCount]) }}"
                                            class="text-hover-primary me-1" data-bs-toggle="tooltip" title="Edit">
                                            <i class="fas fa-edit text-primary"></i>
                                        </a>
                                        <a href="javascript:void(0)" data-voucher-no="{{ $row['voucher_no'] }}"
                                            data-transaction-type="{{ $row['transaction_type'] }}"
                                            class="text-hover-danger me-1 delete-ledger-transition"
                                            data-bs-toggle="tooltip" title="Delete">
                                            <i class="fas fa-trash text-danger"></i>
                                        </a>
                                        @endif
                                    </td>
                                </tr>
                                @endforeach
                                @if(isset($balance) && !empty($balance))
                                <tr>
                                    <td style="border-bottom: 1px solid #4f158c"></td>
                                    <td style="border-bottom: 1px solid #4f158c"><b>Closing Balance</b></td>
                                    <td style="border-bottom: 1px solid #4f158c"></td>
                                    {{-- @if($rows['checkNarration'])
                                    <td style="border-bottom: 1px solid #4f158c"></td>
                                    @endif --}}
                                    <td style="border-bottom: 1px solid #4f158c"></td>
                                    {{-- @if($rows['reportType'] != 'cashBank')
                                    <td style="border-bottom: 1px solid #4f158c"></td>
                                    @endif --}}
                                    <td style="border-bottom: 1px solid #4f158c">
                                        <b>{{ getCurrencySymbol().getCurrencyFormat($debitAmountTotal) }}</b>
                                    </td>
                                    <td style="border-bottom: 1px solid #4f158c">
                                        <b>{{ getCurrencySymbol().getCurrencyFormat($creditAmountTotal) }}</b>
                                    </td>
                                    <td style="border-bottom: 1px solid #4f158c"><b>{{$balance}}</b></td>
                                    <td style="border-bottom: 1px solid #4f158c"></td>
                                </tr>
                                @endif
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
