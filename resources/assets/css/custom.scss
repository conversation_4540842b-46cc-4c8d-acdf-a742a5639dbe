*:not(i) {
    font-family: "Inter", sans-serif !important;
}
.text-gray-100 {
    color: #747685 !important;
}
.amt {
    font-family: "Mulish", sans-serif !important;
    word-break: break-word;
}
.custom-amt-fs {
    font-size: 14px;
}
.w-100vw {
    width: 100% !important;
    @media (min-width: 1199px) and (max-width: 1399px) {
        width: 90vw !important;
    }
    @media (min-width: 1099px) and (max-width: 1199px) {
        width: 100vw !important;
    }
    @media (min-width: 992px) and (max-width: 1099px) {
        width: 110vw !important;
    }
}

.border-left-lg-gray-400 {
    border-left: 1px solid #dde0e4 !important;
    @media (max-width: 991px) {
        border-left: 0px solid #dde0e4 !important;
    }
}
.border-left-gray-400 {
    border-left: 1px solid #dde0e4;
}
.border-right-gray-400 {
    border-right: 1px solid #dde0e4 !important;
}
.border-bottom-gray-400 {
    border-bottom: 1px solid #dde0e4;
}
.border-top-gray-400 {
    border-top: 1px solid #dde0e4;
}
.border-top-lg-0 {
    border-top: 0px solid #dde0e4;
    @media (max-width: 991px) {
        border-top: 1px solid #dde0e4;
    }
}
.fw-bolder,
th {
    font-weight: 600 !important;
}
.currency-icon:before {
    content: "₹";
}

.object-fit-cover {
    object-fit: cover !important;
}

.header-fixed.toolbar-fixed .wrapper {
    padding-top: calc(65px);

    @media (max-width: 991.98px) {
        padding-top: 85px;
    }
}
.align-items-end {
    align-items: flex-end !important;
}
.me-55 {
    margin-right: 55px;
}
.object-fit-contain {
    object-fit: contain !important;
}
.custom-fs-20 {
    font-size: 20px !important;
    @media (max-width:768px) {
        font-size: 18px !important;
    }
}

@media (min-width: 1500px) {
    .width-5 {
        width: 5%;
    }
}

@media (min-width: 1178px) and (max-width: 1422px) {
    .width-5 {
        width: 7%;
    }
}

.border-radius-15 {
    border-radius: 15px;
}

.fs-30 {
    font-size: 30px;
}

@media (max-width: 1366px) {
    .pdf-icon,
    .custom-align-block {
        display: block !important;
    }
    .pdf-icon,
    .excel-icon {
        margin-top: 10px;
    }
}

.file-upload-wrapper {
    $defaultColor: #4f158c;
    $height: 40px;

    position: relative;
    width: 100%;
    height: $height;
    padding: 0.4rem 0.8rem;
    border: 1px solid #6f6f6f;
    border-radius: 0.475rem;

    &:after {
        content: attr(data-text);
        font-size: 14px;
        position: absolute;
        top: 0;
        left: 0;
        background: #fff;
        padding: 10px 15px;
        display: block;
        width: calc(100% - 40px);
        pointer-events: none;
        z-index: 20;
        height: $height - 20px;
        line-height: $height - 20px;
        color: #999;
        border-radius: 5px 10px 10px 5px;
        font-weight: 300;
    }

    &:before {
        content: "Browse";
        position: absolute;
        top: 0;
        right: 0;
        display: inline-block;
        height: 39px;
        background: $defaultColor;
        color: #fff;
        font-weight: 700;
        z-index: 25;
        font-size: 12px;
        line-height: $height;
        padding: 0 15px;
        text-transform: uppercase;
        pointer-events: none;
        border-radius: 0 5px 5px 0;
    }

    &:hover {
        &:before {
            background: darken($defaultColor, 10%);
        }
    }

    input {
        opacity: 0;
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
        z-index: 99;
        height: $height - 20px;
        margin: 0;
        padding: 0;
        display: block;
        cursor: pointer;
        width: 100%;
    }
}

.m-r-495 {
    margin-right: 495px !important;
}

.view-close-btn {
    padding: 5px 26px !important;
    border-radius: 20px !important;
}

.swal2-html-container {
    font-weight: unset !important;
}

.select-filter {
    width: 250px;
    padding: 0.75rem 3rem 0.75rem 1rem !important;

    @media (max-width: 767px) {
        width: 100%;
        margin-bottom: 10px !important;
    }
}

.btn:not(.btn-shadow):not(.shadow):not(.shadow-sm):not(.shadow-lg) {
    &.btn-primary:focus {
        box-shadow: rgb(247 101 0 / 24%) 0 2px 2px 0,
            rgb(247 101 0 / 64%) 0 2px 16px 0 !important;
    }

    &.btn-light:focus {
        box-shadow: rgb(247 101 0 / 24%) 0 2px 2px 0,
            rgb(247 101 0 / 64%) 0 2px 16px 0 !important;
    }

    &.btn-orange:focus {
        box-shadow: rgb(79 21 140 / 24%) 0 2px 2px 0,
            rgb(79 21 140 / 64%) 0 2px 16px 0 !important;
    }
}

.items-details-card {
    overflow-x: auto;

    .item-container {
        .item-ledger-details {
            min-width: 1185px;
        }
    }
}

.card-border-1 {
    border: 1px solid rgb(79 21 140 / 50%);
    border-radius: 0.625rem;
}

input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
    -webkit-appearance: none;
}

.tax-table {
    table,
    th,
    td {
        border: 1px solid black;
        border-collapse: collapse;
    }
}

.text-orange {
    color: #f76600;
}

.custom-font-icon-size {
    font-size: 2.75rem;
}

.gstr-3b-3-1-summary {
    border: 1px solid black;
    border-radius: 10px;
    table {
        thead {
            tr {
                background-color: #4F158C !important;
                color: #ffffff !important;

                td {
                    color: #ffffff !important;
                    font-weight: 500;

                    &:first-child {
                        padding-left: 9.75px;
                    }
                }
            }
        }

        tbody {
            tr:first-child {
                background-color: #4f158c;

                td {
                    color: #ffffff !important;

                    &:first-child {
                        padding-left: 9.75px;
                    }
                }
            }

            tr {
                td {
                    &:first-child {
                        padding-left: 9.75px;
                    }
                }
            }
        }

        tfoot {
            tr {
                background-color: #e6e6e6;

                td {
                    font-weight: 500;

                    &:first-child {
                        padding-left: 9.75px;
                        border-bottom-left-radius: 0.625rem;
                    }

                    &:last-child {
                        border-bottom-right-radius: 0.625rem;
                    }
                }
            }
        }

        .disabled-column-row {
            background-color: #f7660029;
        }

        .border-right-primary {
            border-right: 1px solid #4f158c;
        }

        .border-top-primary {
            border-top: 1px solid #4f158c;
        }

        .border-bottom-primary {
            border-bottom: 1px solid #4f158c;
        }

        .text-right {
            align-items: end;
        }
        .fs-13 {
            font-size: 13px !important;
        }
    }
}

#loader {
    position: absolute;
    top: 50%;
    height: 100%;
    width: 100%;
    z-index: 9999999;
}

@media (min-width: 992px) {
    .table-responsive::-webkit-scrollbar-thumb {
        background-color: black !important;
    }
    .table-responsive:hover::-webkit-scrollbar-thumb {
        background-color: black !important;
    }
}

.bs-tooltip-bottom .tooltip-inner {
    border: 1px solid black !important;
    font-weight: 600 !important;
}
.bs-tooltip-start .tooltip-inner {
    border: 0.3px solid rgba(118, 118, 118, 0.3) !important;
    font-weight: 400 !important;
}
.bs-tooltip-top .tooltip-inner {
    border: 0.3px solid rgba(118, 118, 118, 0.3) !important;
    font-weight: 400 !important;
}
.bs-tooltip-start .tooltip-arrow {
    display: none !important;
}
.bs-tooltip-top .tooltip-arrow {
    display: none !important;
}
.bs-tooltip-bottom {
    .tooltip-arrow {
        transform: translate3d(64px, 4px, 0px) !important;
    }
}

.tooltip {
    z-index: 9999999999;
}

//.custom-icon{
//    position: absolute;
//    left: -37px;
//    z-index: 999;
//    width: 40px;
//}

.custom-select2-width {
    width: calc(100% - 38px);
    @media (max-width: 768px) {
        width: calc(100% - 36px);
    }
}

.custom-group-text {
    border: 1px solid #6f6f6f !important;
    padding: 0.4rem 1rem !important;
    position: absolute !important;
    right: 2px !important;
    height: 100% !important;
}
.custom-group-text-inr {
    border: 1px solid #6f6f6f !important;
    padding: 0.4rem 1rem !important;
    position: absolute !important;
    left: 2px !important;
    height: 100% !important;
}
.custom-filter-width {
    //width:175px!important;
    width: 238px !important;
    @media (max-width: 1499px) {
        width: 220px !important;
    }
    @media (max-width: 1299px) {
        width: 200px !important;
    }
    @media (max-width: 575px) {
        width: 180px !important;
    }
    @media (max-width: 469px) {
        margin-top: 10px;
    }
}

#addLedgerModal,
#addPurchaseLedgerModal {
    .select2-container {
        .select2-dropdown {
            left: unset;
            position: fixed;
        }
    }
}

#expenseAccordion,
#incomeAccordion {
    .accordion-item {
        border: none;

        &:first-of-type .accordion-button {
            border: none;
        }
    }

    .accordion-button {
        padding: 10px;
        background-color: #fff;

        &::after {
            content: none;
        }
    }

    .accordion-body {
        padding: 10px;
    }

    h1,
    h2,
    h3,
    h4,
    h5,
    h5,
    p {
        margin: 0;
    }
}

.select2-container--bootstrap5
    .select2-dropdown
    .select2-results__option.select2-results__option--selected {
    background-position: center right 0.35rem !important;
}

.custom-padding-start {
    padding-left: 12rem !important;
}

#capitalAccordionSection,
#loanAccordionSection,
#reserveAndSurplusAccordionSection,
#currentLiabilitiesAccordionSection,
#fixedAssetAccordionSection,
#investmentAccordionSection,
#loanAndAdvanceAccordionSection,
#currentAssetAccordionSection,
#profitLossAccordionSection,
.accordion-report {
    .accordion-item {
        border: none;

        &:first-of-type .accordion-button {
            border: none;
        }
    }

    .accordion-button {
        padding: 10px;
        background-color: #fff;

        &::after {
            content: none;
        }
    }

    .accordion-body {
        padding: 10px;
    }

    h1,
    h2,
    h3,
    h4,
    h5,
    h5,
    p {
        margin: 0;
    }
}

.dashboard-card {
    display: flex;
    background-color: #ffffff;
    padding: 15px;
    border-radius: 10px;

    h4 {
        font-size: 22px;
        font-weight: 600;
        margin-bottom: 0;
    }

    .tag {
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 14px 18px;
        background-color: rgb(247 102 0 / 13%);
        font-weight: 600;
        font-size: 20px;
        color: #f76600;
        border-radius: 10px;
    }
}
.custom-table.gy-5 th,
.custom-table.gy-5 td {
    padding-top: 12px !important;
    padding-bottom: 12px !important;
}
.custom-padding-table {
    tbody {
        display: block;
        max-height: 150px;
        //overflow-y: auto;
    }

    tr {
        display: table;
        width: 100%;
        table-layout: fixed;
    }
    tr td {
        padding: 10px 20px 10px 0px !important;
    }
    //td:last-child {
    //    padding-right : 10px !important;
    //}
}

.custom-popup-model-table-row-scroll-v {
    tbody {
        display: block;
        max-height: 500px;
        overflow-y: auto;
    }

    tr {
        display: table;
        width: 100%;
        table-layout: fixed;
    }
}

.flex-scroll {
    -ms-overflow-style: none;
    scrollbar-width: none;
    overflow-y: scroll;
}

.flex-scroll::-webkit-scrollbar {
    width: 5px;
    display: none !important;
}

.flex-scroll::-webkit-scrollbar-track {
    -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
    -webkit-border-radius: 10px;
    border-radius: 10px;
    display: none !important;
}

.flex-scroll::-webkit-scrollbar-thumb {
    -webkit-border-radius: 10px;
    border-radius: 10px;
    background: rgb(0 0 0 / 80%);
    -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.5);
    display: none !important;
}

.custom-dashboard-btn {
    width: 100%;
    border: 1px solid #4f158c !important;
    color: #fff !important;
    font-size: 13px;
    background-color: #4f158c !important;
    padding: 5.5px 5px 5.5px 5px !important;
    margin: 3px 0px 3px 0px !important;
}

.custom-add-report-btn {
    width: 100%;
    border: 1px dashed grey !important;
    color: grey !important;
    padding: 5.5px 5px 5.5px 5px !important;
    margin: 3px 0 3px 0 !important;
}
.company-report-button {
    position: relative;
}
.company-report-button:hover {
    .circle-minus-icon {
        opacity: 1;
        transition: all 0.5s;
    }
}
.circle-minus-icon {
    border-radius: 30px;
    position: absolute;
    background-color: white;
    top: 10px;
    right: -10px;
    opacity: 0;
    z-index: 1;
}

.company-dashboard-custom-height tbody {
    display: block;
    //max-height: 415px !important;
    max-height: 302px !important;
    overflow-y: auto;
}

.accordion-button:not(.collapsed) {
    box-shadow: none !important;
}

//.company-gst-report-dashboard-btn{
//    position: relative;
//}
.gst-dashboad-btn:hover {
    .company-gst-report-list {
        display: block;
        transition: all 0.5s;
        z-index: 1;
    }
}
.company-gst-report-list {
    position: absolute;
    background-color: white;
    top: 7px;
    display: none;
    //opacity:0;
    z-index: 0;
    right: 100%;
    width: 200px;
    border: 1px solid #4f158c;
    border-radius: 10px;
    //@media (max-width:1554px) {
    //    right: 190px;
    //}
    @media (max-width: 991px) {
        right: 145px;
    }
    @media (max-width: 575px) {
        right: 85px;
    }
}

//.company-tds-report-dashboard-btn{
//    position: relative;
//}
.company-tds-report-dashboard-btn:hover {
    .company-tds-report-list {
        display: block;
        transition: all 0.5s;
        z-index: 1;
    }
}
.company-tds-report-list {
    position: absolute;
    background-color: white;
    top: 7px;
    display: none;
    z-index: 0;
    right: 100%;
    width: 200px;
    border: 1px solid #4f158c;
    border-radius: 10px;
    @media (max-width: 991px) {
        right: 145px;
    }
    @media (max-width: 575px) {
        right: 85px;
    }
}
.br-10 {
    border-radius: 0.625rem;
}

.dashboard-shortcut-buttons {
    @media (max-width: 1199px) {
        display: none !important;
    }
}

.shortcut-aside-menu {
    display: none !important;
    @media (max-width: 1199px) {
        display: block !important;
    }
    .btn.btn-icon {
        @media (max-width: 1199px) {
            height: calc(3.5em + 1.5rem + 2px);
        }
        @media (max-width: 999px) {
            height: calc(2.5em + 1.5rem + 2px) !important;
        }
    }
}
.border-bottom-orange {
    border-bottom: 2px solid #f76600 !important;
}
.right-asidebar {
    @media (min-width: 1199px) {
        display: none;
        position: fixed !important;
        top: 0 !important;
        bottom: 0 !important;
        right: 0 !important;
        z-index: 101 !important;
        overflow: hidden;
    }
}
.mobile-shortcut-btn {
    @media (max-width: 1199px) {
        margin-right: 0 !important;
        padding: 10px 10px 0 10px;
        margin-bottom: 16px;
    }
    a {
        @media (max-width: 1199px) {
            width: 100%;
        }
    }
}

.company-dashboard-custom-table thead tr {
    background-color: #4f158c;
}

.company-dashboard-custom-table thead tr {
    background-color: #4f158c;
}

.company-dashboard-custom-table > .table thead tr th {
    color: white !important;
}

.company-dashboard-custom-table tr td,
.company-dashboard-custom-table tr th {
    border-right: 1px solid #6a6969;
    text-transform: capitalize;
}

.company-dashboard-custom-table tr td {
    padding-top: 4.65px !important;
    padding-bottom: 4.65px !important;
}

.company-dashboard-custom-table tr td:first-child,
.company-dashboard-custom-table tr th:first-child {
    padding-left: 20px !important;
}

.company-dashboard-custom-table td:last-child {
    border-right: none;
}

.company-dashboard-custom-table tr:nth-child(even) {
    background-color: #f5f8fa;
}

.border-orange {
    border: 1px solid #f76600;
}

.border-dashboard-card {
    border: 1px solid black;
}

.mb-0 {
    margin-bottom: 0px !important;
}

table.company-dashboard-custom-table {
    tbody tr td {
        //font-weight: 500;
    }
}
.company-dashboard-btn-bg {
    width: 45px;
    height: 45px;
    background-color: white;
    border-radius: 5px;
    position: fixed;
    top: 35%;
    right: 0;
    z-index: 9;
}
.company-dashboard-rp-shortcut {
    a {
        width: 32px;
        height: 32px;
        background-color: #eff2f5;
        border-radius: 5px;
        &:hover {
            background-color: #4f158c !important;

            i {
                color: #eff2f5 !important;
            }
        }
        i {
            font-size: 20px;
            padding-right: 0;
            color: #4f158c;
        }
    }
}

.offcanvas {
    height: 100%;
    //overflow-x: hidden;
    position: fixed;
    top: 0;
    right: 0;
    transition: transform 0.3s ease-in-out;
    width: 280px;
    z-index: 99999;
    background-color: white;
}

.report-shortcut-btn-width {
    font-size: 25px !important;
}

.offcanvas-backdrop.fade.show {
    pointer-events: none;
}

.transaction-shortcut-btn {
    background-color: #551a94 !important;
    color: #ffffff;
}

.transaction-shortcut-btn:hover {
    background-color: #f76600 !important;
    color: #ffffff;
}

.custom-dashboard-btn:hover {
    background-color: #f76600 !important;
    color: #ffffff !important;
}
//only for pdf preview
.company-logo-img {
    width: 140px;
    height: 60px;
}
.signature-img {
    width: 230px;
    height: 60px;
}
.vertical-align-bottom {
    vertical-align: bottom;
}

//end pdf preview
.mx-height-200px {
    max-height: 200px;
}

.custom-datepicker-width {
    width: 115px !important;
}

.pt-7-ps-15 {
    padding: 7px 15px !important;
}

.datepicker table tr td span.active.active {
    background-color: #4f158c !important;
    background-image: none !important;
}

.date-input {
    width: 90px;
}

.date-input:focus {
    outline: none !important;
}

.custom-monthpicker-width {
    width: 140px;
    padding: 0.25rem 0.8rem !important;
    height: 33px;
}

.comparative-month-filter-resetbtn {
    background-color: #e9eef1 !important;
    color: #7e8299 !important;
}

.trial-balance-footer {
    border: 1px solid #fd6502;
    padding: 5px 0;
    margin-top: 20px;
    border-radius: 4px;
}

.configuration-setting-btn {
    width: 35px;
    height: 35px;
    background-color: #eff2f5;
    border-radius: 50px;

    i {
        font-size: 17px;
        color: #4f158c;
    }
}

.configuration-button {
    top: 5px;
    right: 5px;

    .configuration-setting-btn:hover {
        background-color: #4f158c;

        i {
            color: #ffffff;
        }
    }
}

.toastr {
    opacity: 1 !important;
    .toastr-close-button {
        margin-left: 8px;
    }
    .toastr-message {
        font-weight: 500 !important;
    }
}

.border-bottom-black {
    border-bottom: 1px solid black !important;
}

.border-top-black {
    border-top: 1px solid black !important;
}

.sheet-time-table {
    border: 1px solid black;
    border-radius: 0.625rem;
    overflow: hidden;
}

.work-sheet-table tr td,
.work-sheet-table tr th {
    border-right: 1px solid #6a6969;
    text-transform: capitalize;
}

table.work-sheet-table tbody tr td {
    font-weight: 500;
}

.shortcut-key-tab {
    border: 1px solid black;
    border-radius: 10px;
    overflow: hidden;
}
.shortcut-key-tab table tbody td:last-child {
    white-space: nowrap;
    padding-right: 9px;
}
.date-input-div {
    min-width: 210px;
    @media (max-width: 767px) {
        min-width: 194px;
    }
}

.date-picker-input-div-width {
    max-width: 110px;
    @media (max-width: 767px) {
        width: 97px;
    }
}
.support-page-or-line {
    position: absolute;
    margin-top: -22px;
    background: #f5f8fa;
    right: 48% !important;
    padding: 0 5px;
    @media (min-width: 800px) {
        right: 40% !important;
    }
    @media (min-width: 1030px) {
        right: 42% !important;
    }
    @media (min-width: 1440px) {
        right: 44% !important;
    }
}
.date-range-picker-input-div-width {
    width: 207px;
    @media (max-width: 767px) {
        width: 194px;
    }
}

.bg-orange {
    background-color: #f76600 !important;
}

.work-management-table {
    .table tr td,
    .table tr th {
        padding: 4px 0.75rem !important;
    }

    .table tr:first-child,
    .table th:first-child,
    .table td:first-child {
        padding-left: 10px !important;
    }

    .table tr:last-child,
    .table th:last-child,
    .table td:last-child {
        padding-right: 10px !important;
    }

    tr td,
    tr th {
        border-right: 1px solid #6a6969;
        text-transform: capitalize;
    }

    td:last-child {
        border-right: none;
    }

    .table tfoot tr th,
    .table tfoot tr td,
    .table tbody tr th,
    .table tbody tr td {
        border-bottom: 1px solid #6a6969 !important;
    }

    .table tfoot tr:last-child th,
    .table tfoot tr:last-child td,
    .table tbody tr:last-child th,
    .table tbody tr:last-child td {
        border-bottom: 0 !important;
    }

    tr th {
        font-weight: 600 !important;
    }
}

.bg-yellow {
    background-color: yellow !important;
}

.week2-custom-width {
    width: 10.5% !important;
}

.week1-start-time,
.week1-end-time,
.week2-start-time,
.week2-end-time {
    max-width: 60px;
    width: 60px;
    @media (max-width: 575px) {
        width: auto;
        max-width: 60px;
    }
}

.custom-hour-width,
.custom-till-width,
.custom-from-width {
    @media (max-width: 496px) {
        width: 33.33%;
    }
}

.custom-till-width {
    @media (max-width: 496px) {
        text-align: end;
    }
}

.custom-hour-width {
    @media (max-width: 496px) {
        text-align: center;
        padding-left: 18px !important;
    }
}

.min-w-130 {
    min-width: 130px !important;
}

.verify-otp-heading {
    stylename: 16 Medium;
    font-family: Poppins, serif;
    font-size: 16px;
    font-weight: 500;
    line-height: 24px;
    letter-spacing: 0em;
    text-align: left;
}

.send-code-again-text {
    font-family: "Poppins", serif;
    font-style: normal;
    font-weight: 500;
    font-size: 14px;
    line-height: 21px;
    color: #f76600;
}

.send-code-again-text-dark {
    font-family: "Poppins", serif;
    font-style: normal;
    font-weight: 500;
    font-size: 14px;
    line-height: 21px;
    color: black;
}

.lable-custom-width {
    width: 80px;
    min-width: 80px;
}

.table-bordered {
    border: 1px solid gray !important;
}

.table-eway th:first-child,
.table-eway td:first-child {
    padding-left: 0.75rem !important;
}

.table-eway tr:last-child,
.table-eway th:last-child,
.table-eway td:last-child {
    padding-right: 0.75rem !important;
}

.table-eway thead {
    border-bottom: 1px solid gray;
}

.border-gray {
    border: 1px solid gray !important;
}

.table-eway tr,
.table-eway th,
.table-eway td {
    font-weight: 600 !important;
}

.br-5 {
    border-radius: 5px !important;
}

.tooltip-dropdown-content1 {
    display: none;
}

.tooltip-content1 {
    margin-left: 0.25rem !important;
}

.custom-tooltip1:hover .tooltip-dropdown-content1 {
    display: block;
}

.tooltip-inner {
    font-weight: 500 !important;
}

.tooltip-dropdown-content1 {
    position: absolute;
    top: 20px;
    background-color: white;
    border-radius: 5px;
    border: 1px solid black;
    max-height: 300px;
    height: auto;
    overflow: auto;
    z-index: 9;
}

.border-black {
    border: 1px solid black;
}

.border-right-black {
    border-right: 1px solid black !important;
}

.btn-icon {
    border-radius: 5px;
    padding: 8px 37px 8px 10px;
    box-shadow: none;
    overflow: hidden;
    font-weight: 500;

    &:focus {
        box-shadow: none;
    }
}
.btn-icon-primary {
    border: 1px solid #4f158c !important;
    background-color: #4f158c !important;
    color: #ffffff !important;
    box-shadow: none;
    position: relative;
    &:hover,
    &:focus,
    &:active,
    &.active {
        box-shadow: none !important;
        background-color: #f76600 !important;
        color: #fff !important;
    }

    &::after {
        width: 20px;
        height: 65%;
        font-size: 18px;
        //background-color: #fff;
        color: #fff;
        opacity: 0.8;
        content: "+";
        position: absolute;
        right: 10px;
        top: 18%;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 5px;
        //border: 1px solid #4F158C;
        // @media (max-width: 1599px) and (min-width: 1200px) {
        //     right: 4px;
        // }
    }
}

.btn-icon-primary-plus-center {
    border: 1px solid #4f158c !important;
    background-color: #4f158c !important;
    color: #ffffff !important;
    box-shadow: none;
    position: relative;
    &:hover,
    &:focus,
    &:active,
    &.active {
        box-shadow: none !important;
        background-color: #f76600 !important;
        color: #fff !important;
    }

    &::after {
        width: 20px;
        height: 54%;
        font-size: 18px;
        //background-color: #fff;
        color: #fff;
        opacity: 0.8;
        content: "+";
        position: absolute;
        right: 10px;
        top: 18%;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 5px;
        //border: 1px solid #4F158C;
        @media (max-width: 1599px) and (min-width: 1200px) {
            right: 4px;
        }
    }
}

.btn-icon1 {
    border-radius: 5px;
    padding: 6px 37px 6px 10px;
    box-shadow: none;
    overflow: hidden;
    font-weight: 500;

    &:focus {
        box-shadow: none;
    }
}

.btn-icon1-primary {
    border: 1px solid #4f158c !important;
    background-color: #4f158c !important;
    color: #ffffff !important;
    box-shadow: none;
    position: relative;

    &:hover,
    &:focus,
    &:active,
    &.active {
        box-shadow: none !important;
        background-color: #f76600 !important;
        color: #fff !important;
    }

    &::after {
        width: 23px;
        height: 60%;
        font-size: 18px;
        //background-color: #fff;
        color: #fff;
        opacity: 0.8;
        content: "+";
        position: absolute;
        right: 8px;
        top: 19%;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 5px;
        //border: 1px solid #4F158C;
    }
}

.plus-btn-menu {
    width: 20px;
    height: 100%;
    //background-color: white;
    color: #fff;
    border-radius: 4px;
    justify-content: center;
    align-items: center;
    position: absolute;
    right: 5px;
    top: 0;
    display: none;
    z-index: 3;
}

.menu-hover-btn:hover .plus-btn-menu {
    display: flex;
}

.adjust-box-border {
    border: 1.5px solid black !important;
    border-radius: 0.625rem;
    padding: 10px;
}

.vertical-align-middle {
    vertical-align: middle !important;
}
.form-select-multiple {
    max-width: 200px !important;
    height: 40px !important;
    overflow: auto !important;
}

.auto-payment-reminder-select {
    max-width: 550px !important;
    height: 70px !important;
    overflow: auto !important;
}

.price-list-select {
    max-width: 750px !important;
    height: 70px !important;
    overflow: auto !important;
}

.price-list-custom-select2-width {
    min-width: 200px !important;
}

.billing-details,
.company-details {
    .form-group {
        label {
            font-weight: 500;
            font-size: 14px;
        }
        .form-control {
            font-size: 14px !important;
            &::placeholder {
                color: black !important;
                font-size: 14px !important;
            }
        }
    }
}

.tooltip-inner {
    font-size: 12px;
    max-width: 200px;
    padding: 4px 5px;
    color: #000000;
    text-align: center;
    background-color: #ffffff;
    border-radius: 0 !important;
    line-height: 1;
    box-shadow: 0 0 1px 1px rgb(*********** / 15%);
}

.bs-tooltip-bottom .tooltip-inner {
    border: 0.3px solid rgb(*********** / 30%) !important;
    font-weight: 400 !important;
    box-shadow: none !important;
}
.table-bordered {
    border: 1px solid #eff2f5 !important;
}
.items-details-card {
    table {
        th {
            width: 110px !important;
        }
        th:first-child,
        td:first-child {
            padding-left: 0.75rem !important;
        }
        th:last-child,
        td:last-child {
            padding-right: 0.75rem !important;
        }
        tbody {
            border: 1px solid #eff2f5 !important;
        }
    }
}

.text-red-color {
    color: red !important;
}

.ql-toolbar.ql-snow {
    border: 1px solid black !important;
}

.ql-toolbar.ql-snow + .ql-container.ql-snow {
    border-top: 0px !important;
}

.ql-container.ql-snow {
    border: 1px solid black !important;
}

.ql-snow .ql-picker .ql-picker-label {
    color: black !important;
}

.ql-toolbar.ql-snow .ql-picker .ql-fill,
.ql-toolbar.ql-snow .ql-picker .ql-stroke,
.ql-toolbar.ql-snow button .ql-fill,
.ql-toolbar.ql-snow button .ql-stroke {
    stroke: black !important;
}

.ql-editor p,
.ql-editor ol,
.ql-editor ul,
.ql-editor pre,
.ql-editor blockquote,
.ql-editor h1,
.ql-editor h2,
.ql-editor h3,
.ql-editor h4,
.ql-editor h5,
.ql-editor h6 {
    color: black !important;
}

.spinner-border {
    width: 3rem;
    height: 3rem;
}

#loader .d-flex {
    padding-left: 230px;
    @media (max-width: 991px) {
        padding-left: 0 !important;
    }
}

.whitespace-nowrap {
    white-space: nowrap !important;
}

footer {
    a {
        .active {
            color: #fc733f !important;
        }
    }
}

.profile-border {
    border: 1px solid rgba(79, 21, 140);
}

.balance-sheet-liabilities,
.balance-sheet-asset {
    position: relative;
    &::after {
        position: absolute;
        content: " ";
        width: 1px;
        height: 100%;
        background-color: #dde0e4;
        top: 0;
        right: 50%;
        z-index: 99;
    }

    &::before {
        position: absolute;
        content: " ";
        width: 1px;
        height: 100%;
        background-color: #dde0e4;
        top: 0;
        right: 25%;
        z-index: 99;
    }
}

.tpl-account {
    position: relative;
    &::after {
        position: absolute;
        content: " ";
        width: 1px;
        height: 100%;
        background-color: #dde0e4;
        top: 0;
        right: 50%;
        z-index: 99;
    }

    &::before {
        position: absolute;
        content: " ";
        width: 1px;
        height: 100%;
        background-color: #dde0e4;
        top: 0;
        right: 25%;
        z-index: 99;
    }
}

.trial-balance-report {
    width: 100%;
    min-width: 800px;
}

.generate-eway-bill {
    .form-label {
        margin-bottom: 0;
        font-size: 1rem !important;
        font-weight: 600;
        color: #181c32 !important;
    }
    .table-bordered {
        border: 1px solid #8e92a2 !important;
    }
    .table-bordered > :not(caption) > * {
        border-width: 0 !important;
    }
}

#tradingPLAccountReportDatatable,
#balanceSheetReportDatatable,
#trialBalanceReportDatatable {
    .text-gray-100 {
        color: #22242f !important;
    }
}
.custom-table-line {
    position: relative;
    &::before {
        position: absolute;
        content: " ";
        width: 1px;
        height: 100%;
        background-color: #dde0e4;
        top: 0;
        right: 34%;
        z-index: 99;
    }
    &::after {
        position: absolute;
        content: " ";
        width: 1px;
        height: 100%;
        background-color: #dde0e4;
        top: 0;
        right: 17%;
        z-index: 99;
    }
}
.text-custom-gray-100 {
    color: #22242f !important;
}
.permission-table {
    border-radius: 6px;
    border: 1px solid gray;
    th:first-child,
    td:first-child {
        width: 30%;
        border-right-color: #dde0e4;
        border-right-style: solid;
        border-right-width: 1px;
    }
    tr:first-child,
    th:first-child,
    td:first-child {
        padding-left: 0.75rem;
        padding-right: 0.75rem;
    }
    .table {
        white-space: nowrap;
        tbody {
            tr {
                border-color: #dde0e4;
                border-style: solid;
                border-width: 1px;
                border-left: 0px;
                border-right: 0px;
                td {
                    width: 10%;
                }
            }
        }
    }
}

.fs-22 {
    font-size: 22px;
}
.company-img {
    width: 80px;
    height: 80px;
    min-width: 80px;
    border-radius: 15px;
    img {
        border-radius: 15px;
    }
}
.fw-5 {
    font-weight: 500;
}
.max-w-384 {
    max-width: 384px;
}
.ms-80 {
    margin-left: 5rem;
    @media (max-width: 375px) {
        margin-left: 40px;
    }
}
.name-list {
    padding-left: 1.5rem !important;
    li {
        font-size: 14px !important;
        font-weight: 500;
    }
}
.highlight-text {
    background-color: #eeeaf4;
    padding: 10px;
    span {
        color: #4f158c;
        font-weight: 500;
        font-size: 14px;
    }
}
.card-header {
    padding: 20px 25px !important;
    border-bottom: none !important;
    h1 {
        color: #f76600;
        font-size: 26px;
        span {
            color: #747685;
            font-size: 16px;
        }
    }
}
.card-body {
    .list-icon {
        width: 20px;
        height: 20px;
        i {
            color: #f76600;
            font-size: 20px;
        }
    }
}
.card-footer {
    padding: 20px 25px !important;
    border-top: none !important;
}
.fs-18 {
    font-size: 18px !important;
}
.fs-26 {
    font-size: 26px;
}
.basic-plan-card {
    padding: 10px;
    .plan-amount {
        margin-bottom: 30px;
        h2 {
            color: #f76600;
        }
    }
    ul {
        li {
            list-style: none;
            font-size: 14px;
            color: #181c32;
            font-weight: 500;
            margin-bottom: 5px;
        }
    }
}
.payment-card {
    border: 1px solid #dde0e4;
    padding: 20px;
    .border-bottom {
        border-bottom: 1px solid #dde0e4 !important;
    }
    .card-footer {
        margin-top: 30px;
        padding: 0 !important;
    }
}
.gst-template-section {
    .btn-check {
        &:focus + .btn,
        .btn:focus {
            outline: 0;
            -webkit-box-shadow: 0 0 0 0.25rem rgba(166, 206, 57, 0.25);
            box-shadow: 0 0 0 0.25rem rgba(166, 206, 57, 0.25);
        }
    }

    .check-btn {
        border-radius: 0 !important;
        padding: 5px 7px !important;
    }

    .btn-check:checked + label {
        background-color: #e7e7f8;
    }

    .pdf-image {
        width: 120px;
        height: 150px;
        border: 1px solid #e9e9e9;

        position: relative;
        .overlay {
            position: absolute;
            bottom: 0;
            background: rgb(248 248 248 / 65%); /* Black see-through */
            width: 100%;
            height: 100%;
            transition: 0.5s ease;
            opacity: 0;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        &:hover {
            .overlay {
                opacity: 1;
            }
        }
        @media (max-width: 991px) {
            width: 100px;
            height: 130px;
            margin: 0 auto;
        }
    }
}

.basic-card-plan {
    max-width: 500px !important;
    border-radius: 10px;
    padding: 20px;
    // .time-duration {
    //     max-width: 200px;
    // }
}
.border-left-gray-300 {
    border-left: 1px solid #a1a5b7;
    @media (max-width: 575px) {
        border-left: none !important;
    }
}
.basic-plan-modal {
    max-width: 700px !important;
    @media (max-width: 768px) {
        max-width: 575px !important;
    }
    .basic-plans {
        border: 1px solid #dde0e4;
        border-radius: 10px;
    }
}
.plan-table {
    tr {
        td {
            // width: 50% !important;
            font-weight: 500 !important;
            &:first-child {
                padding-left: 0 !important;
            }
            &:last-child {
                padding-right: 0 !important;
            }
        }
    }
}
.plan-table > :not(caption) > * > * {
    padding: 0.4rem 0.75rem !important;
}
.px-40 {
    padding-left: 40px;
    padding-right: 40px;
    @media (max-width: 1199px) {
        padding-left: 25px;
        padding-right: 25px;
    }
    @media (max-width: 991px) {
        padding-left: 0;
        padding-right: 0;
    }
}
.mx-30 {
    @media (max-width: 991px) {
        margin-left: 30px;
        margin-right: 30px;
    }
}
.number-input {
    width: 40px;
    height: 24px;
    border: 1px solid #8e92a1 !important;
    border-radius: 5px !important;
    text-align: center;
    margin-right: 3px;
    &::placeholder {
        color: #181c32 !important;
        font-weight: 500;
    }
}
.buy-now-calculation-table td {
    padding-bottom: 5px !important;
}

.flipped {
    transform: rotateX(180deg);
}
.flipped-table {
    transform: rotateX(180deg);
}
.preview-btn {
    padding: 3px;
    border-radius: 5px;
    display: block;
    background-color: #4f158c;
    margin: 5px 10px 0 10px;
    color: white !important;
}

//   shepherd Css
.shepherd-button {
    background: #4e158c !important;
    border: 0;
    border-radius: 3px;
    color: hsla(0, 0%, 100%, 0.75);
    cursor: pointer;
    margin-right: 0.5rem;
    padding: 0.5rem 1.5rem;
    transition: all 0.5s ease;
}
.shepherd-button:not(:disabled):hover {
    background: #196fcc;
    color: hsla(0, 0%, 100%, 0.75);
}
.shepherd-button.shepherd-button-secondary {
    background: #083665;
    color: rgba(0, 0, 0, 0.75);
}
.shepherd-button.shepherd-button-secondary:not(:disabled):hover {
    background: #d6d9db;
    color: rgba(0, 0, 0, 0.75);
}
.shepherd-button:disabled {
    cursor: not-allowed;
}
.shepherd-footer {
    border-bottom-left-radius: 5px;
    border-bottom-right-radius: 5px;
    display: flex;
    justify-content: flex-end;
    padding: 0 0.75rem 0.75rem;
}
.shepherd-footer .shepherd-button:last-child {
    margin-right: 0;
}
.shepherd-cancel-icon {
    background: transparent;
    border: none;
    color: hsla(0, 0%, 50%, 0.75);
    cursor: pointer;
    font-size: 2em;
    font-weight: 400;
    margin: 0;
    padding: 0;
    transition: color 0.5s ease;
}
.shepherd-cancel-icon:hover {
    color: rgba(0, 0, 0, 0.75);
}
.shepherd-has-title .shepherd-content .shepherd-cancel-icon {
    color: hsla(0, 0%, 50%, 0.75);
}
.shepherd-has-title .shepherd-content .shepherd-cancel-icon:hover {
    color: rgba(0, 0, 0, 0.75);
}
.shepherd-title {
    color: rgba(255, 255, 255);
    display: flex;
    flex: 1 0 auto;
    font-size: 1rem;
    font-weight: 400;
    margin: 0;
    padding: 0;
}
.shepherd-header {
    align-items: center;
    border-top-left-radius: 5px;
    border-top-right-radius: 5px;
    display: flex;
    justify-content: flex-end;
    line-height: 2em;
    padding: 0.75rem 0.75rem 0;
}
.shepherd-has-title .shepherd-content .shepherd-header {
    background: #f76601;
    color: white;
    padding: 1em;
}
.shepherd-text {
    color: rgba(0, 0, 0, 0.75);
    font-size: 1rem;
    line-height: 1.3em;
    padding: 0.75em;
}
.shepherd-text p {
    margin-top: 0;
}
.shepherd-text p:last-child {
    margin-bottom: 0;
}
.shepherd-content {
    border-radius: 5px;
    outline: none;
    padding: 0;
}
.shepherd-element {
    background: #fff;
    border-radius: 5px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
    max-width: 400px;
    opacity: 0;
    outline: none;
    transition: opacity 0.3s, visibility 0.3s;
    visibility: hidden;
    width: 100%;
    z-index: 9999;
}
.shepherd-enabled.shepherd-element {
    opacity: 1;
    visibility: visible;
}
.shepherd-element[data-popper-reference-hidden]:not(.shepherd-centered) {
    opacity: 0;
    pointer-events: none;
    visibility: hidden;
}
.shepherd-element,
.shepherd-element *,
.shepherd-element :after,
.shepherd-element :before {
    box-sizing: border-box;
}
.shepherd-arrow,
.shepherd-arrow:before {
    height: 16px;
    position: absolute;
    width: 16px;
    z-index: -1;
}
.shepherd-arrow:before {
    background: #fff;
    content: "";
    transform: rotate(45deg);
}
.shepherd-element[data-popper-placement^="top"] > .shepherd-arrow {
    bottom: -8px;
}
.shepherd-element[data-popper-placement^="bottom"] > .shepherd-arrow {
    top: -8px;
}
.shepherd-element[data-popper-placement^="left"] > .shepherd-arrow {
    right: -8px;
}
.shepherd-element[data-popper-placement^="right"] > .shepherd-arrow {
    left: -8px;
}
.shepherd-element.shepherd-centered > .shepherd-arrow {
    opacity: 0;
}
.shepherd-element.shepherd-has-title[data-popper-placement^="bottom"]
    > .shepherd-arrow:before {
    background-color: #f76601;
}
.shepherd-target-click-disabled.shepherd-enabled.shepherd-target,
.shepherd-target-click-disabled.shepherd-enabled.shepherd-target * {
    pointer-events: none;
}
.shepherd-modal-overlay-container {
    height: 0;
    left: 0;
    opacity: 0;
    overflow: hidden;
    pointer-events: none;
    position: fixed;
    top: 0;
    transition: all 0.3s ease-out, height 0ms 0.3s, opacity 0.3s 0ms;
    width: 100vw;
    z-index: 9997;
}
.shepherd-modal-overlay-container.shepherd-modal-is-visible {
    height: 100vh;
    opacity: 0.5;
    transform: translateZ(0);
    transition: all 0.3s ease-out, height 0s 0s, opacity 0.3s 0s;
}
.shepherd-modal-overlay-container.shepherd-modal-is-visible path {
    pointer-events: all;
}

body.shepherd-modal-is-visible::before {
    overflow: hidden;
}

[data-kt-aside-minimize="on"] .aside:not(.aside-hoverable) .aside-logo .logo,
[data-kt-aside-minimize="on"] .aside:not(:hover) .aside-logo h3 {
    display: none;
}
.transaction-navigation {
    width: 70px;
    height: 70px;
    background-color: #eff2f5;
    border-radius: 50px;
    @media (max-width: 430px) {
        width: 45px;
        height: 45px;
    }
    .fa-solid {
        font-size: 45px;
        color: #4e158c;
        left: 21px;
        margin-left: 13px;
        margin-top: 12px;
        @media (max-width: 430px) {
            font-size: 25px;
            margin-left: 8px;
            margin-top: 10px;
        }
    }
    .fa-file-lines {
        margin-left: 20px;
        @media (max-width: 430px) {
            margin-left: 13px;
        }
    }
}

#updateIncomeNavigation .modal-dialog {
    transform: translateX(100%) !important;
}

#updateIncomeNavigation.show .modal-dialog {
    transform: none !important;
}
#updateIncomeNavigation.modal-content {
    border-top-right-radius: 0 !important;
}
.income-transction-hr {
    width: 100%;
    height: 15px;
    border-bottom: 1px dotted;
    text-align: center;
    margin-bottom: 18px;
    span {
        font-size: 18px;
        background-color: #ffffff;
        padding: 0 5px;
    }
}
.sale-transaction-hr {
    border-bottom: 1px dotted #000;
    text-align: center;
    height: 13px;
    margin-bottom: 8px;
    font-size: medium;
}
.sale-transaction-hr span {
    background: #fff;
    padding: 0 5px;
}

.iti {
    width: 100%;
}
.tutorial-item {
    iframe {
        width: 100% !important;
        height: 200px !important;
        max-width: 100% !important;
    }
}

.page-loader {
    // background: url("../../../public/assets/images/finaccle_loader.gif")
    //     no-repeat center center;
    position: fixed;
    background-color: white;
    top: 0;
    left: 0;
    opacity: 0.6;
    height: 100vh;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999999;

    &:after {
        content: "";
        background: url("../../../public/assets/images/finaccle_loader.gif")
            no-repeat center center;
        background-size: cover;
        position: absolute;
        left: 0;
        right: 0;
        z-index: 2;
        margin: auto;
        display: inline-block;
        width: 80px;
        height: 80px;
        vertical-align: text-bottom;
        @media (max-width: 575px) {
            width: 50px;
            height: 50px;
        }
    }
}
@keyframes spinner-border {
    to {
        transform: rotate(360deg);
    }
}
.holds-the-iframe {
    background: url(../../../public/assets/images/finaccle_loader.gif) center
        center no-repeat;
}

.btn.signup {
    border: 1px solid #4f158c !important;
    background-color: #f5f8fa;
}

.signup:hover .text-Purple {
    color: #4f158c;
}

.previous-next-btn {
    border: 1px solid #4f158c !important;
    width: 35px;
    height: 35px;
    background-color: #4f158c;
    border-radius: 50px;
    padding: 1px 0 2px 2px !important;
    i {
        font-size: 17px;
        color: #eff2f5;
    }
}

.video-tutorial-btn {
    width: 35px;
    height: 35px;
    border-radius: 50px;
    padding: 1px 0 2px 2px !important;
    i {
        font-size: 35px;
        color: #f1416c;
    }
}

.configuration-button {
    top: 5px;
    right: 5px;

    .previous-next-btn:hover {
        background-color: #eff2f5;
        i {
            color: #4f158c;
        }
    }
}

// body .flatpickr-calendar{
//     top: 50% !important;
//     left: 43% !important;
// }
.border-xl-right {
    @media (min-width: 1200px) {
        border-right: 1px solid #dde0e4;
    }
}

.gj-picker {
    .modal-footer {
        .btn:not(.btn-shadow):not(.shadow):not(.shadow-sm):not(.shadow-lg) {
            &:focus,
            &:hover {
                box-shadow: rgba(247, 101, 0, 0.24) 0 2px 2px 0,
                    rgba(247, 101, 0, 0.64) 0 2px 16px 0 !important;
            }
        }
        .btn-outline-secondary {
            color: #ffffff !important;
            border-color: #4f158c !important;
            background-color: #4f158c !important;
            &:focus {
                box-shadow: rgba(247, 101, 0, 0.24) 0 2px 2px 0,
                    rgba(247, 101, 0, 0.64) 0 2px 16px 0 !important;
            }
        }
    }
}
.report-section {
    background-color: white !important;
    border-radius: 10px;
    border: 1px solid #a689c5;
    .nav-tabs {
        border-bottom: 1px solid #dde0e4;
        .nav-link {
            font-size: 15px;
            font-weight: 600;
            padding: 0;
            border-radius: 0;
            border: none;
            color: #4f158c;
            border-bottom: 2px solid transparent;
            &.active {
                color: #4f158c;
                background-color: transparent;
                border: none;
                border-bottom: 2px solid #4f158c;
            }
        }
    }
    .tab-content {
        padding: 30px 25px 8px 30px;
        @media (max-width: 575px) {
            padding: 25px 20px 3px 20px;
        }
    }
    .report-card {
        border-radius: 10px;
        border: 1px solid #a689c5;
        overflow: hidden;
        background-color: white !important;
        .heading-text {
            border-bottom: 1px solid #4f158c;
        }
        .card-body {
            height: 170px;
            overflow: auto !important;
            ul {
                li {
                    font-size: 14px;
                    font-weight: 600;
                    list-style: none;
                    border-bottom: 1px solid #dde0e4;
                    padding: 10px 0;
                }
            }
        }
    }
    ::-webkit-scrollbar-thumb {
        background-color: #4f158c !important;
    }
    ::-webkit-scrollbar {
        width: 4px;
        background-color: #dde0e4;
    }
    .mb-22px {
        margin-bottom: 22px;
    }
}

.setting-report-section {
    background-color: #fff !important;
    border: 1px solid #4f158c !important;
    border-radius: 8px !important;
    .border-lg-right {
        border-right: 1px solid #dee2e6;
        @media (max-width: 991px) {
            border-right: 0;
        }
    }
    .nav-tabs {
        border-bottom: 1px solid #dde0e4;
        .nav-link {
            padding: 8px 15px 8px 15px !important;
            background-color: #f1f4f6 !important;
            border-radius: 10px 10px 0 0 !important;
            font-size: 14px;
            font-weight: 600;
            padding: 0;
            border-radius: 0;
            border: none;
            border: 1px solid #dde0e4;
            color: black;
            &.active {
                background-color: #4f158c !important;
                color: #fff !important;
            }
        }
    }
    .tab-content {
        padding: 30px 25px 8px 30px;
        @media (max-width: 575px) {
            padding: 25px 20px 3px 20px;
        }
        .nav-tabs {
            border-bottom: 1px solid #dde0e4;
            .nav-link {
                padding: 8px 15px 8px 15px !important;
                background-color: #f1f4f6 !important;
                border-radius: 10px 10px 0 0 !important;
                font-size: 15px;
                font-weight: 600;
                padding: 0;
                color: black;
                border: 1px solid #dde0e4;
                border-radius: 5px;
                &.active {
                    color: white !important;
                    background-color: #4f158c !important;
                    border: none;
                    border: 2px solid #4f158c;
                    border-radius: 5px;
                }
            }
        }
    }
    .report-card {
        border-radius: 10px;
        border: 1px solid #a689c5;
        overflow: hidden;
        background-color: white !important;
        .heading-text {
            border-bottom: 1px solid #4f158c;
        }
        .card-body {
            height: 220px;
            overflow: auto !important;
            ul {
                li {
                    font-size: 14px;
                    font-weight: 600;
                    list-style: none;
                    border-bottom: 1px solid #dde0e4;
                    padding: 10px 0;
                }
            }
        }
    }
    ::-webkit-scrollbar-thumb {
        background-color: #4f158c !important;
    }
    ::-webkit-scrollbar {
        width: 4px !important;
        height: 3px !important;
        border-radius: 3px !important;
        background-color: #dde0e4 !important;
    }
    .mb-22px {
        margin-bottom: 22px;
    }
}
.bootstrap-maxlength {
    bottom: 0% !important;
    top: auto !important;
    left: auto !important;
    font-size: 11px !important;
    right: 20px !important;
}

// .enabled-item-description .limit-textarea {
//     bottom: 25% !important;
//     top: 50% !important;
// }

.input-max-length {
    right: 10px !important;
}
.p-20px {
    padding: 20px;
}
.switch-mobile-app-modal {
    .modal-dialog {
        @media (max-width: 575px) {
            margin: auto 20px !important;
        }
        .modal-content {
            border-radius: 10px;
            .close-btn {
                position: absolute;
                top: 10px;
                right: 10px;
                z-index: 9;
                &:hover {
                    background-color: transparent !important;
                }
            }
            .modal-img {
                height: 60px;
                max-width: 100%;
                margin-top: 10px;
                margin-bottom: 30px;
                img {
                    height: 100%;
                }
            }
            .google-pay,
            .app-store {
                max-width: 165px;
            }
        }
    }
}
.til-menu-hover:hover {
    color: #4f158c !important;
    text-decoration: underline !important;
}

.barcode-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding-left: 5px;
    padding-right: 5px;
    padding-top: 10px;
    padding-bottom: 10px;
    // margin-bottom: 20px;
}

.barcode-main {
    border: 1px solid #ccc !important;
    display: block !important;
    // margin: 10px auto !important;
    padding: 0.1in !important;
    page-break-after: always !important;
    // width: 8.25in !important;
    // max-width: 1025px !important;
}

.barcode-style {
    padding: 0.04in !important;
    width: 2.87in !important;
}

.barcode-item {
    border: 1px dotted #ccc !important;
    display: block !important;
    float: left !important;
    font-size: 12px !important;
    line-height: 14px !important;
    overflow: hidden !important;
    text-align: center !important;
    // text-transform: uppercase !important;
    min-height: 90px !important;
}

.barcode-print-focus-button {
    padding-right: 8px !important;
    padding-left: 10px !important;
    padding-top: 5px !important;
    padding-bottom: 5px !important;
    border: 1px solid #4f158c !important;
}

.barcode-print-focus-button:hover {
    background-color: #4f158c !important;
    i {
        color: white !important;
    }
}

.barcode-print-focus-button:focus {
    background-color: #4f158c !important;
    i {
        color: white !important;
    }
}

.sku-code-max-length {
    padding-right: 75px !important;
}
.search-container {
    position: absolute;
    top: 15px;
    left: 0;
    @media (max-width: 991px) {
        top: 11px;
    }
}
.search-text {
    position: relative;
    top: -1px;
    z-index: 5;
    transition: z-index 0.8s, width 0.5s, border 0.3s;
    height: 36px;
    width: 36px;
    margin: 0;
    padding: 5px 5px 5px 28px;
    box-sizing: border-box;
    font-size: 14px;
    cursor: pointer;
    border-radius: 5px;
    border: 1px solid #6f6f6f;
    background-color: white;
    color: #747685;
    font-weight: 500;
    outline: none;
    cursor: auto;
    &::placeholder {
        color: #747685;
        font-weight: 500;
    }
    @media (max-width: 991px) {
        top: -2px;
        height: 37px;
        width: 37px;
    }
    // @media (max-width: 1290px) {
    //     width: 36px;
    // }
    // @media (max-width: 1199px) {
    //     width: 200px;
    // }
    @media (max-width: 575px) {
        top: 0px;
        height: 33px;
        width: 30px;
    }
    // @media (max-width: 360px) {
    //     width: 36px;
    // }
}
.expanded {
    width: calc(100% - 47px);
    @media (max-width: 767px) {
        width: calc(100% - 37px);
    }
}
.list-expanded {
    width: calc(100% - 149px);
    @media (max-width: 1199px) {
        width: calc(100% - 55px);
    }
    @media (max-width: 767px) {
        width: calc(100% - 44px);
    }
    @media (max-width: 575px) {
        width: fit-content;
    }
}
.search-icon {
    position: absolute;
    top: 10px;
    left: 10px;
    font-size: 14px;
    font-weight: bold;
    color: #4f158c;
    z-index: 9;
}
input.search-text::-webkit-search-cancel-button {
    cursor: pointer;
}
.search-list {
    position: absolute;
    margin-top: 8px;
    left: 0;
    top: 100%;
    background-color: white;
    border-radius: 10px;
    padding: 0 15px;
    box-shadow: 0 4px 50px rgba(82, 63, 105, 0.15);
    display: none;
    @media (max-width: 575px) {
        padding: 10px;
        left: -15%;
    }
}

.search-item {
    list-style: none;
    border-bottom: 1px solid #dde0e4;
    padding: 12px 0;
    @media (max-width: 575px) {
        padding: 10px 0;
    }
    svg {
        height: 18px !important;
        @media (max-width: 575px) {
            height: 14px !important;
        }
    }
    .text,
    .desc {
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }
}

.search-item:last-of-type {
    border-bottom: none;
}
.dashboard-card {
    border: 1px solid #4f158c;
    border-radius: 10px;
    padding: 15px 20px;
    h3 {
        font-size: 26px;
        font-weight: bold;
        color: #4f158c;
    }
}
.select-plan {
    .form-control {
        min-width: 115px;
        max-width: 115px;
        height: 40px;
        overflow: auto;
        position: relative;
        padding: 0.4rem;
        @media (max-width: 768px) {
            height: 36px;
        }
        .down-arrow {
            position: absolute;
            right: 8px;
            top: 8px;
        }
    }
}

.available-quota {
    font-size: 12px;
    border: 1px solid #4f158c;
    padding: 3px 8px;
    color: #4f158c;
    border-radius: 6px;
}

.total-available-quota {
    background-color: #eff2f5 !important;
    padding: 4px 6px 4px 15px !important;
    border: 1px solid #dde0e4 !important;
    border-radius: 10px !important;
}

.send-link-whatsapp-link {
    font-weight: 600;
    display: flex;
    justify-content: space-between;
    background-color: #f1f4f6;
    margin: 16px 8px 0px 8px;
    padding: 10px 14px;
    border-radius: 11px;
    color: #4f158c;
    font-size: 15px;
}

.notification-container {
    border: 1px solid #dde0e4;
    border-radius: 9px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: #def0fa;
    cursor: pointer;
}

.read-notification-container {
    border: 1px solid #dde0e4;
    border-radius: 9px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: #f5f8fa;
    cursor: pointer;
}

.read-full-page-notification-container {
    border-bottom: 1px solid #dde0e4;
    display: flex;
    align-items: center;
    cursor: pointer;
}

.full-page-notification-container {
    border-bottom: 1px solid #dde0e4;
    display: flex;
    align-items: center;
    background-color: #def0fa;
    cursor: pointer;
}
.dropdown-menu-sm-end[data-bs-popper] {
    width: 530px !important;
    @media (max-width: 768px) {
        width: 400px !important;
    }
    @media (max-width: 575px) {
        width: 300px !important;
        left: -170px !important;
    }
}
.min-w-30 {
    min-width: 30px !important;
    @media (max-width: 575px) {
        width: 20px;
        min-width: 20px !important;
    }
}
.min-w-40 {
    min-width: 40px !important;
    @media (max-width: 575px) {
        width: 20px;
        min-width: 20px !important;
    }
}

.btn-outline-danger:hover {
    color: #ffffff;
    background-color: #f1416c;
    border-color: #f1416c;
}

.delete-notification:hover {
    .text-danger {
        color: #ffffff !important;
    }
}

.consultant-dropdown {
    overflow-y: scroll;
    max-height: 305px;
    width: 100%;
}

.show-qr-setting-div {
    .select2-container--bootstrap5{
        width: auto !important;
    }
}
.consultant-dropdown .consultant-option:hover {
    background-color: #f7efff !important;
}

.assign-user:not(:last-child) {
    border-bottom: 1px solid #4f158c;
}

.pdf-notice,
.pdf-notice-icon {
    color: #ff9900;
}

.barcode-print-item-icon {
    padding: 0px 1px 2px 5px !important;
    margin-bottom: 4px !important;
    border: 1px solid #4f158c !important;
}

.barcode-print-item-icon:hover {
    background-color: #4f158c !important;
    i {
        color: white !important;
    }
}

.barcode-print-item-icon:focus {
    background-color: #4f158c !important;
    i {
        color: white !important;
    }
}

.disabled-notification-container {
    pointer-events: none;
    cursor: default;
}

.disable-notification-text {
    text-decoration: line-through;
    color: #5a5a5a;
}

html,
body {
    height: auto !important;
    min-height: 100vh;
}

.mx-20px {
    margin-left: 20px !important;
    margin-right: 20px !important;
}

.pdf3-preview {
    * {
        margin: 0;
        padding: 0;
        text-indent: 0;
        font-family: "Arial";
        font-size: 13px;
        font-weight: 400;
        box-sizing: border-box;
    }

    @page {
        margin: 20px;
    }

    h1 {
        font-size: 24px;
    }
    @font-face {
        font-family: "Arial";
        src: url(/public/fonts/Arial.ttf) format(truetype);
        font-style: normal;
        font-weight: 400;
        font-display: swap;
    }

    @font-face {
        font-family: "Arial";
        src: url(/public/fonts/arialbd.ttf) format(truetype);
        font-style: normal;
        font-weight: 600;
        font-display: swap;
    }

    .main-table {
        display: flex;
        flex-direction: column;
        min-height: 100vh;
        width: 100%;
        box-sizing: border-box;
        border: 2px solid black;
    }

    table {
        display: table;
        width: 100%;
        border-collapse: collapse;
    }

    .text-primary {
        color: #4f158c;
    }

    .address {
        font-size: 11px;
        // line-height: 14px;
        padding-left: 8px;
        padding-right: 8px;
    }

    td {
        vertical-align: top;
    }

    .fs-13 {
        font-size: 13px;
    }

    .fs-12 {
        font-size: 12px;
    }

    .fw-6 {
        font-weight: 600;
    }

    .fw-4 {
        font-weight: 400;
    }

    .whitespace-nowrap {
        white-space: nowrap;
    }

    .border-bottom {
        border-bottom: 1px solid black !important;
    }

    .border-right {
        border-right: 1px solid black !important;
    }

    .border-top {
        border-top: 1px solid black !important;
    }

    .border-left {
        border-left: 1px solid black !important;
    }

    .vertical-top {
        vertical-align: top;
    }

    .vertical-middle {
        vertical-align: middle;
    }

    .vertical-bottom {
        vertical-align: bottom;
    }

    .text-center {
        text-align: center;
    }

    .text-start {
        text-align: left;
    }

    .text-end {
        text-align: right;
    }

    .table-heading {
        padding: 3px 8px;
        text-align: left;
        position: relative;
        /* background-color: #eeeeee !important; */
    }

    .signature {
        max-width: 217px;
        height: 100px;
    }

    .desc {
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
        font-size: 12px;
        position: relative;
        padding-left: 5px;
        padding-right: 5px;
    }

    .desc::before {
        position: absolute;
        content: "(";
        top: 0;
        left: 0;
        font-size: 12px;
    }

    .desc::after {
        position: absolute;
        content: ")";
        bottom: 2px;
        right: 0;
        font-size: 12px;
    }
}

.pdf4-preview {
    @page {
        margin: 20px;
    }

    .main-table {
        display: flex;
        flex-direction: column;
        width: 100%;
        box-sizing: border-box;
        border: 2px solid black;
    }

    table {
        display: table;
        width: 100%;
        border-collapse: collapse;
    }

    * {
        font-size: 12px;
        font-weight: 400;
        box-sizing: border-box;
    }

    .address {
        font-size: 11px;
        // line-height: 14px;
        padding-left: 8px;
        padding-right: 8px;
    }

    .phone {
        font-size: 11px;
        line-height: 14px;
        padding-left: 8px;
        padding-right: 8px;
    }

    td {
        vertical-align: top;
    }

    .fs-13 {
        font-size: 13px;
    }

    .fw-6 {
        font-weight: 600;
    }

    .whitespace-nowrap {
        white-space: nowrap;
    }

    .border-bottom {
        border-bottom: 1px solid black;
    }

    .border-right {
        border-right: 1px solid black;
    }

    .border-top {
        border-top: 1px solid black;
    }

    .border-left {
        border-left: 1px solid black;
    }

    .vertical-top {
        vertical-align: top;
    }

    .vertical-middle {
        vertical-align: middle;
    }

    .vertical-bottom {
        vertical-align: bottom;
    }

    .text-center {
        text-align: center;
    }

    .text-start {
        text-align: left;
    }

    .signature {
        max-width: 217px;
        height: 100px;
    }

    .item-table tr:last-of-type {
        font-size: 12px;
        height: 100%;
        vertical-align: top;
    }

    .text-end {
        text-align: right !important;
    }

    .px-0 {
        padding: auto 0 !important;
    }

    .w-50 {
        width: 50%;
    }

    .mb-0 {
        margin-bottom: 0 !important;
    }

    .m-0 {
        margin: 0 !important;
    }

    .border-top-gray {
        border-top: 1px solid #dde0e4;
    }

    .border-bottom-gray {
        border-bottom: 1px solid #dde0e4;
    }

    .table-collapsed tr th,
    .table-collapsed tr td {
        font-size: 12px;
        padding: 2px 5px !important;
    }
    .table-collapsed tbody tr th {
        vertical-align: top;
    }
    .table-collapsed thead tr td,
    .table-collapsed thead tr th,
    .table-collapsed tfoot tr td {
        padding: 3px 5px !important;
    }

    .text-start {
        text-align: left;
    }

    .mb-2 {
        margin-bottom: 6px !important;
    }

    .amount-text {
        padding: 0 15px !important;
    }

    .p-0 {
        padding: 0 !important;
    }

    .py-1 {
        padding: 2px auto !important;
    }

    .py-2 {
        padding: 4px auto !important;
    }

    .vertical-align-top {
        vertical-align: top !important;
    }

    .vertical-align-bottom {
        vertical-align: bottom !important;
    }

    @page {
        margin: 8px 20px !important;
        box-sizing: border-box;
    }

    .px-30 {
        padding: auto 30px !important;
    }

    .py-3 {
        padding: 15px auto !important;
    }

    .px-3 {
        padding: auto 15px !important;
    }

    .mb-20 {
        margin-bottom: 20px !important;
    }

    .fs-12 {
        font-size: 12px;
    }

    .text-primary {
        color: #4f158c;
    }

    .text-gray {
        color: #3f4254 !important;
    }

    .fs-14 {
        font-size: 14px;
    }

    .fw-bold {
        font-weight: bold;
    }

    body {
        font-family: "Poppins", sans-serif !important;
        color: #181c32;
        font-weight: 400;
    }

    .text-orange {
        color: #f76600 !important;
    }

    .logo {
        height: 40px;
        margin-left: auto;
        padding: 0 30px;
    }

    .logo img {
        height: 100%;
        max-width: 100%;
    }

    .text-center {
        text-align: center;
    }

    .px-2 {
        padding: 0 4px !important;
    }

    .mb-3 {
        margin-bottom: 15px !important;
    }

    p {
        margin: 0;
    }

    .custom-font-family {
        font-family: DejaVu Sans, Poppins, "Helvetica", Arial, "Liberation Sans",
            sans-serif !important;
    }

    .bg-light {
        background-color: #f5f8fa;
    }

    .bg-primary-light {
        background-color: #f6f3f9;
    }

    .bg-primary {
        background-color: #4f158c;
    }

    .text-white {
        color: #ffffff !important;
    }

    .table-collapsed {
        border-collapse: collapse;
    }

    .authorized-signature {
        width: 170px;
    }

    .ms-auto {
        margin-left: auto;
    }

    .br-5 {
        border-radius: 5px;
    }

    .overflow-hidden {
        overflow: hidden;
    }

    .p-10 {
        padding: 10px !important;
    }

    .position-absolute {
        position: absolute;
    }

    .bottom-0 {
        bottom: 0;
    }

    .w-100 {
        width: 100%;
    }

    .pb-1 {
        padding-bottom: 2px !important;
    }

    .mb-1 {
        margin-bottom: 4px !important;
    }
}
.fixTableHead {
    min-width: fit-content;
    thead {
        th {
            vertical-align: middle;
            &:first-child {
                width: 70px;
                min-width: 70px;
            }
            &:nth-child(2) {
                width: 260px;
                min-width: 260px;
            }
            &:nth-child(4) {
                width: 110px;
                min-width: 110px;
            }
            &:nth-child(5),
            &:nth-child(6) {
                width: 120px;
                min-width: 120px;
            }
            &:nth-child(3),
            &:nth-child(7) {
                width: 130px;
                min-width: 130px;
            }
            &:last-child {
                width: 60px;
                min-width: 60px;
            }
        }
    }
    &.table-wrapper {
        // max-height: 535px;
        max-height: 70vh;
        overflow-y: auto;
        min-width: fit-content;
        padding-right: 0;
        scrollbar-width: none !important;
        tbody {
            tr {
                td {
                    vertical-align: middle;
                    &:first-child {
                        width: 70px;
                        min-width: 70px;
                    }
                    &:nth-child(2) {
                        width: 260px;
                        min-width: 260px;
                    }
                    &:nth-child(4) {
                        width: 110px;
                        min-width: 110px;
                        max-width: 110px;
                    }
                    &:nth-child(5),
                    &:nth-child(6) {
                        width: 120px;
                        min-width: 120px;
                    }
                    &:nth-child(3),
                    &:nth-child(7) {
                        width: 130px;
                        min-width: 130px;
                    }
                    &:last-child {
                        width: 60px;
                        min-width: 60px;
                    }
                }
            }
        }
    }
}
.content {
    @media (min-width: 992px) {
        padding: 25px 0 25px 0 !important;
    }
}
.notes {
    width: fit-content;
    transform: all 0.3s ease-in;
    &:hover {
        box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2);
    }
}
.custom-input-group
    > :not(:first-child):not(.dropdown-menu):not(.valid-tooltip):not(
        .valid-feedback
    ):not(.invalid-tooltip):not(.invalid-feedback) {
    margin-left: 0px !important;
}
.right-0 {
    right: 0 !important;
}
.custom-input-group {
    .custom-select2-width {
        width: calc(100% - 37px) !important;
        @media (max-width: 768px) {
            width: calc(100% - 34px) !important;
        }
    }
}

.disabled-notification-container {
    pointer-events: none;
    cursor: default;
}

.disable-notification-text {
    text-decoration: line-through;
    color: #5a5a5a;
}

html,
body {
    height: auto !important;
    min-height: 100vh;
}

.mx-20px {
    margin-left: 20px !important;
    margin-right: 20px !important;
}



.pdf4-preview {
    @page {
        margin: 20px;
    }

    .main-table {
        display: flex;
        flex-direction: column;
        width: 100%;
        box-sizing: border-box;
        border: 2px solid black;
    }

    table {
        display: table;
        width: 100%;
        border-collapse: collapse;
    }

    * {
        font-size: 12px;
        font-weight: 400;
        box-sizing: border-box;
    }

    .address {
        font-size: 11px;
        // line-height: 14px;
        padding-left: 8px;
        padding-right: 8px;
    }

    .phone {
        font-size: 11px;
        line-height: 14px;
        padding-left: 8px;
        padding-right: 8px;
    }

    td {
        vertical-align: top;
    }

    .fs-13 {
        font-size: 13px;
    }

    .fw-6 {
        font-weight: 600;
    }

    .whitespace-nowrap {
        white-space: nowrap;
    }

    .border-bottom {
        border-bottom: 1px solid black;
    }

    .border-right {
        border-right: 1px solid black;
    }

    .border-top {
        border-top: 1px solid black;
    }

    .border-left {
        border-left: 1px solid black;
    }

    .vertical-top {
        vertical-align: top;
    }

    .vertical-middle {
        vertical-align: middle;
    }

    .vertical-bottom {
        vertical-align: bottom;
    }

    .text-center {
        text-align: center;
    }

    .text-start {
        text-align: left;
    }

    .signature {
        max-width: 217px;
        height: 100px;
    }

    .item-table tr:last-of-type {
        font-size: 12px;
        height: 100%;
        vertical-align: top;
    }

    .text-end {
        text-align: right !important;
    }

    .px-0 {
        padding: auto 0 !important;
    }

    .w-50 {
        width: 50%;
    }

    .mb-0 {
        margin-bottom: 0 !important;
    }

    .m-0 {
        margin: 0 !important;
    }

    .border-top-gray {
        border-top: 1px solid #dde0e4;
    }

    .border-bottom-gray {
        border-bottom: 1px solid #dde0e4;
    }

    .table-collapsed tr th,
    .table-collapsed tr td {
        font-size: 12px;
        padding: 2px 5px !important;
    }
    .table-collapsed tbody tr th {
        vertical-align: top;
    }
    .table-collapsed thead tr td,
    .table-collapsed thead tr th,
    .table-collapsed tfoot tr td {
        padding: 3px 5px !important;
    }

    .text-start {
        text-align: left;
    }

    .mb-2 {
        margin-bottom: 6px !important;
    }

    .amount-text {
        padding: 0 15px !important;
    }

    .p-0 {
        padding: 0 !important;
    }

    .py-1 {
        padding: 2px auto !important;
    }

    .py-2 {
        padding: 4px auto !important;
    }

    .vertical-align-top {
        vertical-align: top !important;
    }

    .vertical-align-bottom {
        vertical-align: bottom !important;
    }

    @page {
        margin: 8px 20px !important;
        box-sizing: border-box;
    }

    .px-30 {
        padding: auto 30px !important;
    }

    .py-3 {
        padding: 15px auto !important;
    }

    .px-3 {
        padding: auto 15px !important;
    }

    .mb-20 {
        margin-bottom: 20px !important;
    }

    .fs-12 {
        font-size: 12px;
    }

    .text-primary {
        color: #4f158c;
    }

    .text-gray {
        color: #3f4254 !important;
    }

    .fs-14 {
        font-size: 14px;
    }

    .fw-bold {
        font-weight: bold;
    }

    body {
        font-family: "Poppins", sans-serif !important;
        color: #181c32;
        font-weight: 400;
    }

    .text-orange {
        color: #f76600 !important;
    }

    .logo {
        height: 40px;
        margin-left: auto;
        padding: 0 30px;
    }

    .logo img {
        height: 100%;
        max-width: 100%;
    }

    .text-center {
        text-align: center;
    }

    .px-2 {
        padding: 0 4px !important;
    }

    .mb-3 {
        margin-bottom: 15px !important;
    }

    p {
        margin: 0;
    }

    .custom-font-family {
        font-family: DejaVu Sans, Poppins, "Helvetica", Arial, "Liberation Sans",
            sans-serif !important;
    }

    .bg-light {
        background-color: #f5f8fa;
    }

    .bg-primary-light {
        background-color: #f6f3f9;
    }

    .bg-primary {
        background-color: #4f158c;
    }

    .text-white {
        color: #ffffff !important;
    }

    .table-collapsed {
        border-collapse: collapse;
    }

    .authorized-signature {
        width: 170px;
    }

    .ms-auto {
        margin-left: auto;
    }

    .br-5 {
        border-radius: 5px;
    }

    .overflow-hidden {
        overflow: hidden;
    }

    .p-10 {
        padding: 10px !important;
    }

    .position-absolute {
        position: absolute;
    }

    .bottom-0 {
        bottom: 0;
    }

    .w-100 {
        width: 100%;
    }

    .pb-1 {
        padding-bottom: 2px !important;
    }

    .mb-1 {
        margin-bottom: 4px !important;
    }
}
.fixTableHead {
    min-width: fit-content;
    thead {
        th {
            vertical-align: middle;
            &:first-child {
                width: 70px;
                min-width: 70px;
            }
            &:nth-child(2) {
                width: 260px;
                min-width: 260px;
            }
            &:nth-child(4) {
                width: 110px;
                min-width: 110px;
            }
            &:nth-child(5),
            &:nth-child(6) {
                width: 120px;
                min-width: 120px;
            }
            &:nth-child(3),
            &:nth-child(7) {
                width: 130px;
                min-width: 130px;
            }
            &:last-child {
                width: 60px;
                min-width: 60px;
            }
        }
    }
    &.table-wrapper {
        // max-height: 535px;
        max-height: 70vh;
        overflow-y: auto;
        min-width: fit-content;
        padding-right: 0;
        scrollbar-width: none !important;
        tbody {
            tr {
                td {
                    vertical-align: middle;
                    &:first-child {
                        width: 70px;
                        min-width: 70px;
                    }
                    &:nth-child(2) {
                        width: 260px;
                        min-width: 260px;
                    }
                    &:nth-child(4) {
                        width: 110px;
                        min-width: 110px;
                        max-width: 110px;
                    }
                    &:nth-child(5),
                    &:nth-child(6) {
                        width: 120px;
                        min-width: 120px;
                    }
                    &:nth-child(3),
                    &:nth-child(7) {
                        width: 130px;
                        min-width: 130px;
                    }
                    &:last-child {
                        width: 60px;
                        min-width: 60px;
                    }
                }
            }
        }
    }
}
.notes {
    width: fit-content;
    transform: all 0.3s ease-in;
    &:hover {
        box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2);
    }
}
.custom-input-group
    > :not(:first-child):not(.dropdown-menu):not(.valid-tooltip):not(
        .valid-feedback
    ):not(.invalid-tooltip):not(.invalid-feedback) {
    margin-left: 0px !important;
}
.right-0 {
    right: 0 !important;
}
.custom-input-group {
    .custom-select2-width {
        width: calc(100% - 37px) !important;
        @media (max-width: 768px) {
            width: calc(100% - 34px) !important;
        }
    }
}

.notification-button {
    span {
        top: 2px !important;
        right: -10px;
    }
}
.notification-button.start-animation {
    animation: notification 1.5s ease;
}

@keyframes notification {
    30% {
        transform: scale(1);
    }
    40%,
    60% {
        transform: rotate(-15deg) scale(1);
    }
    50% {
        transform: rotate(15deg) scale(1);
    }
    70% {
        transform: rotate(0deg) scale(1);
    }
    100% {
        transform: scale(0.8);
    }
}
.export img {
    width: 26px;
    position: absolute;
    top: 50px;
    left: 15%;
    display: none;
    z-index: 1000;
}
.shipping-name-gstin {
    margin-bottom: 6px !important;
}
.custom-input {
    .select2 {
        max-width: calc(100% - 39px) !important;
    }
}
.primary-unit,
.secondary-unit {
    @media (max-width: 575px) {
        width: 36%;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
}
.payment-reminder-modal {
    .close-btn {
        width: 24px;
        height: 24px;
        min-width: 24px;
        border-radius: 50%;
        border: 1px solid #f76600 !important;
    }
    .modal-dialog {
        @media (min-width: 576px) {
            width: calc(100% - 30px);
            max-width: 760px;
        }
        @media (min-width: 992px) {
            max-width: 800px;
        }
        .modal-content {
            border-radius: 20px;
        }
    }
    .timepicker-btn {
        position: absolute;
        right: 0;
        top: 0;
        height: 100%;
        padding: 5px 10px !important;
        border-left: 1px solid #6f6f6f !important;
        border-radius: 0;
    }
    .table-wrapper {
        border: 1px solid gray;
        border-radius: 12px;
        .reminder-table {
            thead {
                th {
                    padding: 10px 15px;
                    background-color: #4f158c;
                    color: white;
                    border-right: 1px solid gray;
                    vertical-align: middle;
                    &:first-of-type {
                        width: 15%;
                        border-top-left-radius: 10px;
                    }
                    &:last-of-type {
                        width: 15%;
                        border-top-right-radius: 10px;
                        border-right: 0;
                    }
                }
            }
            tbody {
                td {
                    font-weight: 500;
                    padding: 10px 15px;
                    border-right: 1px solid gray;
                    vertical-align: middle;
                    &:last-of-type {
                        border-right: 0;
                    }
                }
                .number-input {
                    width: 45px;
                    padding: 3px 8px;
                    color: #f76601;
                    border-radius: 8px;
                    font-weight: 600;
                    text-align: left;
                }
            }
        }
    }
}
.image-input {
    .image-input-outline {
        .image-input-wrapper {
            border: 1px solid !important;
            box-shadow: 0 0.1rem 1rem 0.25rem rgba(0, 0, 0, 0.05);
        }
    }
}

.edit-company-checkbox {
    width: 18px;
    height: 18px;
    &:checked[type="checkbox"] {
        background-size: 12px;
    }
}
    .start-date-img {
        position: absolute;
        width: 15px;
        height: 22px;
        right: 12px;
        top: 0;
        bottom: 0;
        margin: auto;
        background-color: white;
    }
    .edit-floating-input {
        height: 40px !important;
        background-color: white !important;
        &:focus {
            box-shadow: none !important;
            border-color: #4f158c !important;
        }
    }
    .edit-form-floating .edit-floating-textarea {
        background-color: white !important;
        &:focus {
            box-shadow: none !important;
            border-color: #4f158c !important;
        }
    }
    .edit-form-floating>.form-control.edit-floating-textarea:focus~label,
    .edit-form-floating>.form-control.edit-floating-textarea:not(:placeholder-shown)~label {
        top: -9px !important;
        font-size: 14px !important;
        color: #4f158c !important;
        background-color: #ffffff;
        opacity: 1;
        border-radius: 0.475rem !important;
        transform: translateY(-0.5rem) translateX(0.15rem);
    }
    .edit-form-floating .form-control.custom-floating-bg:not(:placeholder-shown) ~ .form-label,
    .edit-form-floating > .form-select.custom-floating-bg ~ label,
    .edit-form-floating .form-control.custom-floating-bg:focus~label {
        background-color: #F5F8FA !important;
    }

    .textarea-label {
        left: 40px;
    }
    .edit-form-floating > .form-control.edit-floating-input:focus ~ label,
    .edit-form-floating
        > .form-control.edit-floating-input:not(:placeholder-shown)
        ~ label,
    .edit-form-floating > .form-select.edit-floating-input ~ label {
        top: -6px !important;
        font-size: 14px !important;
        color: #4f158c !important;
        background-color: #ffffff;
        opacity: 1;
        border-radius: 0.475rem !important;
        transform: translateY(-0.5rem) translateX(0.15rem);
    }
    .edit-form-floating > .phone-no-label {
        top: -6px !important;
        font-size: 14px !important;
        color: #4f158c !important;
        background-color: #ffffff;
        opacity: 1;
        transform: translateY(-0.5rem) translateX(0.15rem);
    }
    .edit-form-floating > .rate-label {
        left: 52px;
    }
    .edit-form-floating > .rate-symbol{
        width: 50px;
    }
    .edit-form-floating > label {
        position: absolute;
        height: auto;
        left: 8px;
        top: 7px;
        color: #73757d;
        pointer-events: none;
        background-color: #ffffff;
        font-size: 14px !important;
        padding-top: 0 !important;
        padding-bottom: 0 !important;
        margin-bottom: 0 !important;
        transition: top 0.3s ease-in-out, font-size 0.3s ease-in-out,
            color 0.3s ease-in-out;
    }
    .edit-form-floating > .form-control.edit-floating-input:focus,
    .edit-form-floating
        > .form-control.edit-floating-input:not(:placeholder-shown),
    .edit-form-floating .form-select.edit-floating-input {
        padding-top: 0.625rem;
        box-shadow: none !important;
        border-color: #4f158c !important;
    }
    .select2-container--bootstrap5.select2-container--focus
        .form-select:not(.form-select-solid):not(.form-select-transparent),
    .select2-container--bootstrap5.select2-container--open
        .form-select:not(.form-select-solid):not(.form-select-transparent) {
        box-shadow: none !important;
        border-color: #4f158c !important;
    }

.edit-form-floating > .form-control.edit-floating-input:focus ~ label,
.edit-form-floating
    > .form-control.edit-floating-input:not(:placeholder-shown)
    ~ label,
.edit-form-floating > .form-select.edit-floating-input ~ label {
    top: -6px !important;
    font-size: 14px !important;
    color: #4f158c !important;
    background-color: #ffffff;
    opacity: 1;
    transform: translateY(-0.5rem) translateX(0.15rem);
}

.edit-form-floating > .phone-no-label {
    top: -6px !important;
    font-size: 14px !important;
    color: #4f158c !important;
    background-color: #ffffff;
    opacity: 1;
    transform: translateY(-0.5rem) translateX(0.15rem);
}
.edit-form-floating > label {
    position: absolute;
    height: auto;
    left: 8px;
    top: 7px;
    color: #73757d;
    pointer-events: none;
    background-color: #ffffff;
    font-size: 14px !important;
    padding-top: 0 !important;
    padding-bottom: 0 !important;
    margin-bottom: 0 !important;
    transition: top 0.3s ease-in-out, font-size 0.3s ease-in-out,
        color 0.3s ease-in-out;
}
.edit-form-floating > .form-control.edit-floating-input:focus,
.edit-form-floating > .form-control.edit-floating-input:not(:placeholder-shown),
.edit-form-floating .form-select.edit-floating-input {
    padding-top: 0.625rem;
    box-shadow: none !important;
    border-color: #4f158c !important;
}
.select2-container--bootstrap5.select2-container--focus
    .form-select:not(.form-select-solid):not(.form-select-transparent),
.select2-container--bootstrap5.select2-container--open
    .form-select:not(.form-select-solid):not(.form-select-transparent) {
    box-shadow: none !important;
    border-color: #4f158c !important;
}
// }
.edit-company-form {
    .image-preview {
        width: 120px;
        height: 120px;
        min-width: 120px;
        border: 1px solid #dbdce2 !important;
        background-color: #f6f6f6;
        display: flex;
        justify-content: center;
        align-items: center;
        position: relative;
        left: auto;
        top: auto;
        transform: none;
        border-radius: 10px;
    }
    .image-input {
        .image-preview {
            .change-img-btn {
                display: none;
            }
        }
        &.image-input-changed {
            .image-preview {
                .change-img-btn {
                    display: flex;
                }
            }
        }
    }
}

.custom-floating-input {
    height: 40px !important;
    background-color: white !important;
}
.form-floating > .form-control.custom-floating-input:focus ~ label,
.form-floating
    > .form-control.custom-floating-input:not(:placeholder-shown)
    ~ label,
.form-floating > .form-select.custom-floating-input ~ label {
    top: -6px;
    font-size: 14px;
    color: #007bff;
    background-color: #ffffff;
    opacity: 1;
    left: 8px;
}
.form-floating > .form-select.custom-floating-input ~ label {
    top: -4px !important;
}
.form-floating > label {
    position: absolute;
    height: auto;
    left: 8px;
    top: 8px;
    color: #777;
    z-index: 3;
    transition: top 0.3s ease, font-size 0.3s ease, color 0.3s ease;
}
.form-floating > .form-control.custom-floating-input:focus,
.form-floating > .form-control.custom-floating-input:not(:placeholder-shown),
.form-floating .form-select.custom-floating-input {
    padding-top: 0.625rem;
}

.phone-no-label {
    left: 86px !important;
}

.phone-no-label-focus {
    top: -12px !important;
    color: #181c32 !important;
    background-color: #ffffff;
    font-size: 12px !important;
    opacity: 1;
}

.iti__selected-flag {
    width: 80px;
    border-right: 1px solid #6f6f6f;
}

.iti__selected-flag-focused {
    border-right: 1px solid #4f158c !important;
}

.border-primary {
    border-color: #4f158c !important;
}

// .custom-livewire-table .bill-wise-profit-report-table td,
// .custom-livewire-table .bill-wise-profit-report-table th {
//     border: none !important;
// }

.custom-livewire-table .bill-wise-profit-report-table td:first-child {
    padding-left: 20px !important;
}
.custom-livewire-table .bill-wise-profit-report-table thead tr th input.search {
    width: calc(100% - 6px) !important;
    margin: 4px auto;
}

.custom-livewire-table .bill-wise-profit-report-table thead th:first-child div {
    border-top-left-radius: 5px !important;
}

.custom-livewire-table .bill-wise-profit-report-table thead th:last-child div {
    border-top-right-radius: 5px !important;
}
.bill-wise-profit-report-table {
    border: 1px solid rgb(79 21 140 / 50%);
    border-radius: 0.625rem;
    border-collapse: separate;
    border-spacing: 0;
}

.bill-wise-profit-report-table tbody tr {
    height: 25px !important;
}
.bill-wise-profit-report-table tbody tr td {
    padding: 5px 7px !important;
}

.bill-wise-profit-report-table .fixed-header th div {
    margin-left: 0 !important;
    margin-top: 0 !important;
    // background-color: #4F158C;
    border: none !important;
    color: #ffffff;
}

.customer-summary-report-table {
    .fixed-header tr:first-child .total{
        line-height: 16px;
        div {
            padding: 0px !important;
        }
    }
    tbody tr td {
        padding: 3px 5px !important;
    }
}

.customer-summary-report-table .fixed-header tr:nth-child(2) {
    border-bottom: 1px solid white !important;

    div {
        background-color: #4F158C;

        span:first-child {
            margin-left: 12px;
        }
    }
}

.customer-summary-report-table .fixed-header {
    tr:nth-child(3) th:first-child {
        border-left: 1px solid rgb(79 21 140 / 50%) !important;
    }

    tr:nth-child(3) th:last-child {
        border-right: 1px solid rgb(79 21 140 / 50%) !important;
    }

    tr:nth-child(2) th:nth-child(3) div {
        justify-content: center;
    }

    .customer-summary-report-search {
        background-color: white !important;

        .search-head {
            border-right: 1px solid #DDE0E4 !important;
        }
    }
}

.bill-wise-profit-report-table .fixed-header tr:first-child div {
    background-color: #4F158C;
}

.bill-wise-profit-report-table .fixed-header .bill-wise-profit-report-search {
    background-color: white !important;
    .search-head {
        border-right:1px solid #DDE0E4 !important ;
    }
}

.bill-wise-profit-report-table .fixed-header th.search-head:last-child {
    border-right: none !important;
}

.custom-livewire-table .bill-wise-profit-report-table tbody tr:nth-child(odd) {
    background-color: #f4edfa;
}

.custom-bg .bill-wise-profit-report-table tbody tr {
    background-color: #f4edfa;
}

.custom-livewire-table .bill-wise-profit-report-table thead tr:first-child th .total {
    margin-bottom: 7px !important;
    font-size: 11px !important;
}

.did-floating-select:not([value=""]):valid ~ .did-floating-label {
    top: -8px;
    font-size: 13px;
}
.did-floating-select[value=""]:focus ~ .did-floating-label {
    top: 11px;
    font-size: 13px;
}
.did-floating-select:not([multiple]):not([size]) {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='8' height='6' viewBox='0 0 8 6'%3E%3Cpath id='Path_1' data-name='Path 1' d='M371,294l4,6,4-6Z' transform='translate(-371 -294)' fill='%23003d71'/%3E%3C/svg%3E%0A");
    background-position: right 15px top 50%;
    background-repeat: no-repeat;
}
.did-floating-select:not([multiple]):not([size]) {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='8' height='6' viewBox='0 0 8 6'%3E%3Cpath id='Path_1' data-name='Path 1' d='M371,294l4,6,4-6Z' transform='translate(-371 -294)' fill='%239d3b3b'/%3E%3C/svg%3E%0A");
}
.input-group {
    display: flex;
    .did-floating-input {
        border-radius: 0 4px 4px 0;
        border-left: 0;
        padding-left: 0;
    }
}
.input-group-append {
    display: flex;
    align-items: center;
    /*   margin-left:-1px; */
}
.input-group-text {
    display: flex;
    align-items: center;
    font-weight: 400;
    color: #323840;
    padding: 0 5px 0 20px;
    font-size: 12px;
    text-align: center;
    white-space: nowrap;
    border: 1px solid #3d85d8;
    border-right: none;
}

input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
input:-webkit-autofill:active {
    transition: background-color 5000s ease-in-out 0s;
}

.sale-pdf-preview {
    max-height: 94vh;
    overflow: hidden;
    .modal-body {
        height: 94vh;
        overflow: auto;
    }
}

.custom-table-height {
    max-height: 600px;
    overflow-y: auto;
}

.companies-table .form-select {
    line-height: 0.7 !important;
}
.companies-table {
    .fixed-header {
        tr {
            th {
                div {
                    border-bottom: 1px solid #dde0e4 !important;
                }
            }
            .search-head {
                div {
                    min-height: 48px;
                }
            }
            th {
                &:nth-child(1) {
                    background-color: #fff;
                    position: sticky;
                    left: 0;
                    min-width: 259px;
                    z-index: 99;
                    div {
                        padding-left: 0 !important;
                    }
                }
                &:nth-child(2) {
                    background-color: #fff;
                    position: sticky;
                    left: 259px;
                    min-width: 237px;
                    z-index: 99;
                }
                &:nth-child(3) {
                    background-color: #fff;
                    position: sticky;
                    left: 496px;
                    min-width: 116px;
                    z-index: 99;
                }
                &:nth-child(4) {
                    background-color: #fff;
                    position: sticky;
                    left: 612px;
                    z-index: 99;
                    div {
                        border-right: 1px solid #dde0e4 !important;
                    }
                }
                &:nth-child(5) {
                    div {
                        border-left: 1px solid transparent !important;
                    }
                }
            }
        }
    }
    tbody {
        tr {
            td {
                border: none !important;
                border-width: 0 !important;
                padding: 0 !important;
                &:nth-child(1) {
                    background-color: #fff;
                    position: sticky;
                    left: 0;
                    min-width: 259px;
                    div {
                        border-left: 1px solid transparent !important;
                        margin-left: 0 !important;
                    }
                }
                &:nth-child(2) {
                    background-color: #fff;
                    position: sticky;
                    left: 259px;
                    min-width: 237px;
                }
                &:nth-child(3) {
                    background-color: #fff;
                    position: sticky;
                    left: 496px;
                    min-width: 116px;
                }
                &:nth-child(4) {
                    background-color: #fff;
                    position: sticky;
                    left: 612px;
                    div {
                        border-right: 1px solid #dde0e4 !important;
                    }
                }
                &:nth-child(5) {
                    div {
                        border-left: 1px solid transparent !important;
                    }
                }
                div {
                    padding: 7px !important;
                    border-left: 1px solid #dde0e4;
                    border-right: 1px solid transparent;
                    border-bottom: 1px solid #dde0e4;
                    margin-left: -0.5px;
                    min-height: 38px;
                    height: 100%;
                }
                .select2-container {
                    position: static !important;
                }
            }
            &:first-child {
                td {
                    border-top: 0 !important;
                }
            }
            &:last-child {
                border-bottom: none !important;
            }
        }
    }
}
.append-import-price-list {
    .table {
        .fixed-header {
            .search-head {
                div {
                    height: 48px;
                }
            }
        }
    }
}
.custom-bill-wise-table {
    td,
    th {
        &:first-child {
            padding-left: 7px !important;
        }
        &:last-child {
            padding-right: 7px !important;
        }
    }
}

.subscription-livewire-table.sticky-action-column table tbody tr td:last-child,
.subscription-livewire-table.sticky-action-column table thead tr th:last-child {
    right: -0.5px !important;
    &::after {
        display: none !important;
    }
}

.subscription-table {
    .fixed-header {
        tr {
            th {
                div {
                    border-bottom: 1px solid #dde0e4 !important;
                }
            }
            > th:first-child {
                div {
                    // border-left: 0 !important;
                    padding-left: 0 !important;
                }
            }
            .search-head {
                div {
                    height: 48px;
                }
            }
        }
    }
    tbody {
        tr {
            td {
                border: none !important;
                border-width: 0 !important;
                padding: 0 !important;
                > div:first-child {
                    padding: 7px !important;
                    border-left: 1px solid #dde0e4;
                    border-right: 1px solid transparent;
                    border-bottom: 1px solid #dde0e4;
                    margin-left: -0.5px;
                    min-height: 36.27px;
                    height: 100%;
                    display: flex;
                    align-items: center;
                }
            }
            > td:first-child {
                div {
                    border-left: 0 !important;
                    padding-left: 0 !important;
                }
            }
            &:first-child {
                td {
                    border-top: 0 !important;
                }
            }
        }
    }
    .form-select {
        line-height: 0.7 !important;
    }
}

.text-decoration-underline {
    text-decoration: underline !important;
    &:hover {
        text-decoration: underline !important;
    }
}

.channel-partner-dashboard {
    .card-body {
        box-shadow: 0 0 10px #00000040;
        .overview-chart {
            max-width: 220px;
            width: 100%;
        }
        .fw-bold {
            font-weight: 600 !important;
        }
        .table {
            thead {
                border-bottom: 1px solid #d4d4d4;
                th {
                    color: #737373;
                }
            }
            tbody {
                td {
                    color: #404040;
                }
            }
            .indicator {
                width: 14px;
                min-width: 14px;
                height: 14px;
                border-radius: 50%;
            }
        }
        .summary-box {
            position: relative;
            &.last-child {
                &::after {
                    display: none;
                }
            }
            &::after {
                display: block;
                position: absolute;
                content: "";
                top: 0;
                right: 0;
                width: 1px;
                height: 100%;
                background-color: #4f148c12;
                box-shadow: 0 0px 4px #7d00ff80;
                @media (max-width: 575px) {
                    height: 1px;
                    width: 100%;
                    bottom: 0;
                    left: 0;
                    top: auto;
                    right: auto;
                }
            }
            .summary-icon {
                width: 37px;
                min-width: 37px;
                height: 37px;
                border-radius: 50%;
                display: flex;
                justify-content: center;
                align-items: center;
                &.bg-primary-light {
                    background-color: #dac4f3;
                }
                &.bg-orange-light {
                    background-color: #f6d3bb;
                }
                &.bg-green-light {
                    background-color: #9fe898;
                }
            }
            .text-success {
                color: #0b7e00 !important;
            }
            h3 {
                max-width: 120px;
                @media (max-width: 575px) {
                    max-width: 100%;
                }
            }
        }
    }
}

.border-left {
    border-left: 1px solid rgb(79 21 140 / 50%) !important;
}

.border-right {
    border-right: 1px solid rgb(79 21 140 / 50%) !important;

}

.bottom-border {
    border-left: 0 !important;
    border-right: 0 !important;
    border-bottom: 1px solid rgb(79 21 140 / 50%) !important;
    border-radius: 0.625rem !important;
}

.customer-summary-report-table {
    tbody {
        tr {
            &:last-child {
                td {
                    &:first-child {
                        &:first-child {
                            border-bottom-left-radius: .675rem !important;
                        }

                        &:last-child {
                            border-bottom-right-radius: .675rem !important;
                        }
                    }
                }
            }
        }
    }
}
.cheque {
    width: 1000px;
    height: 350px;
    background-color: #eaf4f8;
}
.cheque-date{
    border-collapse: collapse;
}
.cheque-date td {
    padding: 2px 1px;
    min-width: 21.13px;
}
.cheque-date-numbers {
    // padding-left:7px;
    letter-spacing: 17px;
    border:1px solid black;
    width:192px;
    position: relative;
    height: 24px;
    #text {
        width:21px;
    }
    span {
        position: absolute;
        width:1px;
        height:100%;
        top:0;
        background-color: #000;
    }
    :nth-child(1) {
        left:21px;
    }
    :nth-child(2) {
        left:45px;
    }
    :nth-child(3) {
        left:69px;
    }
    :nth-child(4) {
        left:92px;
    }
    :nth-child(5) {
        left:115px;
    }
    :nth-child(6) {
        left:141px;
    }
    :nth-child(7) {
        left:166px;
    }
}

.cheque-date tr:first-child td {
    border: 1px solid black;
}
.fs-12px {
    font-size: 12px;
    line-height: 12px;
}
.fs-14px {
    font-size: 14.4px;
}
.cheque-title {
    padding-top: 19px !important;
    padding: 0px 34px 0px 30px;
}
.gap-10px {
    gap: 10px;
}
.cheque-pay {
    height: 26px;
    border-bottom: 1px solid black;
    margin: 0px 34px 0px 30px;
}
.cheque-rupees {
    height: 34px;
    border-bottom: 1px solid black;
    margin: 0px 34px 0px 30px;
}
.cheque-amount {
    width: 185px;
    border: 1px solid black;
}
.cheque-ruppes-icon {
    border-right: 1px solid black;
}
.cheque-amount-no {
    width: 238px;
    border: 1px solid black;
    height: 40px;
}
.cheque-number {
    height: 60px;
}
.cheque-pay-name {
    border: none !important;
    background-color: transparent !important;
    padding: 0 !important;
    &.form-control{
        &:focus , &:hover , &:active, &:read-only{
            box-shadow: none !important;
            outline: none !important;
            border: none !important;
            background-color: transparent !important
        }
    }
}
.cheque-name-custom{
flex: 1;
}
.cheque-edit-btn{
    border: none !important;
    background-color: transparent !important;
    i{
        margin-bottom: 0 !important;
    }
    &:focus , &:hover , &:active{
        box-shadow: none !important;
        outline: none !important;
        border: none !important;
    }
}
.font-micr {
    font-family: "micrenc-font";
}
.cheque-sign-section {
    margin: 0px 34px 0px 30px;
}
.cheque-amount-section {
    margin: 0px 34px 0px 30px;
}
.cheque-sign {
    height: 40px;
    max-width: 200px;
}

.swal-custom-confirm {
    border-radius: 50rem !important;
    width: 120px;
    height: 55px;
    font-size: large !important;
}
.input-with-select {
    border: none !important;
    background-color: #F5F8FA !important;
    border-radius: 0 !important;

    &.form-select {
        &:focus {
            box-shadow: none !important;
        }
    }

    &:focus,
    &:active,
    &:hover {
        box-shadow: none !important;
        outline: none !important;
    }
}

.z-3 {
    z-index: 3 !important;
}

.bar-code-radius {
    border-radius: 0 0 0 0 !important;
}
.content.item-master-content {
    padding-bottom:0 !important;
}
.box-last{
    box-shadow: 0px 0px 30px 0px #00000040;
    padding: 15px 32px;
    position: sticky;
    width:100%;
    left: 0;
    right: 0;
    // right:30px;
    border-radius: 10px 10px 0px 0px;
}
.fixed-btn-gray{
    background-color:#DDE0E4 ;
    padding: 11px 20px;
    border-radius: 7px;
    border: none !important;
    &:focus , &:active , &:hover{
        box-shadow: none !important;
        outline: none !important;
    }
}
.fixed-btn{
    padding: 11px 20px;
    border-radius: 7px;
    border: none !important;
    &:focus , &:active , &:hover{
        box-shadow: none !important;
        outline: none !important;
    }
}
.gap-6px{
    gap: 6px;
}
.mb-30{
    margin-bottom: 30px;
}

.op-stock-border-radius-y-zero{
    border-radius: 0 0.475rem 0.475rem 0 !important;
}

.border-radius-auto{
    border-radius: 0.475rem 0 0 0.475rem 0 !important;
}

.border-radius-y-zero{
    border-radius: 0.475rem !important;
}

.item-master-card .card-body{
    padding: 0.55rem 1rem;
}

.audit-trail-table{
    b {
        font-weight: 800 !important;
    }
}
.sidebar-content {

    :nth-child(1) {
   width:100%;

//             .menu-link.me-5 {
// margin-right:0 !important;
//             }
        }

}

.landscape-image {
    .pdf-image {
        width: 160px !important;
        max-width: 160px !important;
    }

}

.landscape2-preview {
    .main {
        border: 2px solid black;
        display: flex;
        flex-direction: column;
        padding: 10px;
    }
    p {
        margin-bottom: 0 !important;
    }
    address {
        margin-bottom: 0 !important;
    }
    .fw-semibold {
        font-weight: 600 !important;
    }
    .fw-medium {
        font-weight: 500;
    }
    .text-black {
        color: black !important;
    }

    .text-end {
        text-align: end !important;
    }

    .text-start {
        text-align: start !important;
    }

    .text-center {
        text-align: center !important;
    }

    .mb-2px {
        margin-bottom: 2px;
    }

    .w-50 {
        width: 50%;
    }

    .w-30 {
        width: 30%;
    }

    .w-100 {
        width: 100% !important;
    }

    .w-20px {
        min-width: 20px;
    }

    .mb-2px {
        margin-bottom: 2px;
    }

    .h-100 {
        height: 100%;
    }

    .fs-14 {
        font-size: 14px;
    }

    .py-1 {
        padding-top: 4px;
        padding-bottom: 4px;
    }

    .fs-13 {
        font-size: 13px;
    }

    .fs-12 {
        font-size: 12px;
    }

    .d-flex {
        display: flex;
    }

    .align-items-center {
        align-items: center !important;
    }

    .logo-img {
        width: 80px !important;
        height: 80px !important;
        min-width: 80px !important;
        border-radius: 50% !important;
        min-height: 80px !important;
    }

    .object-fit-cover {
        object-fit: cover !important;
    }

    .lh-1-5 {
        line-height: 1.5 !important;
    }

    .text-nowrap {
        white-space: nowrap !important;
    }

    .break-text {
        word-break: break-all !important;
    }

    .fs-10 {
        font-size: 10px;
    }

    .fs-11 {
        font-size: 11px;
    }

    .py-2 {
        padding-top: 8px;
        padding-bottom: 8px;
    }

    .text-decoration-none {
        text-decoration: none;
    }


    .w-25 {
        width: 25%;
    }

    .ps-3 {
        padding-left: 16px;
    }

    table {
        border-collapse: collapse;
    }

    .tax-name {
        border: 1px solid black;
    }

    .border-bottom {
        border-bottom: 1px solid black;
    }

    .table-intro td,
    .bill-table td,
    .sn-table td,
    .qr-table td {
        border-right: 1px solid black;
        border-bottom: 1px solid black;
        border-left: 1px solid black;
    }

    .sn-table th,
    .tax-table th {
        border-right: 1px solid black;
        border-bottom: 1px solid black;
    }

    .sn-table th:last-child {
        border-right: 1px solid black;
    }

    .sn-table th:first-child,
    .tax-table th:first-child {
        border-left: 1px solid black;
    }

    .qr-table th:last-child,
    .tax-table td:last-child {
        border-right: none;
    }

    .tax-table td:first-child {
        border-left: none;
    }

    .tax-table tbody td {
        border-bottom: none;
    }

    .tax-table tfoot td {
        border-top: 1px solid black;
    }

    .bank-table td {
        border: none !important;
    }

    .po-table td {
        border: none !important;
    }

    .gap-2 {
        gap: 8px !important;
    }

    address {
        font-style: normal !important;
    }

    .vertical-align-top {
        vertical-align: top !important;
    }

    .bg-primary {
        background-color: #f46703 !important;
    }

    .text-primary {
        color: #f46703 !important;
    }

    .text-white {
        color: white !important;
    }

    .px-12px {
        padding-left: 16px;
        padding-right: 16px;
    }

    .px-2 {
        padding-left: 8px;
        padding-right: 8px;
    }

    .mb-1 {
        margin-bottom: 4px;
    }

    .qr-img {
        width: 75px !important;
        max-height: 75px !important;
        height: 75px !important;
        min-width: 75px !important;
    }

    .px-1 {
        padding-left: 4px;
        padding-right: 4px;
    }

    .gap-3 {
        gap: 16px;
    }

    .justify-content-between {
        justify-content: space-between;
    }

    .sign {
        background-color: #f6f6f6 !important;
        height: 70px !important;
        width: 130px !important;
        justify-content: center !important;
    }

    .w-102px {
        min-width: 102px !important;
    }

    .ms-auto {
        margin-left: auto;
    }

    .fs-15 {
        font-size: 15px;
    }

    .mb-3 {
        margin-bottom: 16px;
    }

    .vertical-align-bottom {
        vertical-align: bottom !important;
    }

    .py-3 {
        padding-top: 16px;
        padding-bottom: 16px;
    }

    .pb-1 {
        padding-bottom: 4px;
    }

    .flex-grow-1 {
        flex-grow: 1;
    }

    .sn-table tbody tr:last-child td {
        height: 100% !important;
    }

    table {
        display: table !important;
        width: 100% !important;
        max-width: 100% !important;
    }

    tr {
        display: table-row !important;
    }

    th,
    td {
        display: table-cell !important;
    }

    .pb-2px {
        padding-bottom: 2px;
    }

    .w-22 {
        width: 22%;
    }

    .w-28 {
        width: 28%;
    }

    .ps-1 {
        padding-left: 4px;
    }

    .pe-3 {
        padding-right: 12px;
    }

    .px-12px {
        padding-left: 12px;
        padding-right: 12px;
    }

    .flex-wrap {
        flex-wrap: wrap
    }

    .row-gap-1 {
        row-gap: 2px;
    }

    .colum-gap-2 {
        column-gap: 10px;
    }

    .invoice-type {
        border-top: 1px solid black;
        border-left: 1px solid black;
        border-right: 1px solid black;
    }
    .border-bottom-none  {
        tfoot{
            tr{
                td{
                    border-bottom: none !important;
                }
            }
        }
    }
}

.portrait-pdf-ui-7 {
    border: 1px solid #000 !important;
    .main-container {
        min-height: 100vh !important;
        display: flex !important;
        flex-direction: column !important;
    }

    .d-flex {
        display: flex !important;
    }

    .justify-content-center {
        justify-content: center !important;
    }

    .align-items-center {
        align-items: center !important;
    }

    .w-100 {
        width: 100% !important;
    }

    .h-100 {
        height: 100% !important;
    }

    table {
        max-width: 100% !important;
        width: 100% !important;
        border-collapse: collapse !important;
        display: table !important;
    }

    table td,
    table th {
        display: table-cell !important;
        vertical-align: top !important;
    }

    .lh-1-5 {
        line-height: 1.5 !important;
    }

    .fs-10 {
        font-size: 10px !important;
    }

    .fs-11 {
        font-size: 11px !important;
    }

    .fs-12 {
        font-size: 12px !important;
    }

    .fs-13 {
        font-size: 13px !important;
    }

    .fs-14 {
        font-size: 14px !important;
    }

    .text-black {
        color: black !important;
    }

    .bg-black {
        background-color: black !important;
    }

    .text-white {
        color: white !important;
    }

    .text-nowrap {
        white-space: nowrap !important;
    }

    .jusstify-content-center {
        jusstify-content: center !important;
    }

    .break-text {
        word-break: break-all !important;
    }

    .justify-content-between {
        justify-content: space-between !important;
    }

    .fw-semibold {
        font-weight: 600 !important;
    }

    .text-end {
        text-align: end !important;
    }

    .text-center {
        text-align: center !important;
    }

    .ms-auto {
        margin-left: auto !important;
    }

    .text-start {
        text-align: start !important;
    }

    .ls-1px {
        letter-spacing: 1px !important;
    }

    .w-50 {
        width: 50%
    }

    .px-4 {
        padding-left: 20px !important;
        padding-right: 20px !important;
    }

    .fs-24 {
        font-size: 24px !important;
    }

    .fs-22 {
        font-size: 24px !important;
    }

    .pt-3 {
        padding-top: 16px !important;
    }

    .pt-2 {
        padding-top: 8px !important;
    }

    .pb-1 {
        padding-bottom: 4px !important;
    }

    address {
        font-style: normal !important;
        margin-bottom: 0px !important;
    }

    .text-decoration-none {
        text-decoration: none !important;
    }

    .logo-img {
        max-width: 170px !important;
        max-height: 90px !important;
        height: 90px !important;
    }

    .object-fit-cover {
        object-fit: cover !important;
    }

    .w-33 {
        width: 33.33% !important;
    }

    .pe-1 {
        padding-right: 4px !important;
    }

    .ps-4 {
        padding-left: 22px !important;
    }

    .pe-4 {
        padding-right: 22px !important;
    }

    .mb-4 {
        margin-bottom: 24px !important;
    }

    .py-1 {
        padding-top: 4px !important;
        padding-bottom: 4px !important;
    }

    .py-2 {
        padding-top: 8px !important;
        padding-bottom: 8px !important;
    }

    .px-3 {
        padding-left: 16px !important;
        padding-right: 16px !important;
    }

    .px-22px {
        padding-left: 22px !important;
        padding-right: 22px !important;
    }

    .px-2 {
        padding-left: 8px !important;
        padding-right: 8px !important;
    }

    .mb-3 {
        margin-bottom: 16px !important;
    }

    .sn-table tbody td {
        border-right: 1px solid #e2e2e2 !important;
    }

    .sn-table tbody td:last-child {
        border-right: none !important;
    }

    .flex-grow-1 {
        flex-grow: 1 !important;
    }

    .sn-table tbody tr:nth-last-child(2) td {
        height: 100% !important;
    }

    .flex-column {
        flex-direction: column !important;
    }

    .mb-1 {
        margin-bottom: 4px !important;
    }

    .qr-img {
        width: 75px !important;
        min-width: 75px !important;
        max-height: 75px !important;
    }

    .gap-20px {
        gap: 20px !important;
    }

    .sign {
        height: 120px !important;
        width: 180px !important;
        justify-content: center  !important;
        overflow: hidden !important;
    }

    .mt-2 {
        margin-top: 8px !important;
    }

    .mb-2 {
        margin-bottom: 8px !important;
    }

    .mt-3 {
        margin-top: 16px !important;
    }

    .pe-2 {
        padding-right: 8px !important;
    }

    .mx-auto {
        margin: 0 auto !important;
    }

    .w-120px {
        min-width: 90px !important;
    }

    .px-12px {
        padding-left: 12px !important;
        padding-right: 12px !important;
    }

    .w-30px {
        min-width: 30px !important;
    }

    .fw-medium {
        font-weight: 500 !important;
    }

    p{
        margin-bottom: 0px !important;
    }
}
.letter-head-table {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    width: 100%;
    box-sizing: border-box;
    box-shadow: 0 0 12px #00000026;
}
.main-content-table {
    border: 1px solid black;
    margin-left: 28px;
    margin-right: 28px;
    margin-top: 130px;
    margin-bottom: 22px;
}

.transaction-dropdown-menu {
    position: absolute;
    inset: auto 0px 0px auto !important;
    margin: 0px;
    transform: translate(-84px, 10px) !important;
}
.custom-livewire-table {
    &.sticky-action-column {
        table {
            tbody {
                :nth-child(1) {
                    td:last-of-type {
                        .custom-transaction-menu {
                            transform: translate(-84px, 52px) !important;
                        }
                        .e-way-bill-menu {
                            transform: translate(-35px, 14px) !important;
                        }
                    }
                }
                :nth-child(2) {
                    td:last-of-type {
                        .custom-transaction-menu {
                            transform: translate(-84px, 10px) !important;
                        }
                        .e-way-bill-menu {
                            transform: translate(-35px, 10px) !important;
                        }
                    }
                }
                :nth-child(3) {
                    td:last-of-type {
                        .custom-transaction-menu {
                            transform: translate(-84px, 42px) !important;
                        }
                        .e-way-bill-menu {
                            transform: translate(-35px, 10px) !important;
                        }
                    }
                }
            }
        }
    }
}
.item-master-group-table table tbody tr {
    height: 41.5px !important;
}
.bottom-fixed {
    bottom: 20px !important;
    top: auto !important;
}
.btn-sucess-border  {
    background-color: transparent !important;
    // border:1px solid #50CD89 !important;
    border: none !important;
    color: #50CD89 !important;
    text-align: left;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%2350CD89' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e");
    &:hover{
        background-color: #50CD89 !important;
        color: white !important;
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23ffffff' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e");
    }

    &.dropdown-toggle{
        &::after {
            display:none !important
        }
    }
}
.btn-danger-border  {
    background-color: transparent !important;
    // border:1px solid #F1416C !important;
    border: none !important;
    color: #F1416C !important;
    text-align: left;
    padding: 5px 12px !important;
    &:hover{
        background-color: #F1416C !important;
        color: white !important;
    }
    // &.dropdown-toggle{
    //     &::after {
    //         display:none !important
    //     }
    // }
}
.gst-summary-view{
    .table-border-top{
        border-top: 1px solid #a3a5ad !important;
        border: 1px solid #ebedf2 !important;
    }
    .table-bordered{
        border: 1px solid #ebedf2 !important;
    }
}
.upload-status-content {
    max-width:370px;
    .form-check {
        &.checked {
            background-color: #f7efff;
        }
    }
    .form-check-input {
        border-radius:50% !important;
        width:16px !important;
        height:16px !important;
        opacity: 1 !important;
        &:focus {
            box-shadow:none !important;
        }
        &:disabled ~ .form-check-label {
            opacity:1 !important;
            cursor: default !important;
        }
    }
}
.datepicker-months{
    .table-condensed{
        tbody{
            tr{
                font-size: 13px;
            }
        }
    }
}
// outstanding-main-table
.outstanding-main-table {
    table {
        thead {
            tr {
                th {
                    font-size: 12px !important;
                }
            }
        }
        tbody {
            tr {
                height: 25px !important;
                td {
                    font-size: 11px !important;
                    padding: 0px 4px !important;
                    line-height: normal !important;
                }
                .outstanding_rate_of_interest{
                    font-size: 11px !important;
                    line-height: normal !important;
                    padding: 0px 4px !important;
                    height: 20px !important;
                }
            }
        }
        .party-wise-invoice-tbody-3 {
            tr {
                td {
                    min-width: 130px !important;
                }
            }
        }
    }
}
.select2-dropdown {
    z-index: 9999999999 !important;
}
.new-badge {
    position: absolute;
    top: -8px;
    right: -5px;
    padding: 1px 6px;
    font-size: 10px;
    font-weight: 500;
    background-color: #f1416c;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #fff;
}
.badge-container {
    a {
        &:after {
            display:none !important;
        }
    }
}
.gap-y-3 {
    row-gap: 0.75rem !important;
}
@keyframes rotate360 {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

.rotate-animation {
    animation: rotate360 0.8s linear infinite;
}

.disable-label {
    background-color: #EFF2F5 !important;
}

.broker-report-page {
    .card {
        font-size: 12px;
    }
    .custom-livewire-table {
        table {
            thead {
                tr {
                    th {
                        font-size: 12px;
                        padding: 5px;
                    }
                    &:first-child {
                        th {
                            padding: 5px;
                        }
                        .total {
                            font-size: 10px;
                            padding: 3px;
                        }
                    }
                }
            }
            tbody {
                tr {
                    height: 36.38px;
                    td {
                        font-size: 12px;
                        padding: 5px;
                    }
                }
            }
        }
    }
    .select2-container--bootstrap5 .select2-selection--single .select2-selection__rendered {
        font-size: 12px;
    }
    .date-input-div {
        min-width: 190px;
    }
    .form-control {
        font-size: 12px;
    }
    .select2-container--bootstrap5
        .select2-selection--multiple:not(.form-select-sm):not(.form-select-lg)
        .select2-selection__choice
        .select2-selection__choice__display {
        font-size: 10px;
    }
}

.eway-bill-create-page {
    .card {
        font-size: 12px;

        .form-label {
            font-size: 12px !important;
        }
        .form-control {
            font-size: 12px !important;
        }
    }
    .select2-container--bootstrap5 .select2-selection--single .select2-selection__rendered {
        font-size: 12px !important;
    }
    .select2-container--bootstrap5
        .select2-selection--multiple:not(.form-select-sm):not(.form-select-lg)
        .select2-selection__choice
        .select2-selection__choice__display {
        font-size: 10px;
    }
}

@keyframes rotate360 {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

.rotate-animation {
    animation: rotate360 0.6s linear infinite;
}

.gst-dashboard-table {
     thead {
            tr {
                th {
                    font-size: 12px !important;
                }
            }
        }
    tbody {
        tr {
            td:first-child {
                padding-left: 0 !important;
            }
                height: 25px !important;
                td {
                    font-size: 12px !important;
                    padding: 0px 4px !important;
                    line-height: 1 !important;
                }
        }
    }
}
.row-gap-16px{
    row-gap: 16px;
}
.flex-nowrap-main-screen{
    @media (min-width: 1600px) {
        flex-wrap: nowrap !important;
    }
}
.login-gst-portal-modal{
        .modal-dialog{
        position: fixed !important;
        right: 0 !important;
        max-width: 100% !important;
        height: 100% !important;
        margin: 0 !important;
        width: 560px !important;
        .modal-content{
            border-radius: 10px 0px 0px 10px !important;
        }
        .offcanvas-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 16px 25px;
            color: #000000;
        }
        .close-button {
            background: none;
            border: none;
            font-size: 30px;
            line-height: 1;
            color: #8e92a1 !important;
            cursor: pointer;
        }
        .modal-body{
            background-color: #F5F8FA !important;
            .gstr-card-body{
                background-color: white !important;
                padding: 28px 20px 20px 20px !important;
                border-radius: 10px !important;
            }
        }
        }
}
.filing-status-box{
    background-color: #F5F8FA !important;
    padding: 25px !important;
    width: 400px;
    border-radius: 10px !important;
    .step-1 {
        width: 24px;
        height: 24px;
        border-radius: 50%;
        background-color: #00000061 !important;
        color: white;
        display: flex;
        justify-content: center;
        align-items: center;
        font-weight: 600 !important;
    }
    .step-2{
        width: 24px;
        height: 24px;
    }
    .step-3{
        width: 24px;
        height: 24px;
        border-radius: 50%;
        background-color: #4F158C !important;
        display: flex;
        justify-content: center;
        align-items: center;
        img{
            width: 15px;
            height: 11px;
        }
    }
    .filing-content{
        .filing-icons{
            height: 53px;
            &::after{
                content: "";
                position: absolute;
                bottom: 0;
                left: 0;
                right: 0;
                margin: auto;
                width: 2px;
                height: 23px;
                background-color: #BDBDBD;
            }
        }
    }
}
.alert-box-gstr{
    background-color: #FFF4E5 !important;
    border: 1px solid #D65907 !important;
    padding: 12px 16px !important;
    color: #663C00 !important;
    border-radius: 4px !important;
}
.btn-green-gstr{
    border: 1px solid #02542D !important;
    background-color: #14AE5C !important;
    color: white !important;
    font-size: 16px;
    padding: 5px 20px 5px 10px !important;
}
.gst-error-btn{
    background-color: #FCB3AD;
    color: black;
    padding: 8px 15px;
    margin-left: 10px;
}
.item-master-header{
    vertical-align: middle;
    &.Selling , &.Purchase {
        max-width: 130px !important;
        min-width:130px !important;
        span{
            &:first-child{
                white-space: normal;
            }
        }
    }
}

 .sync-box {

            text-align: center;
        }
        #syncBtn {
            background: #28a745;
            color: white;
            border: none;
            cursor: pointer;
            font-size: 12px;
            transition: background 0.3s, transform 0.2s;
        }
        #syncBtn.running {
            background-color: #ffffff;
            border: 1px solid #8E92A2;
            font-weight: 500;
            color: black;
        }
        #syncBtn.completed {
            background:#551a94;
            color: white;
        }
        .spin {
            display: inline-block;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            50% { transform: rotate(180deg); }
            100% { transform: rotate(360deg); }
        }
        #message {
            margin-top: 15px;
            font-size: 14px;
            color: #555;
        }
