const { set } = require("lodash");
const { list } = require("postcss");

document.addEventListener('DOMContentLoaded', loadGstr1Report);

let gstr1ReportDateRangeFilter;

function loadGstr1Report() {
    validateGstr1OTP();
    loadGstR1MonthDatePicker();
    loadGstR1QuarterlyDatePicker();
    let differenceValue = $(this).val();
    $('.difference-amount').each(function () {
        const differenceAmount = parseFloat($(this).text().replace(/,/g, ''));
        let valueDifference = Math.abs(differenceAmount)
        if (valueDifference <= differenceValue) {
            $(this).removeClass('text-danger');
        }else{
            $(this).addClass('text-danger');
        }
    });
    if (!$('#gstr1ReportDateRange').length) {
        return;
    }


    let date = companyFilter.gstr_1_report_date ? companyFilter.gstr_1_report_date.split(' - ') : '';
    // let searchParams = new URLSearchParams(window.location.search);

    // let start = moment(searchParams.get('start_date') ?? moment(date[0]) ?? moment().startOf('month'));
    // let end = moment(searchParams.get('end_date') ?? moment(date[1]) ?? moment().endOf('month'));

    let start = moment(date[0]) ?? moment().startOf("month");
    let end = moment(date[1]) ?? moment().endOf("month");

    function cb(start, end) {
        let startDate = start.format('YYYY-MM-DD');
        let endDate = end.format('YYYY-MM-DD');
        let dateFilter = startDate + ' - ' + endDate;
        updateCompanyFilter('gstr_1_report_date', dateFilter)
        window.livewire.emit('dateFilter', dateFilter)
        $('#gstr1ReportDateRange').
            html(start.format('MMMM D, YYYY') + ' - ' +
                end.format('MMMM D, YYYY'));
    }

    gstr1ReportDateRangeFilter = $('#gstr1ReportDateRange').
        daterangepicker({
            startDate: start,
            endDate: end,
            minDate: currentFinancialYearStartDate,
            maxDate: currentFinancialYearEndDate,
            showDropdowns: true,
            locale: {
                format: 'DD-MM-YYYY',
            },
            ranges: {
                'This FY': [financialYearStartDate(), financialYearEndDate()],
                'Till Date': [tillStartDate(), tillEndDate()],
                'This Month': [
                    moment().startOf('month'),
                    moment().endOf('month')],
                'Last Month': [
                    moment().subtract(1, 'month').startOf('month'),
                    moment().subtract(1, 'month').endOf('month')],
                'Last 30 Days': [moment().subtract(29, 'days'), moment()],
                'Today': [moment(), moment()],
                'Yesterday': [
                    moment().subtract(1, 'days'),
                    moment().subtract(1, 'days')],
            },
        }, cb);

    cb(start, end);

    gstr1ReportDateRangeFilter.on('apply.daterangepicker', function (ev, picker) {
        let startDate = picker.startDate.format('YYYY-MM-DD');
        let endDate = picker.endDate.format('YYYY-MM-DD');
        let date = startDate + ' - ' + endDate;

        updateCompanyFilter('gstr_1_report_date', date);

        // Build route parameters
        let params = {
            start_date: startDate,
            end_date: endDate,
            filter_type: 1
        };

        // Redirect
        location.href = route('company.reports.gstr-1', params);
    });

    gstr1ReportDateRangeFilter.on('cancel.daterangepicker',
        function (ev, picker) {
            let startDate = cancelFYStartDate;
            let endDate = cancelFYEndDate;
            let date = startDate + ' - ' + endDate
            updateCompanyFilter('gstr_1_report_date', date)
            let params = {
                start_date: startDate,
                end_date: endDate,
                filter_type: 3
            };

            location.href = route('company.reports.gstr-1', params);
        });
}

function loadGstR1MonthDatePicker(reload = false) {
    let comparativeStartMonth = $("#gstR1StartMonth");

    if (!comparativeStartMonth.length) {
        return;
    }

    let financialDate = companyFilter.gstr_1_report_date
        ? companyFilter.gstr_1_report_date.split(" - ")
        : "";

    let financialYear = companyFilter.current_financial_year
    ? companyFilter.current_financial_year.split(" - ")
    : "";
    let start = moment(financialDate[0]) ?? moment().startOf("month");

    let startYear = financialYear[0] ?? financialYearStartDate().split("/")[2];
    let endYear = financialYear[1] ?? financialYearEndDate().split("/")[2];
    let fyStartDate = new Date(startYear, 3, 1); // April 1, 2024 (Month index 3 = April)
    let fyEndDate = new Date(endYear, 2, 31);  // March 31, 2025 (Month index 2 = March)

    $('.gstr1-start-month').val(start.format('MMM-yyyy'));

    if(reload){
        let start = moment(financialDate[0]).startOf('month');
        let end = moment(financialDate[0]).endOf('month');
        let startStr = start.format('YYYY-MM-DD');
        let endStr = end.format('YYYY-MM-DD');
        let dateRange = startStr + " - " + endStr;

        updateCompanyFilter('gstr_1_report_date', dateRange)
                let params = {
                    start_date: startStr,
                    end_date: endStr,
                    filter_type: 1
                };

                location.href = route('company.reports.gstr-1', params);
    }
    comparativeStartMonth.datepicker({
        format: "M-yyyy",
        startView: "months",
        minViewMode: "months",
        autoclose: true,
        startDate: fyStartDate,
        endDate: fyEndDate,
    }).on('changeDate', function (e) {
        let selectedDate = $(this).datepicker('getDate');

        if (selectedDate) {
            let selectedDate = $(this).datepicker('getDate');

            if (selectedDate) {
                let year = selectedDate.getFullYear();
                let month = selectedDate.getMonth() + 1;

                let startDate = new Date(year, month - 1, 2);
                let endDate = new Date(year, month,1);

                let formattedStartDate = startDate.toISOString().slice(0, 10);
                let formattedEndDate = endDate.toISOString().slice(0, 10);
                let dateRange = formattedStartDate + " - " + formattedEndDate;

                updateCompanyFilter('gstr_1_report_date', dateRange)
                let params = {
                    start_date: formattedStartDate,
                    end_date: formattedEndDate,
                    filter_type: 1
                };

                location.href = route('company.reports.gstr-1', params);
            }
        }
    });
}

function loadGstR1QuarterlyDatePicker(reload = false) {
    let comparativeStartMonth = $("#gstR1QuarterMonth");

    if (!comparativeStartMonth.length) {
        return;
    }

    let financialYear = companyFilter.gstr_1_report_date
        ? companyFilter.gstr_1_report_date.split(" - ")
        : "";

        let years = companyFilter.current_financial_year
        ? companyFilter.current_financial_year.split(" - ")
        : "";
        let start = moment(financialYear[0]) ?? moment().startOf("month");
        let end = moment(financialYear[1]) ?? moment().endOf("month");
        let startYear = financialYear[0] ?? financialYearStartDate().split("/")[2];
        let endYear = financialYear[1] ?? financialYearEndDate().split("/")[2];
        let fyStartDate = new Date(startYear, 3, 1); // April 1, 2024 (Month index 3 = April)
        let fyEndDate = new Date(endYear, 2, 31);  // March 31, 2025 (Month index 2 = March)
        var currentDate = moment(financialYear[0]);

    var fyStartYear = parseInt(years[0].trim());
    var fyEndYear = parseInt(years[1].trim());

    // Determine current FY (April - March)
    var currentFyStartDate = moment(currentDate.year() + "-04-01");
    var currentFyEndDate = moment((currentDate.year() + 1) + "-03-31");

    if (currentDate.month() < 3) { // Jan, Feb, Mar
        currentFyStartDate = moment((currentDate.year() - 1) + "-04-01");
        currentFyEndDate = moment(currentDate.year() + "-03-31");
    }

    var selectedFyIsCurrent = (fyStartYear === currentFyStartDate.year() && fyEndYear === currentFyEndDate.year());

    var selectedQuarter = '';
    var selectedYear = '';
    var year;
    if (selectedFyIsCurrent) {
        // Determine current quarter
        var month = currentDate.month(); // Jan = 0
        if (month >= 3 && month <= 5) { // Apr - Jun
            selectedQuarter = 'Q1';
            selectedYear = fyStartYear;
            start = 3; end = 5; year = fyStartYear;
        } else if (month >= 6 && month <= 8) { // Jul - Sep
            selectedQuarter = 'Q2';
            selectedYear = fyStartYear;
            start = 6; end = 8; year = fyStartYear;
        } else if (month >= 9 && month <= 11) { // Oct - Dec
            selectedQuarter = 'Q3';
            selectedYear = fyStartYear;
            start = 9; end = 11; year = fyStartYear;
        } else { // Jan - Mar
            selectedQuarter = 'Q4';
            selectedYear = fyEndYear; // Q4 belongs to next calendar year
            start = 0; end = 2; year = fyStartYear + 1;
        }
    } else {
        // For past FY → always set Q4 of that FY
        selectedQuarter = 'Q4';
        selectedYear = fyEndYear;
        start = 0; end = 2; year = fyEndYear;
    }

    // Set value in your field
    $('.gstr1-quarter-month').val(selectedQuarter + ' - ' + selectedYear);
        const currentFyStart = fyStartYear;
        const currentFyEnd = fyEndYear;

    if(reload){
        const startDate = new Date(year, start, 1);
        const endDate = new Date(year, end + 1, 0); // Last day of quarter
        const formattedStartDate = moment(startDate).format('YYYY-MM-DD');
        const formattedEndDate = moment(endDate).format('YYYY-MM-DD');
        let dateRange = formattedStartDate + " - " + formattedEndDate;
        updateCompanyFilter('gstr_1_report_date', dateRange)
        let params = {
            start_date: formattedStartDate,
            end_date: formattedEndDate,
            filter_type: 2
        };

        location.href = route('company.reports.gstr-1', params);
    }

    $.fn.datepicker.dates['qtrs'] = {
        days: ["Sunday", "monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"],
        daysShort: ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"],
        daysMin: ["Su", "Mo", "Tu", "We", "Th", "Fr", "Sa"],
        months: ["Q1", "Q2", "Q3", "Q4", "", "", "", "", "", "", "", ""],
        monthsShort: [
            `Q1 ${currentFyStart}`,
            `Q2 ${currentFyStart}`,
            `Q3 ${currentFyStart}`,
            `Q4 ${currentFyEnd}`,
            "", "", "", "", "", "", "", ""
        ],
        today: "Today",
        clear: "Clear",
        format: "mm/dd/yyyy",
        titleFormat: "MM yyyy",
        weekStart: 0
    };

    comparativeStartMonth.datepicker({
        format: "MM yyyy",
        minViewMode: 1,
        autoclose: true,
        language: "qtrs",
        forceParse: false,
        startDate: fyStartDate,
        endDate: fyEndDate,
    }).on("show", function (event) {
        $(".datepicker table tr td span").css("width", "100%");
        $(".month").each(function (index, element) {
            if (index > 3) $(element).hide();
        });

    }).on("changeDate", function (event) {
        const dt = event.date;
        const month = dt.getMonth();
        let startMonth, endMonth,yearForQuarter;

    if (month === 0) { // April
        startMonth = 3; endMonth = 5; yearForQuarter = currentFyStart;
    } else if (month === 1) { // July
        startMonth = 6; endMonth = 8; yearForQuarter = currentFyStart;
    } else if (month === 2) { // October
        startMonth = 9; endMonth = 11; yearForQuarter = currentFyStart;
    } else if (month === 3) { // January
        startMonth = 0; endMonth = 2; yearForQuarter = currentFyStart + 1; // Q4 goes to next calendar year
    } else {
        console.warn("Invalid month selected for quarter:", month);
        return;
    }


    const startDate = new Date(yearForQuarter, startMonth, 1);
    const endDate = new Date(yearForQuarter, endMonth + 1, 0); // Last day of quarter

    const formattedStartDate = moment(startDate).format('YYYY-MM-DD');
    const formattedEndDate = moment(endDate).format('YYYY-MM-DD');

        let dateRange = formattedStartDate + " - " + formattedEndDate;
        updateCompanyFilter('gstr_1_report_date', dateRange)
        let params = {
            start_date: formattedStartDate,
            end_date: formattedEndDate,
            filter_type: 2
        };

        location.href = route('company.reports.gstr-1', params);
    });
}

listenChange('#gstR1DateRange', function () {
    let type = $(this).val();

    if (type == 1) { // Months
        $('#gstR1StartMonth').removeClass('d-none');
        loadGstR1MonthDatePicker(true);
    } else {
        $('#gstR1StartMonth').addClass('d-none');
    }

    if (type == 2) { // Quarters
        $('#gstR1QuarterMonth').removeClass('d-none');
        loadGstR1QuarterlyDatePicker(true);
    } else {
        $('#gstR1QuarterMonth').addClass('d-none');
    }

    if (type == 3) {  // Custom
        $('.gstr1-report-daterange').removeClass('d-none');
        $('#gstr1ReportDateRange').click();
    } else {
        $('.gstr1-report-daterange').addClass('d-none');
    }

})

function handleGstr1Filing(formData) {
    $.ajax({
        type: 'POST',
        url: route('company.reports.file-gstr1'),
        data: formData,
        beforeSend: function () {
            startPageLoader();
        },
        success: function (response) {
            let result = response.data;
            if (response.data.otpISInValid) {
                let gstLogin = response.data.gstLogin
                $('#gstLoginUser').val(gstLogin.username);
                $('#gstLoginUser').attr('readonly',true);
                $('#otpForGstr1').attr('required',true);
                $('.gst-otp').removeClass('d-none');
                $("#loginGSTPortalModal").modal("show");
                successMessage(response.message);
            }
            if (response.success) {
                successMessage(response.message);
                setTimeout(function () {
                    location.reload();
                }, 1000);

            }
        },
        error: function (error) {
            if(error.responseJSON.data.otpISInValid){
                let gstLogin = error.responseJSON.data.gstLogin
                $('#gstLoginUser').val(gstLogin.username);
                $('#gstLoginUser').attr('readonly',true);
                $('#otpForGstr1').attr('required',true);
                $("#loginGSTPortalModal").modal("show");
                $('.gst-otp').removeClass('d-none');
                $('.send-gst-otp').addClass('d-none');
            }
            stopPageLoader();
            errorMessage(error.responseJSON.message);
            // setTimeout(function () {
            //     location.reload();
            // }, 1000);
        },
        complete: function () {
            stopPageLoader();
            stopLoader();
            screenUnLock();
        },
    });
}


listenClick('#fileGstR1Button', function () {
    $('#verifyGstr1OTPModal').modal('hide');
    let startDate = $(this).attr('data-start-date');
    let endDate = $(this).attr('data-end-date');
    let formData = {
        'start_date': startDate,
        'end_date': endDate,
        'otp': 123456,
    };
    handleGstr1Filing(formData);
});
listenClick('#fileGstR1EVCButton', function () {
    // $("#congratulationsModel").modal("show");
    $("#verifyGstr1OTPModal").modal("show");
})


listenSubmit('#storEvcPanNumberAndSendOTP', function(e){
    e.preventDefault();
     let panNumber = $('#evcPanForGstr1Report').val();
     let endDate = $('#evcEndDateForGstr1Report').val();
     let formData = {
        'pan': panNumber,
        'endDate' : endDate
    };
        $.ajax({
        type: 'get',
        url: route('company.reports.file-gstr1-evc'),
        data: formData,
        beforeSend: function () {
            startPageLoader();
        },
        success: function (response) {
            if (response.success) {
                $("#verifyGstr1OTPModal").modal("hide");
                $("#verifyGstr1OTPNumberModal").modal("show");
                displaySuccessMessage(response.message);
            }
            if(!response.success){
                displayErrorMessage(result.message)
            }
        },
        error: function (error) {
            stopPageLoader();
            displayErrorMessage(error.responseJSON.message);
        },
        complete: function () {
            stopPageLoader();
            stopLoader();
            screenUnLock();
        },
    });
})

listenSubmit('#saveEVCOtpForGSTR1Report', function(e){
    e.preventDefault();
    let endDate = $('#evcEndDateForGstr1Report').val();
    let formData = {
        'endDate' : endDate,
        'otp': $('#evcOtpForGstr1Report').val(),
    };
    $.ajax({
        type: 'get',
        url: route('company.reports.file-gstr1-evc-otp'),
        data: formData,
        beforeSend: function () {
            startPageLoader();
        },
        success: function (response) {
            let result = response.data;
            if (response.data.success) {
                $("#verifyGstr1OTPNumberModal").modal("hide");
                $('.ack-num').text(result.result.stdClass.ack_num);
                $("#congratulationsModel").modal("show");
                //  location.reload();
                // setTimeout(function () {

                // }, 2000);
                // displaySuccessMessage(response.message);
            }
            if(!result.success){
                displayErrorMessage(result.message)
            }
        },
        error: function (error) {
            stopPageLoader();
            displayErrorMessage(error.responseJSON.message);
        },
        complete: function () {
            stopPageLoader();
            stopLoader();
            screenUnLock();
        },
    });
})

listenSubmit('#otpForGstr1Form', function (e) {
    e.preventDefault();
    let startDate = $('#fileGstR1Button').attr('data-start-date');
    let endDate = $('#fileGstR1Button').attr('data-end-date');
    let otp = $('#otpForGstr1').val();

    let formData = {
        'start_date': startDate,
        'end_date': endDate,
        'otp': otp,
    };

    if (!otp.length) {
        errorMessage('Please enter OTP');
        return false;
    }else if (otp.length !== 6) {
        errorMessage('OTP must be 6 digits');
        return false;
    }


    handleGstr1Filing(formData);
})

// listenHiddenBsModal('#verifyGstr1OTPModal', function () {

//     $('#otpForGstr1Form')[0].reset();
// });

function validateGstr1OTP() {
    $('#otpForGstr1').on('input', function (e) {
        let value = $(this).val();
        $(this).val(value.replace(/\D/g, '').slice(0, 6));
    });
}

listenClick('.gst-portal-login', function(){
    let date = companyFilter.gstr_1_report_date ? companyFilter.gstr_1_report_date.split(' - ') : '';

    $('#GstFileingDate').val(date[1]);
    $("#loginGSTPortalModal").appendTo("body").modal("show");
})

listenSubmit('#loginGSTPortalForm', function(e){
    e.preventDefault();
    let form = $(this).serialize();

    loginGstPortalForm(form)
})

listenClick('.connect-gstin-btn', function () {
    let date = companyFilter.gstr_1_report_date ? companyFilter.gstr_1_report_date.split(' - ') : '';
    $('#GstFileingDate').val(date[1]);
    $("#loginGSTPortalModal").appendTo("body").modal("show");
});

function startGstinModalLoader() {
    $('.connect-gstin-submit-btn').addClass('d-none');
    $('.connect-gstin-spinner').removeClass('d-none');
}

function stopGstinModalLoader() {
    $('.connect-gstin-submit-btn').removeClass('d-none');
    $('.connect-gstin-spinner').addClass('d-none');
}

listenChange('#changeFinancialYear', function () {
    let dateFilter = $('#changeFinancialYear option:selected').val();
    window.livewire.emit('financialYear', dateFilter)
})

listenClick('.view-summary',function(){
    let id = $(this).attr('data-id');
    viewGstSummary(route('company.gstr1-view-summary',{id}));
})

listenClick('.view-gstr3b-summary',function(){
    let id = $(this).attr('data-id');
    viewGstSummary(route('company.gstr3b-view-summary',{id}));
})

function viewGstSummary(url) {
    $.ajax({
        url: url,
        type: 'GET',
        beforeSend: function () {
            startPageLoader();
        },
        success: function (response) {
            let result = response.data;
            $('.append-view-summary').empty().append(result);
            $('#gstr1SummaryModal').appendTo("body").modal("show");
            if (result.success) {
                displaySuccessMessage(result.message);
            }
        },
        error: function (error) {
            stopPageLoader();
            displayErrorMessage(error.responseJSON.message);
        },
        complete: function () {
            stopPageLoader();
            stopLoader();
            screenUnLock();
        },
    });
}

listenClick('.resend-otp-for-gst-login',function(){
    let form = $('#loginGSTPortalForm').serialize()
        form = form  + "&resend=1";

        loginGstPortalForm(form)
})

function loginGstPortalForm(form){
    $.ajax({
        type: 'POST',
        url: route('company.gst.portal.login'),
        data: form  + "&resend=1",
        beforeSend: function () {
            startPageLoader();
        },
        success: function (response) {
            if (response.success) {
                successMessage(response.message);
                if(response.data.hideModal){
                    $("#loginGSTPortalModal").modal("hide");
                    $('#gstLoginUser').attr('readonly',false);
                    $('#otpForGstr1').attr('required',false);
                    $('.gst-otp').addClass('d-none');
                    $('.send-gst-otp').removeClass('d-none');
                    // location.reload();
                }else{
                    $("#loginGSTPortalModal").modal("show");
                    $('#gstLoginUser').attr('readonly',true);
                    $('#otpForGstr1').attr('required',true);
                    $('.gst-otp').removeClass('d-none');
                    $('.send-gst-otp').addClass('d-none');
                }

            } else {
                errorMessage(response.message);
            }
        },
        error: function (error) {
            stopPageLoader();
            errorMessage(error.responseJSON.message);
        },
        complete: function () {
            stopPageLoader();
            stopLoader();
            screenUnLock();
        },
    });
}

function setSyncRunningState($button) {
        $button.prop('disabled', true)
                .removeClass()
               .addClass('p-2 rounded ms-auto running fetch-data-gstr')
               .html('<span class="spin">⏳</span> Sync Running...');
        // $('#message').text('Please wait while data is syncing with GST portal.');
}

function setSyncCompletedState($button) {
        $button.prop('disabled', false)
               .removeClass()
               .addClass('btn btn-sm fetch-data-gstr')
               .html('🔄 Sync GST Data');
        // $('#message').html('✔️ Sync Completed Successfully. You can re-sync anytime.');
}

function setSyncFailedState($button, errorMsg) {
    $button.prop('disabled', false)
           .removeClass()
           .addClass('btn btn-sm fetch-data-gstr')
           .html('🔄 Retry GST Sync');
    $('#message').html(`❌ Sync Failed: ${errorMsg}`);
}

listenClick('.fetch-data-gstr', function () {
        const $button = $('.fetch-data-gstr');
        setSyncRunningState($button);

        $.ajax({
            type: 'GET',
            url: route('company.get-gst-data'),
            beforeSend: function () {
                startPageLoader();
            },
            success: function (response) {
                if (response.success) {
                    startSyncStatusCheck();
                } else {
                    setSyncFailedState($button, response.message || 'Unknown error');
                }
            },
            error: function (error) {
                const errorMsg = error.responseJSON?.message || 'Unexpected error occurred.';
                setSyncFailedState($button, errorMsg);

                $("#loginGSTPortalModal").appendTo("body").modal("show");
                errorMessage(errorMsg);
            },
            complete: function () {
                stopPageLoader();
                stopLoader();
                screenUnLock();
            }
        });
});

let syncInterval = null;

function startSyncStatusCheck(isFirstTime = false) {
    if (typeof isFirstTime !== 'boolean') {
        isFirstTime = false;
    }

    const $button = $('.fetch-data-gstr');

    if (syncInterval !== null) {
        clearInterval(syncInterval);
    }

    // Start new interval
    syncInterval = setInterval(() => {
        $.ajax({
            type: 'GET',
            url: route('company.check-gst-sync-status'),
            success: function (response) {
                if (response.success) {
                    if (response.message === 0) {
                        // Not yet synced, keep polling
                        console.log('Waiting for sync to start...');
                    } else if (response.message === 1) {
                        // Sync in progress
                        setSyncRunningState($button);
                        console.log('Sync in progress...');
                    } else if (response.message === 2) {
                        // Sync completed
                        setSyncCompletedState($button);
                        clearInterval(syncInterval); // Stop polling

                        if (!isFirstTime) {
                            successMessage('Data sync completed successfully. You can now view the latest records.');
                            setTimeout(() => location.reload(), 3000);
                        }
                    }
                } else {
                    const message = response.message || 'Failed to check sync status.';
                    setSyncFailedState($button, message);
                    clearInterval(syncInterval); // Stop on failure
                }
            },
            error: function (error) {
                const errorMsg = error.responseJSON?.message || 'Error checking sync status.';
                setSyncFailedState($button, errorMsg);
                clearInterval(syncInterval); // Stop on error
            }
        });
    }, 3000); // Run every 3 seconds
}

listenKeyup('.allow-difference-gst',function(){
    let differenceValue = $(this).val();
    $('.difference-amount').each(function () {
        const differenceAmount = parseFloat($(this).text().replace(/,/g, ''));
        let valueDifference = Math.abs(differenceAmount)
        if (valueDifference <= differenceValue) {
            $(this).removeClass('text-danger');
        }else{
            $(this).addClass('text-danger');
        }
    });
})

listenClick('#gstr1Reload', function () {
    let startDate = $(this).attr('data-start-date');
    let endDate = $(this).attr('data-end-date');
    handleGstPortalRequest(
        route('company.reports.gstr-1-reload'),
        { start_date: startDate, end_date: endDate },
        'GSTR-1 data reloaded successfully'
    );
});

listenClick('#resetGstData', function () {
    let startDate = $(this).attr('data-start-date');
    let endDate = $(this).attr('data-end-date');
    handleGstPortalRequest(
        route('company.reset-gst-data-for-portal'),
        { start_date: startDate, end_date: endDate },
        'GSTR-1 data reset successfully'
    );
});

function handleGstPortalRequest(url, data, successMessage) {
    $.ajax({
        type: 'POST',
        url: url,
        data: data,
        beforeSend: function () {
            startPageLoader();
        },
        success: function (response) {
            if (response.success) {
                displaySuccessMessage(response.message || successMessage);
                location.reload();
            }
        },
        error: function (error) {
            stopPageLoader();
            displayErrorMessage(error.responseJSON.message);
            if(error.responseJSON.data?.showModal){
                $("#loginGSTPortalModal").modal("show");
                $('#gstLoginUser').val(error.responseJSON.data.data.username);
                $('#gstLoginUser').attr('readonly', true);
                $('#otpForGstr1').attr('required', true);
                $('.gst-otp').removeClass('d-none');
                $('.send-gst-otp').addClass('d-none');
            }
        },
        complete: function () {
            stopPageLoader();
            stopLoader();
            screenUnLock();
        },
    });
}

listenClick('.resend-otp-link',function(e){
    let endDate = $('#evcEndDateForGstr1Report').val();
    let formData = {
        'endDate' : endDate,
    };
    $.ajax({
        type: 'get',
        url: route('company.resend-verification-otp'),
        data: formData,
        beforeSend: function () {
            startPageLoader();
        },
        success: function (response) {
            let result = response.data;
            if (response.data.success) {
                displaySuccessMessage(response.message);
            }
            if(!result.success){
                displayErrorMessage(result.message)
            }
        },
        error: function (error) {
            stopPageLoader();
            displayErrorMessage(error.responseJSON.message);
        },
        complete: function () {
            stopPageLoader();
            stopLoader();
            screenUnLock();
        },
    });
})

$("#congratulationsModel").on('hidden.bs.modal', function () {
    location.reload(); // reload entire page
});
