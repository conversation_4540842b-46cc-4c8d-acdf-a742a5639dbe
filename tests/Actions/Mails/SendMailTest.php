<?php

use App\Mail\CompleteProfileEmail;
use App\Mail\CustomerSummaryReportMail;
use App\Mail\NotifyFailureEmailToUserMail;
use App\Mail\NotifySAdminForNewAddOnMail;
use App\Mail\NotifySAdminForNewSubscriptionMail;
use App\Mail\PaymentSuccessMail;
use App\Mail\RegisterNewCompanyEmail;
use App\Mail\ResetMPinMail;
use App\Mail\ResetPasswordMail;
use App\Mail\SandSubscriptionsPaymentSuccessMail;
use App\Mail\UserVerificationOtpEmail;
use App\Mail\WelcomeCompanyEmail;
use App\Models\Company;
use App\Models\Ledger;
use App\Models\Master\Customer;
use App\Models\Plan;
use App\Models\Subscription;
use App\Models\Transaction;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Mail;

test('send user verification otp mail', function () {
    //arrange
    Mail::fake();
    setCompany(1);
    $company = Company::where('id', 1)->firstOrFail();
    $user = User::whereId($company->user_id)->firstOrFail();

    //act
    $response = $this->get(route('otp-as-password', ['email_or_phone' => $user->email]));

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'OTP sent successfully.',
        ]);
    Mail::assertSent(UserVerificationOtpEmail::class, function ($mail) use ($user) {
        $mail->build();

        return $mail->hasTo($user->email) && $mail->hasSubject('Hi User, please verify your account.');
    });
    $this->assertDatabaseHas('otp_verifications', [
        'user_id' => $user->id,
        'otp' => '123456',
    ]);
});

test('send forgot password otp email', function () {
    // arrange
    Mail::fake();
    $company = Company::where('id', 1)->firstOrFail();
    $user = User::whereId($company->user_id)->firstOrFail();
    $input = [
        'email_or_phone' => $user->email,
        'submit' => 'send_otp',
    ];

    // act
    $response = $this->post(route('forgot-password.email'), $input);

    // assert
    $response->assertStatus(302);
    $response->assertRedirect(route('forgot-password.email').'?flag='.urlencode($input['email_or_phone']));
    Mail::assertSent(UserVerificationOtpEmail::class, function ($mail) use ($user) {
        $mail->build();

        return $mail->hasTo($user->email) && $mail->hasSubject('Hi User, please verify your account.');
    });
    $this->assertDatabaseHas('otp_verifications', [
        'user_id' => $user->id,
        'otp' => '123456',
    ]);
});

test('send welcome mail', function () {
    //arrange
    Mail::fake();
    setCompany(1);
    $company = Company::where('id', 1)->firstOrFail();
    $user = User::whereId($company->user_id)->firstOrFail();

    //act
    Mail::to($user->email)->send(new WelcomeCompanyEmail());

    //assert
    Mail::assertSent(WelcomeCompanyEmail::class, function ($mail) use ($user) {
        $mail->build();

        return $mail->hasTo($user->email) && $mail->hasSubject('Thank You for Choosing At HisabKitab');
    });
    $mailable = new WelcomeCompanyEmail();
    $renderedHtml = $mailable->render();
    expect($renderedHtml)->toContain('Thankyou for registering to hisabkitab!!')
        ->toContain('Thank you for registering')
        ->toContain('<EMAIL>');
});

test('send complete profile mail', function () {
    //arrange
    Mail::fake();
    setCompany(1);
    $company = Company::where('id', 1)->firstOrFail();
    $user = User::whereId($company->user_id)->firstOrFail();

    //act
    Mail::to($user->email)->send(new CompleteProfileEmail($user));

    //assert
    Mail::assertSent(CompleteProfileEmail::class, function ($mail) use ($user) {
        $mail->build();

        return $mail->hasTo($user->email) && $mail->user->id === $user->id && $mail->hasSubject('Complete Your Profile');
    });
    $mailable = new CompleteProfileEmail($user);
    $renderedHtml = $mailable->render();
    expect($renderedHtml)->toContain('Complete Your Profile')
        ->toContain($user->first_name)
        ->toContain(route('company.companies.edit', ['company' => $user->company->id]))
        ->toContain('<EMAIL>');
});

test('send register new company mail', function () {
    Mail::fake();
    config(['app.send_mail_when_company_register' => '<EMAIL>']);
    $input = [
        'name' => 'Dhruvik Virani',
        'email' => '<EMAIL>',
        'phone_no' => '9584758542',
        'password' => '123456',
        'region_code' => '91',
        'region_iso' => 'in',
        'role' => 'client_user',
    ];

    //act
    $this->post(route('register.company'), $input);

    //assert
    Mail::assertSent(RegisterNewCompanyEmail::class, function ($mail) {
        return $mail->hasTo('<EMAIL>');
    });

});

test('send reset mpin mail', function () {
    //arrange
    Mail::fake();
    setCompany(1);
    $company = Company::where('id', 1)->firstOrFail();
    $user = User::whereId($company->user_id)->firstOrFail();

    //act
    Mail::to($user->email)->send(new ResetMPinMail(['url' => 'www.hisabkitab.co/reset-mpin-test', 'companyName' => 'Test Company']));

    //assert
    Mail::assertSent(ResetMPinMail::class, function ($mail) use ($user) {
        $mail->build();

        return $mail->hasTo($user->email) && $mail->hasSubject('Reset Your MPIN');
    });
});

test('send reset password mail', function () {
    //arrange
    Mail::fake();
    setCompany(1);
    $company = Company::where('id', 1)->firstOrFail();
    $user = User::whereId($company->user_id)->firstOrFail();

    //act
    Mail::to($user->email)->send(new ResetPasswordMail(['url' => 'www.hisabkitab.co/reset-password-test', 'companyName' => 'Test Company']));

    //assert
    Mail::assertSent(ResetPasswordMail::class, function ($mail) use ($user) {
        $mail->build();

        return $mail->hasTo($user->email) && $mail->hasSubject('Reset Your Password');
    });
});

test('send notify failure email to user mail', function () {
    //arrange
    Mail::fake();
    setCompany(1);
    $company = Company::where('id', 1)->firstOrFail();
    $user = User::whereId($company->user_id)->firstOrFail();

    $input = [
        'name' => 'Test user',
        'invoice_id' => '1234',
        'to' => $user->email,
        'recipient_email' => 'test@gmail',
        'mail_subject' => 'Test failure email',
        'date_time' => Carbon::now(),
        'subject' => 'Test failure email',
    ];

    //act
    Mail::to($user->email)->send(new NotifyFailureEmailToUserMail($input));

    //assert
    Mail::assertSent(NotifyFailureEmailToUserMail::class, function ($mail) use ($user) {
        $mail->build();

        return $mail->hasTo($user->email) && $mail->hasSubject('Test failure email');
    });
});

test('send notify super admin for new add on mail', function () {
    //arrange
    Mail::fake();
    setCompany(1);
    $company = Company::where('id', 1)->firstOrFail();
    $user = User::whereId($company->user_id)->firstOrFail();

    $addOnMeta = [
        'addition_pay' => '100',
        'extra_companies' => '1',
        'extra_users' => '1',
    ];

    //act
    Mail::to($user->email)->send(new NotifySAdminForNewAddOnMail($user, $addOnMeta));

    //assert
    Mail::assertSent(NotifySAdminForNewAddOnMail::class, function ($mail) use ($user) {
        $mail->build();

        return $mail->hasTo($user->email) && $mail->hasSubject('Additional Users and Companies Purchase');
    });
});

test('send notify super admin for new subcription on mail', function () {
    //arrange
    Mail::fake();
    setCompany(1);
    $company = Company::where('id', 1)->firstOrFail();
    $user = User::whereId($company->user_id)->firstOrFail();

    $plan = Plan::create([
        'name' => 'Test Plan',
        'price' => 100,
        'frequency' => 1,
        'is_for_mobile' => 0,
        'status' => 1,
    ]);

    $transaction = Transaction::create([
        'plan_id' => $plan->id,
        'meta' => json_encode(['order_id=**********']),
        'status' => 1,
    ]);

    $subscription = Subscription::create([
        'plan_id' => $plan->id,
        'account_id' => $user->account_id ?? 1,
        'start_date' => now(),
        'end_date' => now()->addYear(),
        'transaction_id' => $transaction->id,
        'type' => 1,
        'status' => 1,
        'amount' => 200,
        'active_users' => 1,
        'active_companies' => 1,
        'meta' => json_encode(['total' => 200]),
    ]);

    //act
    Mail::to($user->email)->send(new NotifySAdminForNewSubscriptionMail($subscription));

    //assert
    Mail::assertSent(NotifySAdminForNewSubscriptionMail::class, function ($mail) use ($user) {
        $mail->build();

        return $mail->hasTo($user->email) && $mail->subject === 'New Subscription Notification';
    });
    $mailable = new NotifySAdminForNewSubscriptionMail($subscription);
    $renderedHtml = $mailable->render();
    expect($renderedHtml)->toContain('New Company Subscribed');
});

test('send payment success mail', function () {
    //arrange
    Mail::fake();
    setCompany(1);
    $company = Company::where('id', 1)->firstOrFail();
    $user = User::whereId($company->user_id)->firstOrFail();

    $input = [
        'transaction' => [
            'customer' => ['name' => 'test user'],
            'full_invoice_number' => 'INV-1234',
        ],
        'paidAmount' => 200,
        'gstin' => '24ABCDE1234G1Z',
        'paymentType' => 'online',
        'paymentStatus' => 'Paid',
        'transactionId' => '1234',
        'currentCompany' => $company,

    ];

    //act
    Mail::to($user->email)->send(new PaymentSuccessMail($input));

    //assert
    Mail::assertSent(PaymentSuccessMail::class, function ($mail) use ($user, $company) {
        $mail->build();

        return $mail->hasTo($user->email) && $mail->hasSubject('INV-1234 from '.$company->trade_name);
    });
    $mailable = new PaymentSuccessMail($input);
    $renderedHtml = $mailable->render();
    expect($renderedHtml)->toContain('INV-1234')
        ->toContain('1234');
});

test('send subcription payment success mail', function () {
    //arrange
    Mail::fake();
    setCompany(1);
    $company = Company::where('id', 1)->firstOrFail();
    $user = User::whereId($company->user_id)->firstOrFail();

    $plan = Plan::create([
        'name' => 'Test Plan',
        'price' => 100,
        'frequency' => 1,
        'is_for_mobile' => 0,
        'status' => 1,
    ]);

    $transaction = Transaction::create([
        'plan_id' => $plan->id,
        'meta' => json_encode(['order_id=**********']),
        'status' => 1,
    ]);

    $subscription = Subscription::create([
        'plan_id' => $plan->id,
        'account_id' => $user->account_id ?? 1,
        'start_date' => now(),
        'end_date' => now()->addYear(),
        'transaction_id' => $transaction->id,
        'type' => 1,
        'status' => 1,
        'amount' => 200,
        'active_users' => 1,
        'active_companies' => 1,
        'meta' => json_encode(['total' => 200]),
    ]);

    //act
    Mail::to($user->email)->send(new SandSubscriptionsPaymentSuccessMail($subscription));

    //assert
    Mail::assertSent(SandSubscriptionsPaymentSuccessMail::class, function ($mail) use ($user) {
        $mail->build();

        return $mail->hasTo($user->email) && $mail->subject === 'Thank You for Choosing At HisabKitab';
    });
    $mailable = new SandSubscriptionsPaymentSuccessMail($subscription);
    $renderedHtml = $mailable->render();
    expect($renderedHtml)->toContain('Woohoo! hisabkitab Subscription is Confirmed')
        ->toContain('<EMAIL>');
});

test('send customer summury report mail', function () {
    //arrange
    Mail::fake();
    setCompany(1);
    $company = Company::where('id', 1)->firstOrFail();
    $user = User::whereId($company->user_id)->firstOrFail();
    $ledger = Ledger::whereModelType(Customer::class)->first();
    $ledger->model->update([
        'person_email' => $user->email,
    ]);

    $cacheKey = generateCacheKey('customer_summary_report');

    Cache::put($cacheKey, [
        'start_date' => Carbon::now()->year.'-01-01',
        'end_date' => Carbon::now()->year.'-01-31',
    ]);

    //act
    $response = $this->get(route('company.customer-summary-report.send-email', $ledger->id));

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Mail Sent Successfully.',
        ]);
    Mail::assertSent(CustomerSummaryReportMail::class, function ($mail) use ($user) {
        return $mail->hasTo($user->email);
    });
});
