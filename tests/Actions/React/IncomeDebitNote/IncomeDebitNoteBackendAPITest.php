<?php

use App\Actions\v1\Transactions\IncomeDebitNote\StoreIncomeDebitNoteAction;
use App\Models\GstTax;
use App\Models\IncomeDebitNoteTransaction;
use App\Models\Master\IncomeDebitNoteTransactionMaster;
use App\Models\Master\ItemMasterGoods;
use Carbon\Carbon;
use Database\Factories\Api\IncomeDebitNoteTransactionFactory;

/*
|----------------------------------------------------------------------------------------------------------------------------------------------------
| Income Debit Note Transaction Item Invoice Backend Validation Test Case
|----------------------------------------------------------------------------------------------------------------------------------------------------
*/

test('validate income debit note transaction item invoice will return gst na incorrect', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false, 'app.is_validation_testing' => false]);

    $debitNoteInput = IncomeDebitNoteTransactionFactory::new()->make()->toArray();
    $debitNoteInput['date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = $this->postJson(route('income-debit-note'), $debitNoteInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Is GST NA is not correct.',
        ]);
});

test('validate income debit note transaction item invoice will return cgst, sgst, and igst calculated is not correct', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false, 'app.is_validation_testing' => false]);

    $debitNoteInput = IncomeDebitNoteTransactionFactory::new()->make(['is_gst_na' => true, 'is_cgst_sgst_igst_calculated' => true])->toArray();
    $debitNoteInput['date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = $this->postJson(route('income-debit-note'), $debitNoteInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Is CGST, SGST, and IGST Calculated is not correct.',
        ]);
});

test('validate income debit note transaction item invoice will return subtotal is not correct when pass wrong total', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $debitNoteInput = IncomeDebitNoteTransactionFactory::new()->make([
        'is_gst_na' => true,
    ])->toArray();
    $debitNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $debitNoteInput['items'][0]['total'] = 10;

    //act
    $response = $this->postJson(route('income-debit-note'), $debitNoteInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Subtotal is not correct.',
        ]);
});

test('validate income debit note transaction item invoice will return subtotal is not correct when pass wrong total with gst na, exempt, zero in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $debitNoteInput = IncomeDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $debitNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $debitNoteInput['items'][0]['total'] = 10;

    //act
    $response = $this->postJson(route('income-debit-note'), $debitNoteInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Subtotal is not correct.',
        ]);
});

test('validate income debit note transaction item invoice will return subtotal is not correct when pass wrong total with gst percentage', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $gst = GstTax::whereTaxRate(28.00)->first();
    $debitNoteInput = IncomeDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $debitNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $debitNoteInput['items'][0]['gst_id'] = $gst->id;
    $debitNoteInput['items'][0]['gst_tax_percentage'] = $gst->tax_rate;

    //act
    $response = $this->postJson(route('income-debit-note'), $debitNoteInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Subtotal is not correct.',
        ]);
});

test('validate income debit note transaction item invoice will return gross value is not correct', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $debitNoteInput = IncomeDebitNoteTransactionFactory::new()->make([
        'is_gst_na' => true,
        'gross_value' => 10,
    ])->toArray();
    $debitNoteInput['date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = $this->postJson(route('income-debit-note'), $debitNoteInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Gross value is not correct.',
        ]);
});

test('validate income debit note transaction item invoice will return addition charge total is not correct', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $debitNoteInput = IncomeDebitNoteTransactionFactory::new()->make([
        'is_gst_na' => true,
    ])->toArray();
    $debitNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $debitNoteInput['additional_charges'][0]['ac_value'] = 20;

    //act
    $response = $this->postJson(route('income-debit-note'), $debitNoteInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Additional charge total is not correct.',
        ]);
});

test('validate income debit note transaction item invoice will return Taxable value is not correct when pass additional gst percentage in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $gst = GstTax::whereTaxRate(28.00)->first();
    $debitNoteInput = IncomeDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $debitNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $debitNoteInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    foreach ($debitNoteInput['items'] as $key => $item) {
        $debitNoteInput['items'][$key]['gst_id'] = 2;
    }

    //act
    $response = $this->postJson(route('income-debit-note'), $debitNoteInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Taxable value is not correct.',
        ]);
});

test('validate income debit note transaction item invoice will return taxable value is not correct', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $debitNoteInput = IncomeDebitNoteTransactionFactory::new()->make([
        'is_gst_na' => true,
        'taxable_value' => 10,
    ])->toArray();
    $debitNoteInput['date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = $this->postJson(route('income-debit-note'), $debitNoteInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Taxable value is not correct.',
        ]);
});

test('validate income debit note transaction item invoice will return cgst is not correct in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $debitNoteInput = IncomeDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'gross_value' => 468.78,
        'taxable_value' => 478.78,
        'main_classification_nature_type' => 'Sale Intra State Taxable',
    ])->toArray();
    $debitNoteInput['date'] = Carbon::now()->format('d-m-Y');
    foreach ($debitNoteInput['items'] as $key => $item) {
        $debitNoteInput['items'][$key]['gst_id'] = $gst->id;
        $debitNoteInput['items'][$key]['total'] = 78.13;
    }

    //act
    $response = $this->postJson(route('income-debit-note'), $debitNoteInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'CGST is not correct.',
        ]);
});

test('validate income debit note transaction item invoice will return sgst is not correct in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $debitNoteInput = IncomeDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'gross_value' => 468.78,
        'taxable_value' => 478.78,
        'main_classification_nature_type' => 'Sale Intra State Taxable',
        'cgst' => 65.64,
    ])->toArray();
    $debitNoteInput['date'] = Carbon::now()->format('d-m-Y');
    foreach ($debitNoteInput['items'] as $key => $item) {
        $debitNoteInput['items'][$key]['gst_id'] = $gst->id;
        $debitNoteInput['items'][$key]['total'] = 78.13;
    }

    //act
    $response = $this->postJson(route('income-debit-note'), $debitNoteInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'SGST is not correct.',
        ]);
});

test('validate income debit note transaction item invoice will return igst is not correct in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $debitNoteInput = IncomeDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'gross_value' => 468.78,
        'taxable_value' => 478.78,
    ])->toArray();
    $debitNoteInput['date'] = Carbon::now()->format('d-m-Y');
    foreach ($debitNoteInput['items'] as $key => $item) {
        $debitNoteInput['items'][$key]['gst_id'] = $gst->id;
        $debitNoteInput['items'][$key]['total'] = 78.13;
    }

    //act
    $response = $this->postJson(route('income-debit-note'), $debitNoteInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'IGST is not correct.',
        ]);
});

test('validate income debit note transaction item invoice will return classification nature type is empty', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(0.00)->first();

    $debitNoteInput = IncomeDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'main_classification_nature_type' => null,
        'taxable_value' => 610,
    ])->toArray();
    $debitNoteInput['date'] = Carbon::now()->format('d-m-Y');
    foreach ($debitNoteInput['items'] as $key => $item) {
        $debitNoteInput['items'][$key]['gst_id'] = $gst->id;
    }

    //act
    $response = $this->postJson(route('income-debit-note'), $debitNoteInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Classification Nature Type is empty.',
        ]);
});

test('validate income debit note transaction item invoice will return add less total is not correct', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $debitNoteInput = IncomeDebitNoteTransactionFactory::new()->make([
        'is_gst_na' => true,
        'taxable_value' => 610,
    ])->toArray();
    $debitNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $debitNoteInput['add_less'][0]['al_total'] = 100;

    //act
    $response = $this->postJson(route('income-debit-note'), $debitNoteInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Add Less total is not correct.',
        ]);
});

test('validate income debit note transaction item invoice will return round off amount is not correct', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $debitNoteInput = IncomeDebitNoteTransactionFactory::new()->make([
        'is_gst_na' => true,
        'is_round_off_not_changed' => 1,
        'taxable_value' => 610,
        'rounding_amount' => 10.50,
    ])->toArray();
    $debitNoteInput['date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = $this->postJson(route('income-debit-note'), $debitNoteInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Round off amount is not correct.',
        ]);
});

test('validate income debit note transaction item invoice will return grand total is not correct', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $debitNoteInput = IncomeDebitNoteTransactionFactory::new()->make([
        'is_gst_na' => true,
        'taxable_value' => 610,
        'grand_total' => 100,
    ])->toArray();
    $debitNoteInput['date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = $this->postJson(route('income-debit-note'), $debitNoteInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Grand total is not correct.',
        ]);
});

/*
|----------------------------------------------------------------------------------------------------------------------------------------------------
| Income Debit Note Transaction Account Invoice Backend Validation Test Case
|----------------------------------------------------------------------------------------------------------------------------------------------------
*/

test('validate income debit note transaction account invoice will return gst na incorrect', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false, 'app.is_validation_testing' => false]);

    $debitNoteInput = IncomeDebitNoteTransactionFactory::new()->make(['dn_item_type' => IncomeDebitNoteTransaction::ACCOUNTING_INVOICE])->toArray();
    $debitNoteInput['date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = $this->postJson(route('income-debit-note'), $debitNoteInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Is GST NA is not correct.',
        ]);
});

test('validate income debit note transaction account invoice will return cgst, sgst, and igst calculated is not correct', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false, 'app.is_validation_testing' => false]);

    $debitNoteInput = IncomeDebitNoteTransactionFactory::new()->make([
        'dn_item_type' => IncomeDebitNoteTransaction::ACCOUNTING_INVOICE,
        'is_gst_na' => true,
        'is_cgst_sgst_igst_calculated' => true,
    ])->toArray();
    $debitNoteInput['date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = $this->postJson(route('income-debit-note'), $debitNoteInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Is CGST, SGST, and IGST Calculated is not correct.',
        ]);
});

test('validate income debit note transaction account invoice will return subtotal is not correct when pass wrong total', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $debitNoteInput = IncomeDebitNoteTransactionFactory::new()->make([
        'dn_item_type' => IncomeDebitNoteTransaction::ACCOUNTING_INVOICE,
        'is_gst_na' => true,
    ])->toArray();
    $debitNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $debitNoteInput['ledgers'][0]['total'] = 10;

    //act
    $response = $this->postJson(route('income-debit-note'), $debitNoteInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Subtotal is not correct.',
        ]);
});

test('validate income debit note transaction account invoice will return subtotal is not correct when pass wrong total with gst na, exempt, zero in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $debitNoteInput = IncomeDebitNoteTransactionFactory::new()->make([
        'dn_item_type' => IncomeDebitNoteTransaction::ACCOUNTING_INVOICE,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $debitNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $debitNoteInput['ledgers'][0]['total'] = 10;

    //act
    $response = $this->postJson(route('income-debit-note'), $debitNoteInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Subtotal is not correct.',
        ]);
});

test('validate income debit note transaction account invoice will return subtotal is not correct when pass wrong total with gst percentage', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $gst = GstTax::whereTaxRate(28.00)->first();
    $debitNoteInput = IncomeDebitNoteTransactionFactory::new()->make([
        'dn_item_type' => IncomeDebitNoteTransaction::ACCOUNTING_INVOICE,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $debitNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $debitNoteInput['ledgers'][0]['gst_id'] = $gst->id;
    $debitNoteInput['ledgers'][0]['with_tax'] = true;
    $debitNoteInput['ledgers'][0]['gst_tax_percentage'] = $gst->tax_rate;

    //act
    $response = $this->postJson(route('income-debit-note'), $debitNoteInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Subtotal is not correct.',
        ]);
});

test('validate income debit note transaction account invoice will return gross value is not correct', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $debitNoteInput = IncomeDebitNoteTransactionFactory::new()->make([
        'dn_item_type' => IncomeDebitNoteTransaction::ACCOUNTING_INVOICE,
        'is_gst_na' => true,
        'gross_value' => 10,
    ])->toArray();
    $debitNoteInput['date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = $this->postJson(route('income-debit-note'), $debitNoteInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Gross value is not correct.',
        ]);
});

test('validate income debit note transaction account invoice will return addition charge total is not correct', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $debitNoteInput = IncomeDebitNoteTransactionFactory::new()->make([
        'dn_item_type' => IncomeDebitNoteTransaction::ACCOUNTING_INVOICE,
        'is_gst_na' => true,
    ])->toArray();
    $debitNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $debitNoteInput['additional_charges'][0]['ac_value'] = 20;

    //act
    $response = $this->postJson(route('income-debit-note'), $debitNoteInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Additional charge total is not correct.',
        ]);
});

test('validate income debit note transaction account invoice will return Taxable value is not correct when pass additional gst percentage in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $gst = GstTax::whereTaxRate(28.00)->first();
    $debitNoteInput = IncomeDebitNoteTransactionFactory::new()->make([
        'dn_item_type' => IncomeDebitNoteTransaction::ACCOUNTING_INVOICE,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $debitNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $debitNoteInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    foreach ($debitNoteInput['ledgers'] as $key => $ledger) {
        $debitNoteInput['ledgers'][$key]['gst_id'] = 2;
    }

    //act
    $response = $this->postJson(route('income-debit-note'), $debitNoteInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Taxable value is not correct.',
        ]);
});

test('validate income debit note transaction account invoice will return taxable value is not correct', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $debitNoteInput = IncomeDebitNoteTransactionFactory::new()->make([
        'dn_item_type' => IncomeDebitNoteTransaction::ACCOUNTING_INVOICE,
        'is_gst_na' => true,
        'taxable_value' => 10,
    ])->toArray();
    $debitNoteInput['date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = $this->postJson(route('income-debit-note'), $debitNoteInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Taxable value is not correct.',
        ]);
});

test('validate income debit note transaction account invoice will return cgst is not correct in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $debitNoteInput = IncomeDebitNoteTransactionFactory::new()->make([
        'dn_item_type' => IncomeDebitNoteTransaction::ACCOUNTING_INVOICE,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'gross_value' => 468.78,
        'taxable_value' => 478.78,
        'main_classification_nature_type' => 'Sale Intra State Taxable',
    ])->toArray();
    $debitNoteInput['date'] = Carbon::now()->format('d-m-Y');
    foreach ($debitNoteInput['ledgers'] as $key => $ledger) {
        $debitNoteInput['ledgers'][$key]['gst_id'] = $gst->id;
        $debitNoteInput['ledgers'][$key]['total'] = 78.13;
    }

    //act
    $response = $this->postJson(route('income-debit-note'), $debitNoteInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'CGST is not correct.',
        ]);
});

test('validate income debit note transaction account invoice will return sgst is not correct in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $debitNoteInput = IncomeDebitNoteTransactionFactory::new()->make([
        'dn_item_type' => IncomeDebitNoteTransaction::ACCOUNTING_INVOICE,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'gross_value' => 468.78,
        'taxable_value' => 478.78,
        'main_classification_nature_type' => 'Sale Intra State Taxable',
        'cgst' => 65.64,
    ])->toArray();
    $debitNoteInput['date'] = Carbon::now()->format('d-m-Y');
    foreach ($debitNoteInput['ledgers'] as $key => $ledger) {
        $debitNoteInput['ledgers'][$key]['gst_id'] = $gst->id;
        $debitNoteInput['ledgers'][$key]['total'] = 78.13;
    }

    //act
    $response = $this->postJson(route('income-debit-note'), $debitNoteInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'SGST is not correct.',
        ]);
});

test('validate income debit note transaction account invoice will return igst is not correct in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $debitNoteInput = IncomeDebitNoteTransactionFactory::new()->make([
        'dn_item_type' => IncomeDebitNoteTransaction::ACCOUNTING_INVOICE,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'gross_value' => 468.78,
        'taxable_value' => 478.78,
    ])->toArray();
    $debitNoteInput['date'] = Carbon::now()->format('d-m-Y');
    foreach ($debitNoteInput['ledgers'] as $key => $ledger) {
        $debitNoteInput['ledgers'][$key]['gst_id'] = $gst->id;
        $debitNoteInput['ledgers'][$key]['total'] = 78.13;
    }

    //act
    $response = $this->postJson(route('income-debit-note'), $debitNoteInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'IGST is not correct.',
        ]);
});

test('validate income debit note transaction account invoice will return classification nature type is empty', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(0.00)->first();

    $debitNoteInput = IncomeDebitNoteTransactionFactory::new()->make([
        'dn_item_type' => IncomeDebitNoteTransaction::ACCOUNTING_INVOICE,
        'is_gst_enabled' => true,
        'main_classification_nature_type' => null,
        'taxable_value' => 610,
    ])->toArray();
    $debitNoteInput['date'] = Carbon::now()->format('d-m-Y');
    foreach ($debitNoteInput['ledgers'] as $key => $ledger) {
        $debitNoteInput['ledgers'][$key]['gst_id'] = $gst->id;
    }

    //act
    $response = $this->postJson(route('income-debit-note'), $debitNoteInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Classification Nature Type is empty.',
        ]);
});

test('validate income debit note transaction account invoice will return add less total is not correct', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $debitNoteInput = IncomeDebitNoteTransactionFactory::new()->make([
        'dn_item_type' => IncomeDebitNoteTransaction::ACCOUNTING_INVOICE,
        'is_gst_na' => true,
        'taxable_value' => 610,
    ])->toArray();
    $debitNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $debitNoteInput['add_less'][0]['al_total'] = 100;

    //act
    $response = $this->postJson(route('income-debit-note'), $debitNoteInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Add Less total is not correct.',
        ]);
});

test('validate income debit note transaction account invoice will return round off amount is not correct', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $debitNoteInput = IncomeDebitNoteTransactionFactory::new()->make([
        'dn_item_type' => IncomeDebitNoteTransaction::ACCOUNTING_INVOICE,
        'is_round_off_not_changed' => 1,
        'is_gst_na' => true,
        'taxable_value' => 610,
        'rounding_amount' => 10.50,
    ])->toArray();
    $debitNoteInput['date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = $this->postJson(route('income-debit-note'), $debitNoteInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Round off amount is not correct.',
        ]);
});

test('validate income debit note transaction account invoice will return grand total is not correct', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $debitNoteInput = IncomeDebitNoteTransactionFactory::new()->make([
        'dn_item_type' => IncomeDebitNoteTransaction::ACCOUNTING_INVOICE,
        'is_gst_na' => true,
        'taxable_value' => 610,
        'grand_total' => 100,
    ])->toArray();
    $debitNoteInput['date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = $this->postJson(route('income-debit-note'), $debitNoteInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Grand total is not correct.',
        ]);
});

/*
|----------------------------------------------------------------------------------------------------------------------------------------------------
| Store Income Debit Note Transaction Item Invoice Backend Validation with update Test Case
|----------------------------------------------------------------------------------------------------------------------------------------------------
*/

test('validation with update decimal rpu amount when decimal place is 1 for income debit note transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 1]);
    $debitNoteInput = IncomeDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $debitNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $debitNoteInput['items'][0]['rpu'] = 100.********;
    $debitNoteInput['items'][0]['rpu_without_gst'] = 100.********;
    $debitNoteInput['items'][0]['rpu_with_gst'] = 100.********;
    $debitNoteInput['items'][0]['gst_id'] = $gst->id;
    $debitNoteInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('income-debit-note'), $debitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Debit Note Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('income_debit_note_transactions', [
        'id' => $response->json('data.income_debit_note_transaction.id'),
        'company_id' => 1,
        'debit_note_number' => $debitNoteInput['debit_note_number'],
        'dn_item_type' => IncomeDebitNoteTransaction::ITEM_INVOICE,
    ]);
    $debitNoteTransaction = IncomeDebitNoteTransaction::with('incomeDebitNoteItems')->whereId($response->json('data.income_debit_note_transaction.id'))->first();
    $this->assertEquals(78.2, $debitNoteTransaction->incomeDebitNoteItems->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 2 for income debit note transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 2]);
    $debitNoteInput = IncomeDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $debitNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $debitNoteInput['items'][0]['rpu'] = 100.********;
    $debitNoteInput['items'][0]['rpu_without_gst'] = 100.********;
    $debitNoteInput['items'][0]['rpu_with_gst'] = 100.********;
    $debitNoteInput['items'][0]['gst_id'] = $gst->id;
    $debitNoteInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('income-debit-note'), $debitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Debit Note Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('income_debit_note_transactions', [
        'id' => $response->json('data.income_debit_note_transaction.id'),
        'company_id' => 1,
        'debit_note_number' => $debitNoteInput['debit_note_number'],
        'dn_item_type' => IncomeDebitNoteTransaction::ITEM_INVOICE,
    ]);
    $debitNoteTransaction = IncomeDebitNoteTransaction::with('incomeDebitNoteItems')->whereId($response->json('data.income_debit_note_transaction.id'))->first();
    $this->assertEquals(78.22, $debitNoteTransaction->incomeDebitNoteItems->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 3 for income debit note transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 3]);
    $debitNoteInput = IncomeDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $debitNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $debitNoteInput['items'][0]['rpu'] = 100.********;
    $debitNoteInput['items'][0]['rpu_without_gst'] = 100.********;
    $debitNoteInput['items'][0]['rpu_with_gst'] = 100.********;
    $debitNoteInput['items'][0]['gst_id'] = $gst->id;
    $debitNoteInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('income-debit-note'), $debitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Debit Note Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('income_debit_note_transactions', [
        'id' => $response->json('data.income_debit_note_transaction.id'),
        'company_id' => 1,
        'debit_note_number' => $debitNoteInput['debit_note_number'],
        'dn_item_type' => IncomeDebitNoteTransaction::ITEM_INVOICE,
    ]);
    $debitNoteTransaction = IncomeDebitNoteTransaction::with('incomeDebitNoteItems')->whereId($response->json('data.income_debit_note_transaction.id'))->first();
    $this->assertEquals(78.220, $debitNoteTransaction->incomeDebitNoteItems->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 4 for income debit note transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 4]);
    $debitNoteInput = IncomeDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $debitNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $debitNoteInput['items'][0]['rpu'] = 100.********;
    $debitNoteInput['items'][0]['rpu_without_gst'] = 100.********;
    $debitNoteInput['items'][0]['rpu_with_gst'] = 100.********;
    $debitNoteInput['items'][0]['gst_id'] = $gst->id;
    $debitNoteInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('income-debit-note'), $debitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Debit Note Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('income_debit_note_transactions', [
        'id' => $response->json('data.income_debit_note_transaction.id'),
        'company_id' => 1,
        'debit_note_number' => $debitNoteInput['debit_note_number'],
        'dn_item_type' => IncomeDebitNoteTransaction::ITEM_INVOICE,
    ]);
    $debitNoteTransaction = IncomeDebitNoteTransaction::with('incomeDebitNoteItems')->whereId($response->json('data.income_debit_note_transaction.id'))->first();
    $this->assertEquals(78.2197, $debitNoteTransaction->incomeDebitNoteItems->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 5 for income debit note transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 5]);
    $debitNoteInput = IncomeDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $debitNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $debitNoteInput['items'][0]['rpu'] = 100.********;
    $debitNoteInput['items'][0]['rpu_without_gst'] = 100.********;
    $debitNoteInput['items'][0]['rpu_with_gst'] = 100.********;
    $debitNoteInput['items'][0]['gst_id'] = $gst->id;
    $debitNoteInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('income-debit-note'), $debitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Debit Note Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('income_debit_note_transactions', [
        'id' => $response->json('data.income_debit_note_transaction.id'),
        'company_id' => 1,
        'debit_note_number' => $debitNoteInput['debit_note_number'],
        'dn_item_type' => IncomeDebitNoteTransaction::ITEM_INVOICE,
    ]);
    $debitNoteTransaction = IncomeDebitNoteTransaction::with('incomeDebitNoteItems')->whereId($response->json('data.income_debit_note_transaction.id'))->first();
    $this->assertEquals(78.21970, $debitNoteTransaction->incomeDebitNoteItems->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 1 for income debit note transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 1]);
    $debitNoteInput = IncomeDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $debitNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $debitNoteInput['items'][0]['rpu'] = 100.********;
    $debitNoteInput['items'][0]['rpu_without_gst'] = 100.********;
    $debitNoteInput['items'][0]['rpu_with_gst'] = 100.********;
    $debitNoteInput['items'][0]['gst_id'] = $gst->id;
    $debitNoteInput['items'][0]['gst_tax_percentage'] = 2;
    $debitNoteInput['items'][0]['with_tax'] = false;

    //act
    $response = $this->postJson(route('income-debit-note'), $debitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Debit Note Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('income_debit_note_transactions', [
        'id' => $response->json('data.income_debit_note_transaction.id'),
        'company_id' => 1,
        'debit_note_number' => $response->json('data.income_debit_note_transaction.debit_note_number'),
        'dn_item_type' => IncomeDebitNoteTransaction::ITEM_INVOICE,
    ]);
    $debitNoteTransaction = IncomeDebitNoteTransaction::with('incomeDebitNoteItems')->whereId($response->json('data.income_debit_note_transaction.id'))->first();
    $this->assertEquals(128.2, $debitNoteTransaction->incomeDebitNoteItems->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 2 for income debit note transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 2]);
    $debitNoteInput = IncomeDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $debitNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $debitNoteInput['items'][0]['rpu'] = 100.********;
    $debitNoteInput['items'][0]['rpu_without_gst'] = 100.********;
    $debitNoteInput['items'][0]['rpu_with_gst'] = 100.********;
    $debitNoteInput['items'][0]['gst_id'] = $gst->id;
    $debitNoteInput['items'][0]['gst_tax_percentage'] = 2;
    $debitNoteInput['items'][0]['with_tax'] = false;

    //act
    $response = $this->postJson(route('income-debit-note'), $debitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Debit Note Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('income_debit_note_transactions', [
        'id' => $response->json('data.income_debit_note_transaction.id'),
        'company_id' => 1,
        'debit_note_number' => $response->json('data.income_debit_note_transaction.debit_note_number'),
        'dn_item_type' => IncomeDebitNoteTransaction::ITEM_INVOICE,
    ]);
    $debitNoteTransaction = IncomeDebitNoteTransaction::with('incomeDebitNoteItems')->whereId($response->json('data.income_debit_note_transaction.id'))->first();
    $this->assertEquals(128.16, $debitNoteTransaction->incomeDebitNoteItems->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 3 for income debit note transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 3]);
    $debitNoteInput = IncomeDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $debitNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $debitNoteInput['items'][0]['rpu'] = 100.********;
    $debitNoteInput['items'][0]['rpu_without_gst'] = 100.********;
    $debitNoteInput['items'][0]['rpu_with_gst'] = 100.********;
    $debitNoteInput['items'][0]['gst_id'] = $gst->id;
    $debitNoteInput['items'][0]['gst_tax_percentage'] = 2;
    $debitNoteInput['items'][0]['with_tax'] = false;

    //act
    $response = $this->postJson(route('income-debit-note'), $debitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Debit Note Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('income_debit_note_transactions', [
        'id' => $response->json('data.income_debit_note_transaction.id'),
        'company_id' => 1,
        'debit_note_number' => $response->json('data.income_debit_note_transaction.debit_note_number'),
        'dn_item_type' => IncomeDebitNoteTransaction::ITEM_INVOICE,
    ]);
    $debitNoteTransaction = IncomeDebitNoteTransaction::with('incomeDebitNoteItems')->whereId($response->json('data.income_debit_note_transaction.id'))->first();
    $this->assertEquals(128.155, $debitNoteTransaction->incomeDebitNoteItems->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 4 for income debit note transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 4]);
    $debitNoteInput = IncomeDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $debitNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $debitNoteInput['items'][0]['rpu'] = 100.********;
    $debitNoteInput['items'][0]['rpu_without_gst'] = 100.********;
    $debitNoteInput['items'][0]['rpu_with_gst'] = 100.********;
    $debitNoteInput['items'][0]['gst_id'] = $gst->id;
    $debitNoteInput['items'][0]['gst_tax_percentage'] = 2;
    $debitNoteInput['items'][0]['with_tax'] = false;

    //act
    $response = $this->postJson(route('income-debit-note'), $debitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Debit Note Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('income_debit_note_transactions', [
        'id' => $response->json('data.income_debit_note_transaction.id'),
        'company_id' => 1,
        'debit_note_number' => $response->json('data.income_debit_note_transaction.debit_note_number'),
        'dn_item_type' => IncomeDebitNoteTransaction::ITEM_INVOICE,
    ]);
    $debitNoteTransaction = IncomeDebitNoteTransaction::with('incomeDebitNoteItems')->whereId($response->json('data.income_debit_note_transaction.id'))->first();
    $this->assertEquals(128.1552, $debitNoteTransaction->incomeDebitNoteItems->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 5 for income debit note transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 5]);
    $debitNoteInput = IncomeDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $debitNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $debitNoteInput['items'][0]['rpu'] = 100.********;
    $debitNoteInput['items'][0]['rpu_without_gst'] = 100.********;
    $debitNoteInput['items'][0]['rpu_with_gst'] = 100.********;
    $debitNoteInput['items'][0]['gst_id'] = $gst->id;
    $debitNoteInput['items'][0]['gst_tax_percentage'] = 2;
    $debitNoteInput['items'][0]['with_tax'] = false;

    //act
    $response = $this->postJson(route('income-debit-note'), $debitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Debit Note Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('income_debit_note_transactions', [
        'id' => $response->json('data.income_debit_note_transaction.id'),
        'company_id' => 1,
        'debit_note_number' => $response->json('data.income_debit_note_transaction.debit_note_number'),
        'dn_item_type' => IncomeDebitNoteTransaction::ITEM_INVOICE,
    ]);
    $debitNoteTransaction = IncomeDebitNoteTransaction::with('incomeDebitNoteItems')->whereId($response->json('data.income_debit_note_transaction.id'))->first();
    $this->assertEquals(128.15515, $debitNoteTransaction->incomeDebitNoteItems->first()->rpu_with_gst);
});

test('validation with update negative and wrong rpu without or with gst for income debit note transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $debitNoteInput = IncomeDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $debitNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $debitNoteInput['items'][0]['rpu_without_gst'] = -10000;
    $debitNoteInput['items'][0]['rpu_with_gst'] = -10000;
    $debitNoteInput['items'][0]['gst_id'] = $gst->id;
    $debitNoteInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('income-debit-note'), $debitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Debit Note Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('income_debit_note_transactions', [
        'id' => $response->json('data.income_debit_note_transaction.id'),
        'company_id' => 1,
        'debit_note_number' => $response->json('data.income_debit_note_transaction.debit_note_number'),
        'dn_item_type' => IncomeDebitNoteTransaction::ITEM_INVOICE,
    ]);
    $debitNoteTransaction = IncomeDebitNoteTransaction::with('incomeDebitNoteItems')->whereId($response->json('data.income_debit_note_transaction.id'))->first();
    $this->assertNotEquals(100, $debitNoteTransaction->incomeDebitNoteItems->first()->rpu_without_gst);
    $this->assertEquals(100.0, $debitNoteTransaction->incomeDebitNoteItems->first()->rpu_with_gst);
});

test('validation with update negative and wrong gst tax percentage for income debit note transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $debitNoteInput = IncomeDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $debitNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $debitNoteInput['items'][0]['total'] = -10000;
    $debitNoteInput['items'][0]['gst_id'] = $gst->id;
    $debitNoteInput['items'][0]['gst_tax_percentage'] = $gst->tax_rate;

    //act
    $response = $this->postJson(route('income-debit-note'), $debitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Debit Note Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('income_debit_note_transactions', [
        'id' => $response->json('data.income_debit_note_transaction.id'),
        'company_id' => 1,
        'debit_note_number' => $response->json('data.income_debit_note_transaction.debit_note_number'),
        'dn_item_type' => IncomeDebitNoteTransaction::ITEM_INVOICE,
    ]);
    $debitNoteTransaction = IncomeDebitNoteTransaction::with('incomeDebitNoteItems')->whereId($response->json('data.income_debit_note_transaction.id'))->first();
    $this->assertEquals(28, intval($debitNoteTransaction->incomeDebitNoteItems->first()->gst_tax_percentage));
});

test('validation with update negative and wrong total discount for income debit note transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(5.00)->first();

    $debitNoteInput = IncomeDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $debitNoteInput['date'] = Carbon::now()->format('d-m-Y');
    foreach ($debitNoteInput['items'] as $key => $item) {
        $debitNoteInput['items'][$key]['gst_id'] = $gst->id;
        $debitNoteInput['items'][$key]['discount_type'] = 2;
        $debitNoteInput['items'][$key]['discount_value'] = 10;
        $debitNoteInput['items'][$key]['discount_type_2'] = 2;
        $debitNoteInput['items'][$key]['discount_value_2'] = 10;
        $debitNoteInput['items'][$key]['total_discount_amount'] = 10;
    }

    //act
    $response = $this->postJson(route('income-debit-note'), $debitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Debit Note Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('income_debit_note_transactions', [
        'id' => $response->json('data.income_debit_note_transaction.id'),
        'company_id' => 1,
        'debit_note_number' => $response->json('data.income_debit_note_transaction.debit_note_number'),
        'dn_item_type' => IncomeDebitNoteTransaction::ITEM_INVOICE,
    ]);
    $debitNoteTransaction = IncomeDebitNoteTransaction::with('incomeDebitNoteItems')->whereId($response->json('data.income_debit_note_transaction.id'))->first();
    foreach ($debitNoteTransaction->incomeDebitNoteItems as $item) {
        $this->assertEquals(18.1, $item->total_discount_amount);
    }
});

test('validation with update negative and wrong sub total for income debit note transaction item invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $debitNoteInput = IncomeDebitNoteTransactionFactory::new()->make()->toArray();
    $debitNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $debitNoteInput['items'][0]['total'] = -10000;

    //act
    $response = $this->postJson(route('income-debit-note'), $debitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Debit Note Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('income_debit_note_transactions', [
        'id' => $response->json('data.income_debit_note_transaction.id'),
        'company_id' => 1,
        'debit_note_number' => $response->json('data.income_debit_note_transaction.debit_note_number'),
        'dn_item_type' => IncomeDebitNoteTransaction::ITEM_INVOICE,
    ]);
    $debitNoteTransaction = IncomeDebitNoteTransaction::with('incomeDebitNoteItems')->whereId($response->json('data.income_debit_note_transaction.id'))->first();
    $this->assertEquals(100, intval($debitNoteTransaction->incomeDebitNoteItems->first()->total));
});

test('validation with update negative and wrong total for income debit note transaction item invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $debitNoteInput = IncomeDebitNoteTransactionFactory::new()->make(['total' => -620])->toArray();
    $debitNoteInput['date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = $this->postJson(route('income-debit-note'), $debitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Debit Note Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('income_debit_note_transactions', [
        'id' => $response->json('data.income_debit_note_transaction.id'),
        'company_id' => 1,
        'debit_note_number' => $response->json('data.income_debit_note_transaction.debit_note_number'),
        'dn_item_type' => IncomeDebitNoteTransaction::ITEM_INVOICE,
        'total' => 620.00,
    ]);
});

test('validation with update negative and wrong gross value for income debit note transaction item invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $debitNoteInput = IncomeDebitNoteTransactionFactory::new()->make(['gross_value' => -100])->toArray();
    $debitNoteInput['date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = $this->postJson(route('income-debit-note'), $debitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Debit Note Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('income_debit_note_transactions', [
        'id' => $response->json('data.income_debit_note_transaction.id'),
        'company_id' => 1,
        'debit_note_number' => $response->json('data.income_debit_note_transaction.debit_note_number'),
        'dn_item_type' => IncomeDebitNoteTransaction::ITEM_INVOICE,
        'gross_value' => 600.00,
    ]);
});

test('validation with update negative and wrong additional charges gst tax percentage for income debit note transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $debitNoteInput = IncomeDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $debitNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $debitNoteInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    $debitNoteInput['additional_charges'][0]['ac_total'] = -2000;

    //act
    $response = $this->postJson(route('income-debit-note'), $debitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Debit Note Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('income_debit_note_transactions', [
        'id' => $response->json('data.income_debit_note_transaction.id'),
        'company_id' => 1,
        'debit_note_number' => $response->json('data.income_debit_note_transaction.debit_note_number'),
        'dn_item_type' => IncomeDebitNoteTransaction::ITEM_INVOICE,
    ]);
    $debitNoteTransaction = IncomeDebitNoteTransaction::with('additionalCharges')->whereId($response->json('data.income_debit_note_transaction.id'))->first();
    $this->assertNotEquals(10.00, $debitNoteTransaction->additionalCharges->first()->total);
});

test('validation with update negative and wrong additional charges total for income debit note transaction item invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $debitNoteInput = IncomeDebitNoteTransactionFactory::new()->make()->toArray();
    $debitNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $debitNoteInput['additional_charges'][0]['ac_total'] = -200;

    //act
    $response = $this->postJson(route('income-debit-note'), $debitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Debit Note Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('income_debit_note_transactions', [
        'id' => $response->json('data.income_debit_note_transaction.id'),
        'company_id' => 1,
        'debit_note_number' => $response->json('data.income_debit_note_transaction.debit_note_number'),
        'dn_item_type' => IncomeDebitNoteTransaction::ITEM_INVOICE,
        'gross_value' => 600.00,
    ]);
    $debitNoteTransaction = IncomeDebitNoteTransaction::with('additionalCharges')->whereId($response->json('data.income_debit_note_transaction.id'))->first();
    $this->assertEquals(10, intval($debitNoteTransaction->additionalCharges->first()->total));
});

test('validation with update negative and wrong cgst and sgst when sale intra state taxable classification for income debit note transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $debitNoteInput = IncomeDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'main_classification_nature_type' => 'Sale Intra State Taxable',
    ])->toArray();
    $debitNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $debitNoteInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    foreach ($debitNoteInput['items'] as $key => $item) {
        $debitNoteInput['items'][$key]['gst_id'] = $gst->id;
    }

    //act
    $response = $this->postJson(route('income-debit-note'), $debitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Debit Note Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('income_debit_note_transactions', [
        'id' => $response->json('data.income_debit_note_transaction.id'),
        'company_id' => 1,
        'debit_note_number' => $response->json('data.income_debit_note_transaction.debit_note_number'),
        'dn_item_type' => IncomeDebitNoteTransaction::ITEM_INVOICE,
    ]);
    $debitNoteTransaction = IncomeDebitNoteTransaction::whereId($response->json('data.income_debit_note_transaction.id'))->first();
    $this->assertEquals(67.04, $debitNoteTransaction->cgst);
    $this->assertEquals(67.04, $debitNoteTransaction->sgst);
});

test('validation with update negative and wrong igst when sale inter state taxable classification for income debit note transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $debitNoteInput = IncomeDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'main_classification_nature_type' => 'Sale Inter State Taxable',
    ])->toArray();
    $debitNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $debitNoteInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    foreach ($debitNoteInput['items'] as $key => $item) {
        $debitNoteInput['items'][$key]['gst_id'] = $gst->id;
    }

    //act
    $response = $this->postJson(route('income-debit-note'), $debitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Debit Note Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('income_debit_note_transactions', [
        'id' => $response->json('data.income_debit_note_transaction.id'),
        'company_id' => 1,
        'debit_note_number' => $response->json('data.income_debit_note_transaction.debit_note_number'),
        'dn_item_type' => IncomeDebitNoteTransaction::ITEM_INVOICE,
    ]);
    $debitNoteTransaction = IncomeDebitNoteTransaction::whereId($response->json('data.income_debit_note_transaction.id'))->first();
    $this->assertEquals(134.08, $debitNoteTransaction->igst);
});

test('validation with update negative and wrong taxable value for income debit note transaction item invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $debitNoteInput = IncomeDebitNoteTransactionFactory::new()->make(['taxable_value' => -100])->toArray();
    $debitNoteInput['date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = $this->postJson(route('income-debit-note'), $debitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Debit Note Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('income_debit_note_transactions', [
        'id' => $response->json('data.income_debit_note_transaction.id'),
        'company_id' => 1,
        'debit_note_number' => $response->json('data.income_debit_note_transaction.debit_note_number'),
        'dn_item_type' => IncomeDebitNoteTransaction::ITEM_INVOICE,
        'taxable_value' => 610.00,
    ]);
});

test('validation with update negative and wrong addless total for income debit note transaction item invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $debitNoteInput = IncomeDebitNoteTransactionFactory::new()->make()->toArray();
    $debitNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $debitNoteInput['add_less'][0]['al_total'] = -100;

    //act
    $response = $this->postJson(route('income-debit-note'), $debitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Debit Note Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('income_debit_note_transactions', [
        'id' => $response->json('data.income_debit_note_transaction.id'),
        'company_id' => 1,
        'debit_note_number' => $response->json('data.income_debit_note_transaction.debit_note_number'),
        'dn_item_type' => IncomeDebitNoteTransaction::ITEM_INVOICE,
        'gross_value' => 600.00,
    ]);
    $debitNoteTransaction = IncomeDebitNoteTransaction::with('addLess')->whereId($response->json('data.income_debit_note_transaction.id'))->first();
    $this->assertEquals(10, intval($debitNoteTransaction->addLess->first()->total));
});

test('validation with update negative and wrong grand total for income debit note transaction item invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $debitNoteInput = IncomeDebitNoteTransactionFactory::new()->make(['grand_total' => -100])->toArray();
    $debitNoteInput['date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = $this->postJson(route('income-debit-note'), $debitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Debit Note Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('income_debit_note_transactions', [
        'id' => $response->json('data.income_debit_note_transaction.id'),
        'company_id' => 1,
        'debit_note_number' => $response->json('data.income_debit_note_transaction.debit_note_number'),
        'dn_item_type' => IncomeDebitNoteTransaction::ITEM_INVOICE,
        'grand_total' => 620.00,
    ]);
});

/*
|----------------------------------------------------------------------------------------------------------------------------------------------------
| Store Income Debit Note Transaction Account Invoice Backend Validation with update Test Case
|----------------------------------------------------------------------------------------------------------------------------------------------------
*/

test('validation with update decimal rpu amount when decimal place is 1 for income debit note transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $debitNoteInput = IncomeDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'dn_item_type' => IncomeDebitNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $debitNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $debitNoteInput['ledgers'][0]['decimal'] = 1;
    $debitNoteInput['ledgers'][0]['rpu'] = 100.********;
    $debitNoteInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $debitNoteInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $debitNoteInput['ledgers'][0]['gst_id'] = $gst->id;
    $debitNoteInput['ledgers'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('income-debit-note'), $debitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Debit Note Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('income_debit_note_transactions', [
        'id' => $response->json('data.income_debit_note_transaction.id'),
        'company_id' => 1,
        'debit_note_number' => $response->json('data.income_debit_note_transaction.debit_note_number'),
        'dn_item_type' => IncomeDebitNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $debitNoteTransaction = IncomeDebitNoteTransaction::with('incomeDebitNoteLedgers')->whereId($response->json('data.income_debit_note_transaction.id'))->first();
    $this->assertEquals(78.2, $debitNoteTransaction->incomeDebitNoteLedgers->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 2 for income debit note transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $debitNoteInput = IncomeDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'dn_item_type' => IncomeDebitNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $debitNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $debitNoteInput['ledgers'][0]['decimal'] = 2;
    $debitNoteInput['ledgers'][0]['rpu'] = 100.********;
    $debitNoteInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $debitNoteInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $debitNoteInput['ledgers'][0]['gst_id'] = $gst->id;
    $debitNoteInput['ledgers'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('income-debit-note'), $debitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Debit Note Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('income_debit_note_transactions', [
        'id' => $response->json('data.income_debit_note_transaction.id'),
        'company_id' => 1,
        'debit_note_number' => $response->json('data.income_debit_note_transaction.debit_note_number'),
        'dn_item_type' => IncomeDebitNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $debitNoteTransaction = IncomeDebitNoteTransaction::with('incomeDebitNoteLedgers')->whereId($response->json('data.income_debit_note_transaction.id'))->first();
    $this->assertEquals(78.22, $debitNoteTransaction->incomeDebitNoteLedgers->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 3 for income debit note transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $debitNoteInput = IncomeDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'dn_item_type' => IncomeDebitNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $debitNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $debitNoteInput['ledgers'][0]['decimal'] = 3;
    $debitNoteInput['ledgers'][0]['rpu'] = 100.********;
    $debitNoteInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $debitNoteInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $debitNoteInput['ledgers'][0]['gst_id'] = $gst->id;
    $debitNoteInput['ledgers'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('income-debit-note'), $debitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Debit Note Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('income_debit_note_transactions', [
        'id' => $response->json('data.income_debit_note_transaction.id'),
        'company_id' => 1,
        'debit_note_number' => $response->json('data.income_debit_note_transaction.debit_note_number'),
        'dn_item_type' => IncomeDebitNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $debitNoteTransaction = IncomeDebitNoteTransaction::with('incomeDebitNoteLedgers')->whereId($response->json('data.income_debit_note_transaction.id'))->first();
    $this->assertEquals(78.220, $debitNoteTransaction->incomeDebitNoteLedgers->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 4 for income debit note transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $debitNoteInput = IncomeDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'dn_item_type' => IncomeDebitNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $debitNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $debitNoteInput['ledgers'][0]['decimal'] = 4;
    $debitNoteInput['ledgers'][0]['rpu'] = 100.********;
    $debitNoteInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $debitNoteInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $debitNoteInput['ledgers'][0]['gst_id'] = $gst->id;
    $debitNoteInput['ledgers'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('income-debit-note'), $debitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Debit Note Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('income_debit_note_transactions', [
        'id' => $response->json('data.income_debit_note_transaction.id'),
        'company_id' => 1,
        'debit_note_number' => $response->json('data.income_debit_note_transaction.debit_note_number'),
        'dn_item_type' => IncomeDebitNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $debitNoteTransaction = IncomeDebitNoteTransaction::with('incomeDebitNoteLedgers')->whereId($response->json('data.income_debit_note_transaction.id'))->first();
    $this->assertEquals(78.2197, $debitNoteTransaction->incomeDebitNoteLedgers->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 5 for income debit note transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $debitNoteInput = IncomeDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'dn_item_type' => IncomeDebitNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $debitNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $debitNoteInput['ledgers'][0]['decimal'] = 5;
    $debitNoteInput['ledgers'][0]['rpu'] = 100.********;
    $debitNoteInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $debitNoteInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $debitNoteInput['ledgers'][0]['gst_id'] = $gst->id;
    $debitNoteInput['ledgers'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('income-debit-note'), $debitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Debit Note Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('income_debit_note_transactions', [
        'id' => $response->json('data.income_debit_note_transaction.id'),
        'company_id' => 1,
        'debit_note_number' => $response->json('data.income_debit_note_transaction.debit_note_number'),
        'dn_item_type' => IncomeDebitNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $debitNoteTransaction = IncomeDebitNoteTransaction::with('incomeDebitNoteLedgers')->whereId($response->json('data.income_debit_note_transaction.id'))->first();
    $this->assertEquals(78.21970, $debitNoteTransaction->incomeDebitNoteLedgers->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 1 for income debit note transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $debitNoteInput = IncomeDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'dn_item_type' => IncomeDebitNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $debitNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $debitNoteInput['ledgers'][0]['decimal'] = 1;
    $debitNoteInput['ledgers'][0]['rpu'] = 100.********;
    $debitNoteInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $debitNoteInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $debitNoteInput['ledgers'][0]['gst_id'] = $gst->id;
    $debitNoteInput['ledgers'][0]['gst_tax_percentage'] = 2;
    $debitNoteInput['ledgers'][0]['with_tax'] = false;

    //act
    $response = $this->postJson(route('income-debit-note'), $debitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Debit Note Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('income_debit_note_transactions', [
        'id' => $response->json('data.income_debit_note_transaction.id'),
        'company_id' => 1,
        'debit_note_number' => $response->json('data.income_debit_note_transaction.debit_note_number'),
        'dn_item_type' => IncomeDebitNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $debitNoteTransaction = IncomeDebitNoteTransaction::with('incomeDebitNoteLedgers')->whereId($response->json('data.income_debit_note_transaction.id'))->first();
    $this->assertEquals(128.2, $debitNoteTransaction->incomeDebitNoteLedgers->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 2 for income debit note transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $debitNoteInput = IncomeDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'dn_item_type' => IncomeDebitNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $debitNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $debitNoteInput['ledgers'][0]['decimal'] = 2;
    $debitNoteInput['ledgers'][0]['rpu'] = 100.********;
    $debitNoteInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $debitNoteInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $debitNoteInput['ledgers'][0]['gst_id'] = $gst->id;
    $debitNoteInput['ledgers'][0]['gst_tax_percentage'] = 2;
    $debitNoteInput['ledgers'][0]['with_tax'] = false;

    //act
    $response = $this->postJson(route('income-debit-note'), $debitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Debit Note Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('income_debit_note_transactions', [
        'id' => $response->json('data.income_debit_note_transaction.id'),
        'company_id' => 1,
        'debit_note_number' => $response->json('data.income_debit_note_transaction.debit_note_number'),
        'dn_item_type' => IncomeDebitNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $debitNoteTransaction = IncomeDebitNoteTransaction::with('incomeDebitNoteLedgers')->whereId($response->json('data.income_debit_note_transaction.id'))->first();
    $this->assertEquals(128.16, $debitNoteTransaction->incomeDebitNoteLedgers->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 3 for income debit note transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $debitNoteInput = IncomeDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'dn_item_type' => IncomeDebitNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $debitNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $debitNoteInput['ledgers'][0]['decimal'] = 3;
    $debitNoteInput['ledgers'][0]['rpu'] = 100.********;
    $debitNoteInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $debitNoteInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $debitNoteInput['ledgers'][0]['gst_id'] = $gst->id;
    $debitNoteInput['ledgers'][0]['gst_tax_percentage'] = 2;
    $debitNoteInput['ledgers'][0]['with_tax'] = false;

    //act
    $response = $this->postJson(route('income-debit-note'), $debitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Debit Note Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('income_debit_note_transactions', [
        'id' => $response->json('data.income_debit_note_transaction.id'),
        'company_id' => 1,
        'debit_note_number' => $response->json('data.income_debit_note_transaction.debit_note_number'),
        'dn_item_type' => IncomeDebitNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $debitNoteTransaction = IncomeDebitNoteTransaction::with('incomeDebitNoteLedgers')->whereId($response->json('data.income_debit_note_transaction.id'))->first();
    $this->assertEquals(128.155, $debitNoteTransaction->incomeDebitNoteLedgers->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 4 for income debit note transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $debitNoteInput = IncomeDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'dn_item_type' => IncomeDebitNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $debitNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $debitNoteInput['ledgers'][0]['decimal'] = 4;
    $debitNoteInput['ledgers'][0]['rpu'] = 100.********;
    $debitNoteInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $debitNoteInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $debitNoteInput['ledgers'][0]['gst_id'] = $gst->id;
    $debitNoteInput['ledgers'][0]['gst_tax_percentage'] = 2;
    $debitNoteInput['ledgers'][0]['with_tax'] = false;

    //act
    $response = $this->postJson(route('income-debit-note'), $debitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Debit Note Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('income_debit_note_transactions', [
        'id' => $response->json('data.income_debit_note_transaction.id'),
        'company_id' => 1,
        'debit_note_number' => $response->json('data.income_debit_note_transaction.debit_note_number'),
        'dn_item_type' => IncomeDebitNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $debitNoteTransaction = IncomeDebitNoteTransaction::with('incomeDebitNoteLedgers')->whereId($response->json('data.income_debit_note_transaction.id'))->first();
    $this->assertEquals(128.1552, $debitNoteTransaction->incomeDebitNoteLedgers->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 5 for income debit note transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $debitNoteInput = IncomeDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'dn_item_type' => IncomeDebitNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $debitNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $debitNoteInput['ledgers'][0]['decimal'] = 5;
    $debitNoteInput['ledgers'][0]['rpu'] = 100.********;
    $debitNoteInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $debitNoteInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $debitNoteInput['ledgers'][0]['gst_id'] = $gst->id;
    $debitNoteInput['ledgers'][0]['gst_tax_percentage'] = 2;
    $debitNoteInput['ledgers'][0]['with_tax'] = false;

    //act
    $response = $this->postJson(route('income-debit-note'), $debitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Debit Note Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('income_debit_note_transactions', [
        'id' => $response->json('data.income_debit_note_transaction.id'),
        'company_id' => 1,
        'debit_note_number' => $response->json('data.income_debit_note_transaction.debit_note_number'),
        'dn_item_type' => IncomeDebitNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $debitNoteTransaction = IncomeDebitNoteTransaction::with('incomeDebitNoteLedgers')->whereId($response->json('data.income_debit_note_transaction.id'))->first();
    $this->assertEquals(128.15515, $debitNoteTransaction->incomeDebitNoteLedgers->first()->rpu_with_gst);
});

test('validation with update negative and wrong rpu without or with gst for income debit note transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $debitNoteInput = IncomeDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'dn_item_type' => IncomeDebitNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $debitNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $debitNoteInput['ledgers'][0]['rpu_without_gst'] = -10000;
    $debitNoteInput['ledgers'][0]['rpu_with_gst'] = -10000;
    $debitNoteInput['ledgers'][0]['gst_id'] = $gst->id;
    $debitNoteInput['ledgers'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('income-debit-note'), $debitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Debit Note Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('income_debit_note_transactions', [
        'id' => $response->json('data.income_debit_note_transaction.id'),
        'company_id' => 1,
        'debit_note_number' => $response->json('data.income_debit_note_transaction.debit_note_number'),
        'dn_item_type' => IncomeDebitNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $debitNoteTransaction = IncomeDebitNoteTransaction::with('incomeDebitNoteLedgers')->whereId($response->json('data.income_debit_note_transaction.id'))->first();
    $this->assertNotEquals(100, $debitNoteTransaction->incomeDebitNoteLedgers->first()->rpu_without_gst);
    $this->assertEquals(100.0, $debitNoteTransaction->incomeDebitNoteLedgers->first()->rpu_with_gst);
});

test('validation with update negative and wrong gst tax percentage for income debit note transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $debitNoteInput = IncomeDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'dn_item_type' => IncomeDebitNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $debitNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $debitNoteInput['ledgers'][0]['total'] = -10000;
    $debitNoteInput['ledgers'][0]['gst_id'] = $gst->id;
    $debitNoteInput['ledgers'][0]['gst_tax_percentage'] = $gst->tax_rate;

    //act
    $response = $this->postJson(route('income-debit-note'), $debitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Debit Note Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('income_debit_note_transactions', [
        'id' => $response->json('data.income_debit_note_transaction.id'),
        'company_id' => 1,
        'debit_note_number' => $response->json('data.income_debit_note_transaction.debit_note_number'),
        'dn_item_type' => IncomeDebitNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $debitNoteTransaction = IncomeDebitNoteTransaction::with('incomeDebitNoteLedgers')->whereId($response->json('data.income_debit_note_transaction.id'))->first();
    $this->assertEquals(28, intval($debitNoteTransaction->incomeDebitNoteLedgers->first()->gst_tax_percentage));
});

test('validation with update negative and wrong total discount for income debit note transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(5.00)->first();

    $debitNoteInput = IncomeDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'dn_item_type' => IncomeDebitNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $debitNoteInput['date'] = Carbon::now()->format('d-m-Y');
    foreach ($debitNoteInput['ledgers'] as $key => $ledger) {
        $debitNoteInput['ledgers'][$key]['gst_id'] = $gst->id;
        $debitNoteInput['ledgers'][$key]['discount_type'] = 2;
        $debitNoteInput['ledgers'][$key]['discount_value'] = 10;
        $debitNoteInput['ledgers'][$key]['discount_type_2'] = 2;
        $debitNoteInput['ledgers'][$key]['discount_value_2'] = 10;
        $debitNoteInput['ledgers'][$key]['total_discount_amount'] = 10;
    }

    //act
    $response = $this->postJson(route('income-debit-note'), $debitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Debit Note Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('income_debit_note_transactions', [
        'id' => $response->json('data.income_debit_note_transaction.id'),
        'company_id' => 1,
        'debit_note_number' => $response->json('data.income_debit_note_transaction.debit_note_number'),
        'dn_item_type' => IncomeDebitNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $debitNoteTransaction = IncomeDebitNoteTransaction::with('incomeDebitNoteLedgers')->whereId($response->json('data.income_debit_note_transaction.id'))->first();
    foreach ($debitNoteTransaction->incomeDebitNoteLedgers as $ledger) {
        $this->assertEquals(18.1, $ledger->total_discount_amount);
    }
});

test('validation with update negative and wrong sub total for income debit note transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $debitNoteInput = IncomeDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'dn_item_type' => IncomeDebitNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $debitNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $debitNoteInput['ledgers'][0]['total'] = -10000;
    $debitNoteInput['ledgers'][0]['gst_id'] = $gst->id;
    $debitNoteInput['ledgers'][0]['gst_tax_percentage'] = $gst->tax_rate;

    //act
    $response = $this->postJson(route('income-debit-note'), $debitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Debit Note Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('income_debit_note_transactions', [
        'id' => $response->json('data.income_debit_note_transaction.id'),
        'company_id' => 1,
        'debit_note_number' => $response->json('data.income_debit_note_transaction.debit_note_number'),
        'dn_item_type' => IncomeDebitNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $debitNoteTransaction = IncomeDebitNoteTransaction::with('incomeDebitNoteLedgers')->whereId($response->json('data.income_debit_note_transaction.id'))->first();
    $this->assertEquals(78.13, $debitNoteTransaction->incomeDebitNoteLedgers->first()->total);
});

test('validation with update negative and wrong sub total for income debit note transaction account invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $debitNoteInput = IncomeDebitNoteTransactionFactory::new()->make(['dn_item_type' => IncomeDebitNoteTransaction::ACCOUNTING_INVOICE])->toArray();
    $debitNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $debitNoteInput['ledgers'][0]['total'] = -10000;

    //act
    $response = $this->postJson(route('income-debit-note'), $debitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Debit Note Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('income_debit_note_transactions', [
        'id' => $response->json('data.income_debit_note_transaction.id'),
        'company_id' => 1,
        'debit_note_number' => $response->json('data.income_debit_note_transaction.debit_note_number'),
        'dn_item_type' => IncomeDebitNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $debitNoteTransaction = IncomeDebitNoteTransaction::with('incomeDebitNoteLedgers')->whereId($response->json('data.income_debit_note_transaction.id'))->first();
    $this->assertEquals(100, intval($debitNoteTransaction->incomeDebitNoteLedgers->first()->total));
});

test('validation with update negative and wrong total for income debit note transaction account invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $debitNoteInput = IncomeDebitNoteTransactionFactory::new()->make([
        'total' => -620,
        'dn_item_type' => IncomeDebitNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $debitNoteInput['date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = $this->postJson(route('income-debit-note'), $debitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Debit Note Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('income_debit_note_transactions', [
        'id' => $response->json('data.income_debit_note_transaction.id'),
        'company_id' => 1,
        'debit_note_number' => $response->json('data.income_debit_note_transaction.debit_note_number'),
        'dn_item_type' => IncomeDebitNoteTransaction::ACCOUNTING_INVOICE,
        'total' => 620.00,
    ]);
});

test('validation with update negative and wrong gross value for income debit note transaction account invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $debitNoteInput = IncomeDebitNoteTransactionFactory::new()->make([
        'gross_value' => -100,
        'dn_item_type' => IncomeDebitNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $debitNoteInput['date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = $this->postJson(route('income-debit-note'), $debitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Debit Note Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('income_debit_note_transactions', [
        'id' => $response->json('data.income_debit_note_transaction.id'),
        'company_id' => 1,
        'debit_note_number' => $response->json('data.income_debit_note_transaction.debit_note_number'),
        'dn_item_type' => IncomeDebitNoteTransaction::ACCOUNTING_INVOICE,
        'gross_value' => 600.00,
    ]);
});

test('validation with update negative and wrong additional charges gst tax percentage for income debit note transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $debitNoteInput = IncomeDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'dn_item_type' => IncomeDebitNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $debitNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $debitNoteInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    $debitNoteInput['additional_charges'][0]['ac_total'] = -2000;

    //act
    $response = $this->postJson(route('income-debit-note'), $debitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Debit Note Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('income_debit_note_transactions', [
        'id' => $response->json('data.income_debit_note_transaction.id'),
        'company_id' => 1,
        'debit_note_number' => $response->json('data.income_debit_note_transaction.debit_note_number'),
        'dn_item_type' => IncomeDebitNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $debitNoteTransaction = IncomeDebitNoteTransaction::with('additionalCharges')->whereId($response->json('data.income_debit_note_transaction.id'))->first();
    $this->assertNotEquals(10.00, $debitNoteTransaction->additionalCharges->first()->total);
});

test('validation with update negative and wrong additional charges total for income debit note transaction account invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $debitNoteInput = IncomeDebitNoteTransactionFactory::new()->make(['dn_item_type' => IncomeDebitNoteTransaction::ACCOUNTING_INVOICE])->toArray();
    $debitNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $debitNoteInput['additional_charges'][0]['ac_total'] = -200;

    //act
    $response = $this->postJson(route('income-debit-note'), $debitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Debit Note Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('income_debit_note_transactions', [
        'id' => $response->json('data.income_debit_note_transaction.id'),
        'company_id' => 1,
        'debit_note_number' => $response->json('data.income_debit_note_transaction.debit_note_number'),
        'dn_item_type' => IncomeDebitNoteTransaction::ACCOUNTING_INVOICE,
        'gross_value' => 600.00,
    ]);
    $debitNoteTransaction = IncomeDebitNoteTransaction::with('additionalCharges')->whereId($response->json('data.income_debit_note_transaction.id'))->first();
    $this->assertEquals(10, intval($debitNoteTransaction->additionalCharges->first()->total));
});

test('validation with update negative and wrong cgst and sgst when sale intra state taxable classification for income debit note transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $debitNoteInput = IncomeDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'main_classification_nature_type' => 'Sale Intra State Taxable',
        'dn_item_type' => IncomeDebitNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $debitNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $debitNoteInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    foreach ($debitNoteInput['ledgers'] as $key => $ledger) {
        $debitNoteInput['ledgers'][$key]['gst_id'] = $gst->id;
    }

    //act
    $response = $this->postJson(route('income-debit-note'), $debitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Debit Note Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('income_debit_note_transactions', [
        'id' => $response->json('data.income_debit_note_transaction.id'),
        'company_id' => 1,
        'debit_note_number' => $response->json('data.income_debit_note_transaction.debit_note_number'),
        'dn_item_type' => IncomeDebitNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $debitNoteTransaction = IncomeDebitNoteTransaction::whereId($response->json('data.income_debit_note_transaction.id'))->first();
    $this->assertEquals(67.04, $debitNoteTransaction->cgst);
    $this->assertEquals(67.04, $debitNoteTransaction->sgst);
});

test('validation with update negative and wrong igst when sale inter state taxable classification for income debit note transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $debitNoteInput = IncomeDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'main_classification_nature_type' => 'Sale Inter State Taxable',
        'dn_item_type' => IncomeDebitNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $debitNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $debitNoteInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    foreach ($debitNoteInput['ledgers'] as $key => $ledger) {
        $debitNoteInput['ledgers'][$key]['gst_id'] = $gst->id;
    }

    //act
    $response = $this->postJson(route('income-debit-note'), $debitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Debit Note Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('income_debit_note_transactions', [
        'id' => $response->json('data.income_debit_note_transaction.id'),
        'company_id' => 1,
        'debit_note_number' => $response->json('data.income_debit_note_transaction.debit_note_number'),
        'dn_item_type' => IncomeDebitNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $debitNoteTransaction = IncomeDebitNoteTransaction::whereId($response->json('data.income_debit_note_transaction.id'))->first();
    $this->assertEquals(134.08, $debitNoteTransaction->igst);
});

test('validation with update negative and wrong taxable value for income debit note transaction account invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $debitNoteInput = IncomeDebitNoteTransactionFactory::new()->make([
        'taxable_value' => -100,
        'dn_item_type' => IncomeDebitNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $debitNoteInput['date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = $this->postJson(route('income-debit-note'), $debitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Debit Note Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('income_debit_note_transactions', [
        'id' => $response->json('data.income_debit_note_transaction.id'),
        'company_id' => 1,
        'debit_note_number' => $response->json('data.income_debit_note_transaction.debit_note_number'),
        'dn_item_type' => IncomeDebitNoteTransaction::ACCOUNTING_INVOICE,
        'taxable_value' => 610.00,
    ]);
});

test('validation with update negative and wrong addless total for income debit note transaction account invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $debitNoteInput = IncomeDebitNoteTransactionFactory::new()->make([
        'dn_item_type' => IncomeDebitNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $debitNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $debitNoteInput['add_less'][0]['al_total'] = -100;

    //act
    $response = $this->postJson(route('income-debit-note'), $debitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Debit Note Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('income_debit_note_transactions', [
        'id' => $response->json('data.income_debit_note_transaction.id'),
        'company_id' => 1,
        'debit_note_number' => $response->json('data.income_debit_note_transaction.debit_note_number'),
        'dn_item_type' => IncomeDebitNoteTransaction::ACCOUNTING_INVOICE,
        'gross_value' => 600.00,
    ]);
    $debitNoteTransaction = IncomeDebitNoteTransaction::with('addLess')->whereId($response->json('data.income_debit_note_transaction.id'))->first();
    $this->assertEquals(10, intval($debitNoteTransaction->addLess->first()->total));
});

test('validation with update negative and wrong grand total for income debit note transaction account invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $debitNoteInput = IncomeDebitNoteTransactionFactory::new()->make([
        'grand_total' => -100,
        'dn_item_type' => IncomeDebitNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $debitNoteInput['date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = $this->postJson(route('income-debit-note'), $debitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Debit Note Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('income_debit_note_transactions', [
        'id' => $response->json('data.income_debit_note_transaction.id'),
        'company_id' => 1,
        'debit_note_number' => $response->json('data.income_debit_note_transaction.debit_note_number'),
        'dn_item_type' => IncomeDebitNoteTransaction::ACCOUNTING_INVOICE,
        'grand_total' => 620.00,
    ]);
});

test('validation with update negative and wrong round off amount and method for income debit note transaction', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);
    $debitNoteInput = IncomeDebitNoteTransactionFactory::new()->make()->toArray();
    $debitNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $debitNoteInput['additional_charges'][0]['ac_value'] = 10.54;

    $roundOffMethods = [
        ['method' => 1, 'correct_amount' => 0],
        ['method' => 2, 'correct_amount' => -0.54],
        ['method' => 3, 'correct_amount' => 0.46],
        ['method' => 4, 'correct_amount' => 0.46],
    ];
    foreach ($roundOffMethods as $roundOffMethod) {
        $debitNoteInput['debit_note_number'] = 'DN-'.$roundOffMethod['method'];
        $debitNoteInput['round_off_method'] = 0;
        IncomeDebitNoteTransactionMaster::whereCompanyId(1)->update(['round_off_method' => $roundOffMethod['method']]);
        $debitNoteInput['rounding_amount'] = -10.50;

        //act
        $response = $this->postJson(route('income-debit-note'), $debitNoteInput);

        //assert
        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Income Debit Note Transaction Created Successfully',
            ]);
        $this->assertDatabaseHas('income_debit_note_transactions', [
            'id' => $response->json('data.income_debit_note_transaction.id'),
            'company_id' => 1,
            'debit_note_number' => $response->json('data.income_debit_note_transaction.debit_note_number'),
            'round_off_method' => $roundOffMethod,
            'rounding_amount' => $roundOffMethod['correct_amount'],
        ]);
    }
});

/*
|----------------------------------------------------------------------------------------------------------------------------------------------------
| Update Income Debit Note Transaction Item Invoice Backend Validation with update Test Case
|----------------------------------------------------------------------------------------------------------------------------------------------------
*/

test('validation with update decimal rpu amount when decimal place is 1 for update income debit note transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 1]);
    $debitNoteInput = IncomeDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $debitNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $debitNoteData = StoreIncomeDebitNoteAction::run($debitNoteInput);
    $debitNoteInput['items'][0]['rpu'] = 100.********;
    $debitNoteInput['items'][0]['rpu_without_gst'] = 100.********;
    $debitNoteInput['items'][0]['rpu_with_gst'] = 100.********;
    $debitNoteInput['items'][0]['gst_id'] = $gst->id;
    $debitNoteInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('income-debit-note-update', $debitNoteData['transaction_id']), $debitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Debit Note Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_debit_note_transactions', [
        'id' => $debitNoteData['transaction_id'],
        'company_id' => 1,
        'debit_note_number' => $debitNoteInput['debit_note_number'],
        'dn_item_type' => IncomeDebitNoteTransaction::ITEM_INVOICE,
    ]);
    $debitNoteTransaction = IncomeDebitNoteTransaction::with('incomeDebitNoteItems')->whereId($debitNoteData['transaction_id'])->first();
    $this->assertEquals(78.2, $debitNoteTransaction->incomeDebitNoteItems->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 2 for update income debit note transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 2]);
    $debitNoteInput = IncomeDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $debitNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $debitNoteData = StoreIncomeDebitNoteAction::run($debitNoteInput);
    $debitNoteInput['items'][0]['rpu'] = 100.********;
    $debitNoteInput['items'][0]['rpu_without_gst'] = 100.********;
    $debitNoteInput['items'][0]['rpu_with_gst'] = 100.********;
    $debitNoteInput['items'][0]['gst_id'] = $gst->id;
    $debitNoteInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('income-debit-note-update', $debitNoteData['transaction_id']), $debitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Debit Note Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_debit_note_transactions', [
        'id' => $debitNoteData['transaction_id'],
        'company_id' => 1,
        'debit_note_number' => $debitNoteInput['debit_note_number'],
        'dn_item_type' => IncomeDebitNoteTransaction::ITEM_INVOICE,
    ]);
    $debitNoteTransaction = IncomeDebitNoteTransaction::with('incomeDebitNoteItems')->whereId($debitNoteData['transaction_id'])->first();
    $this->assertEquals(78.22, $debitNoteTransaction->incomeDebitNoteItems->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 3 for update income debit note transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 3]);
    $debitNoteInput = IncomeDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $debitNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $debitNoteData = StoreIncomeDebitNoteAction::run($debitNoteInput);
    $debitNoteInput['items'][0]['rpu'] = 100.********;
    $debitNoteInput['items'][0]['rpu_without_gst'] = 100.********;
    $debitNoteInput['items'][0]['rpu_with_gst'] = 100.********;
    $debitNoteInput['items'][0]['gst_id'] = $gst->id;
    $debitNoteInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('income-debit-note-update', $debitNoteData['transaction_id']), $debitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Debit Note Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_debit_note_transactions', [
        'id' => $debitNoteData['transaction_id'],
        'company_id' => 1,
        'debit_note_number' => $debitNoteInput['debit_note_number'],
        'dn_item_type' => IncomeDebitNoteTransaction::ITEM_INVOICE,
    ]);
    $debitNoteTransaction = IncomeDebitNoteTransaction::with('incomeDebitNoteItems')->whereId($debitNoteData['transaction_id'])->first();
    $this->assertEquals(78.220, $debitNoteTransaction->incomeDebitNoteItems->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 4 for update income debit note transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 4]);
    $debitNoteInput = IncomeDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $debitNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $debitNoteData = StoreIncomeDebitNoteAction::run($debitNoteInput);
    $debitNoteInput['items'][0]['rpu'] = 100.********;
    $debitNoteInput['items'][0]['rpu_without_gst'] = 100.********;
    $debitNoteInput['items'][0]['rpu_with_gst'] = 100.********;
    $debitNoteInput['items'][0]['gst_id'] = $gst->id;
    $debitNoteInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('income-debit-note-update', $debitNoteData['transaction_id']), $debitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Debit Note Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_debit_note_transactions', [
        'id' => $debitNoteData['transaction_id'],
        'company_id' => 1,
        'debit_note_number' => $debitNoteInput['debit_note_number'],
        'dn_item_type' => IncomeDebitNoteTransaction::ITEM_INVOICE,
    ]);
    $debitNoteTransaction = IncomeDebitNoteTransaction::with('incomeDebitNoteItems')->whereId($debitNoteData['transaction_id'])->first();
    $this->assertEquals(78.2197, $debitNoteTransaction->incomeDebitNoteItems->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 5 for update income debit note transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 5]);
    $debitNoteInput = IncomeDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $debitNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $debitNoteData = StoreIncomeDebitNoteAction::run($debitNoteInput);
    $debitNoteInput['items'][0]['rpu'] = 100.********;
    $debitNoteInput['items'][0]['rpu_without_gst'] = 100.********;
    $debitNoteInput['items'][0]['rpu_with_gst'] = 100.********;
    $debitNoteInput['items'][0]['gst_id'] = $gst->id;
    $debitNoteInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('income-debit-note-update', $debitNoteData['transaction_id']), $debitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Debit Note Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_debit_note_transactions', [
        'id' => $debitNoteData['transaction_id'],
        'company_id' => 1,
        'debit_note_number' => $debitNoteInput['debit_note_number'],
        'dn_item_type' => IncomeDebitNoteTransaction::ITEM_INVOICE,
    ]);
    $debitNoteTransaction = IncomeDebitNoteTransaction::with('incomeDebitNoteItems')->whereId($debitNoteData['transaction_id'])->first();
    $this->assertEquals(78.21970, $debitNoteTransaction->incomeDebitNoteItems->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 1 for update income debit note transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 1]);
    $debitNoteInput = IncomeDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $debitNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $debitNoteData = StoreIncomeDebitNoteAction::run($debitNoteInput);
    $debitNoteInput['items'][0]['rpu'] = 100.********;
    $debitNoteInput['items'][0]['rpu_without_gst'] = 100.********;
    $debitNoteInput['items'][0]['rpu_with_gst'] = 100.********;
    $debitNoteInput['items'][0]['gst_id'] = $gst->id;
    $debitNoteInput['items'][0]['gst_tax_percentage'] = 2;
    $debitNoteInput['items'][0]['with_tax'] = false;

    //act
    $response = $this->postJson(route('income-debit-note-update', $debitNoteData['transaction_id']), $debitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Debit Note Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_debit_note_transactions', [
        'id' => $debitNoteData['transaction_id'],
        'company_id' => 1,
        'debit_note_number' => $debitNoteInput['debit_note_number'],
        'dn_item_type' => IncomeDebitNoteTransaction::ITEM_INVOICE,
    ]);
    $debitNoteTransaction = IncomeDebitNoteTransaction::with('incomeDebitNoteItems')->whereId($debitNoteData['transaction_id'])->first();
    $this->assertEquals(128.2, $debitNoteTransaction->incomeDebitNoteItems->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 2 for update income debit note transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 2]);
    $debitNoteInput = IncomeDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $debitNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $debitNoteData = StoreIncomeDebitNoteAction::run($debitNoteInput);
    $debitNoteInput['items'][0]['rpu'] = 100.********;
    $debitNoteInput['items'][0]['rpu_without_gst'] = 100.********;
    $debitNoteInput['items'][0]['rpu_with_gst'] = 100.********;
    $debitNoteInput['items'][0]['gst_id'] = $gst->id;
    $debitNoteInput['items'][0]['gst_tax_percentage'] = 2;
    $debitNoteInput['items'][0]['with_tax'] = false;

    //act
    $response = $this->postJson(route('income-debit-note-update', $debitNoteData['transaction_id']), $debitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Debit Note Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_debit_note_transactions', [
        'id' => $debitNoteData['transaction_id'],
        'company_id' => 1,
        'debit_note_number' => $debitNoteInput['debit_note_number'],
        'dn_item_type' => IncomeDebitNoteTransaction::ITEM_INVOICE,
    ]);
    $debitNoteTransaction = IncomeDebitNoteTransaction::with('incomeDebitNoteItems')->whereId($debitNoteData['transaction_id'])->first();
    $this->assertEquals(128.16, $debitNoteTransaction->incomeDebitNoteItems->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 3 for update income debit note transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 3]);
    $debitNoteInput = IncomeDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $debitNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $debitNoteData = StoreIncomeDebitNoteAction::run($debitNoteInput);
    $debitNoteInput['items'][0]['rpu'] = 100.********;
    $debitNoteInput['items'][0]['rpu_without_gst'] = 100.********;
    $debitNoteInput['items'][0]['rpu_with_gst'] = 100.********;
    $debitNoteInput['items'][0]['gst_id'] = $gst->id;
    $debitNoteInput['items'][0]['gst_tax_percentage'] = 2;
    $debitNoteInput['items'][0]['with_tax'] = false;

    //act
    $response = $this->postJson(route('income-debit-note-update', $debitNoteData['transaction_id']), $debitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Debit Note Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_debit_note_transactions', [
        'id' => $debitNoteData['transaction_id'],
        'company_id' => 1,
        'debit_note_number' => $debitNoteInput['debit_note_number'],
        'dn_item_type' => IncomeDebitNoteTransaction::ITEM_INVOICE,
    ]);
    $debitNoteTransaction = IncomeDebitNoteTransaction::with('incomeDebitNoteItems')->whereId($debitNoteData['transaction_id'])->first();
    $this->assertEquals(128.155, $debitNoteTransaction->incomeDebitNoteItems->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 4 for update income debit note transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 4]);
    $debitNoteInput = IncomeDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $debitNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $debitNoteData = StoreIncomeDebitNoteAction::run($debitNoteInput);
    $debitNoteInput['items'][0]['rpu'] = 100.********;
    $debitNoteInput['items'][0]['rpu_without_gst'] = 100.********;
    $debitNoteInput['items'][0]['rpu_with_gst'] = 100.********;
    $debitNoteInput['items'][0]['gst_id'] = $gst->id;
    $debitNoteInput['items'][0]['gst_tax_percentage'] = 2;
    $debitNoteInput['items'][0]['with_tax'] = false;

    //act
    $response = $this->postJson(route('income-debit-note-update', $debitNoteData['transaction_id']), $debitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Debit Note Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_debit_note_transactions', [
        'id' => $debitNoteData['transaction_id'],
        'company_id' => 1,
        'debit_note_number' => $debitNoteInput['debit_note_number'],
        'dn_item_type' => IncomeDebitNoteTransaction::ITEM_INVOICE,
    ]);
    $debitNoteTransaction = IncomeDebitNoteTransaction::with('incomeDebitNoteItems')->whereId($debitNoteData['transaction_id'])->first();
    $this->assertEquals(128.1552, $debitNoteTransaction->incomeDebitNoteItems->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 5 for update income debit note transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 5]);
    $debitNoteInput = IncomeDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $debitNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $debitNoteData = StoreIncomeDebitNoteAction::run($debitNoteInput);
    $debitNoteInput['items'][0]['rpu'] = 100.********;
    $debitNoteInput['items'][0]['rpu_without_gst'] = 100.********;
    $debitNoteInput['items'][0]['rpu_with_gst'] = 100.********;
    $debitNoteInput['items'][0]['gst_id'] = $gst->id;
    $debitNoteInput['items'][0]['gst_tax_percentage'] = 2;
    $debitNoteInput['items'][0]['with_tax'] = false;

    //act
    $response = $this->postJson(route('income-debit-note-update', $debitNoteData['transaction_id']), $debitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Debit Note Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_debit_note_transactions', [
        'id' => $debitNoteData['transaction_id'],
        'company_id' => 1,
        'debit_note_number' => $debitNoteInput['debit_note_number'],
        'dn_item_type' => IncomeDebitNoteTransaction::ITEM_INVOICE,
    ]);
    $debitNoteTransaction = IncomeDebitNoteTransaction::with('incomeDebitNoteItems')->whereId($debitNoteData['transaction_id'])->first();
    $this->assertEquals(128.15515, $debitNoteTransaction->incomeDebitNoteItems->first()->rpu_with_gst);
});

test('validation with update negative and wrong rpu without or with gst for update income debit note transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $debitNoteInput = IncomeDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $debitNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $debitNoteData = StoreIncomeDebitNoteAction::run($debitNoteInput);
    $debitNoteInput['items'][0]['rpu_without_gst'] = -10000;
    $debitNoteInput['items'][0]['rpu_with_gst'] = -10000;
    $debitNoteInput['items'][0]['gst_id'] = $gst->id;
    $debitNoteInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('income-debit-note-update', $debitNoteData['transaction_id']), $debitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Debit Note Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_debit_note_transactions', [
        'id' => $debitNoteData['transaction_id'],
        'company_id' => 1,
        'debit_note_number' => $debitNoteInput['debit_note_number'],
        'dn_item_type' => IncomeDebitNoteTransaction::ITEM_INVOICE,
    ]);
    $debitNoteTransaction = IncomeDebitNoteTransaction::with('incomeDebitNoteItems')->whereId($debitNoteData['transaction_id'])->first();
    $this->assertNotEquals(100, $debitNoteTransaction->incomeDebitNoteItems->first()->rpu_without_gst);
    $this->assertEquals(100.0, $debitNoteTransaction->incomeDebitNoteItems->first()->rpu_with_gst);
});

test('validation with update negative and wrong gst tax percentage for update income debit note transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $debitNoteInput = IncomeDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $debitNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $debitNoteData = StoreIncomeDebitNoteAction::run($debitNoteInput);
    $debitNoteInput['items'][0]['total'] = -10000;
    $debitNoteInput['items'][0]['gst_id'] = $gst->id;
    $debitNoteInput['items'][0]['gst_tax_percentage'] = $gst->tax_rate;

    //act
    $response = $this->postJson(route('income-debit-note-update', $debitNoteData['transaction_id']), $debitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Debit Note Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_debit_note_transactions', [
        'id' => $debitNoteData['transaction_id'],
        'company_id' => 1,
        'debit_note_number' => $debitNoteInput['debit_note_number'],
        'dn_item_type' => IncomeDebitNoteTransaction::ITEM_INVOICE,
    ]);
    $debitNoteTransaction = IncomeDebitNoteTransaction::with('incomeDebitNoteItems')->whereId($debitNoteData['transaction_id'])->first();
    $this->assertEquals(28, intval($debitNoteTransaction->incomeDebitNoteItems->first()->gst_tax_percentage));
});

test('validation with update negative and wrong total discount for update income debit note transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(5.00)->first();

    $debitNoteInput = IncomeDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $debitNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $debitNoteData = StoreIncomeDebitNoteAction::run($debitNoteInput);
    foreach ($debitNoteInput['items'] as $key => $item) {
        $debitNoteInput['items'][$key]['gst_id'] = $gst->id;
        $debitNoteInput['items'][$key]['discount_type'] = 2;
        $debitNoteInput['items'][$key]['discount_value'] = 10;
        $debitNoteInput['items'][$key]['discount_type_2'] = 2;
        $debitNoteInput['items'][$key]['discount_value_2'] = 10;
        $debitNoteInput['items'][$key]['total_discount_amount'] = 10;
    }

    //act
    $response = $this->postJson(route('income-debit-note-update', $debitNoteData['transaction_id']), $debitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Debit Note Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_debit_note_transactions', [
        'id' => $debitNoteData['transaction_id'],
        'company_id' => 1,
        'debit_note_number' => $debitNoteInput['debit_note_number'],
        'dn_item_type' => IncomeDebitNoteTransaction::ITEM_INVOICE,
    ]);
    $debitNoteTransaction = IncomeDebitNoteTransaction::with('incomeDebitNoteItems')->whereId($debitNoteData['transaction_id'])->first();
    foreach ($debitNoteTransaction->incomeDebitNoteItems as $item) {
        $this->assertEquals(18.1, $item->total_discount_amount);
    }
});

test('validation with update negative and wrong sub total for update income debit note transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $debitNoteInput = IncomeDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $debitNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $debitNoteData = StoreIncomeDebitNoteAction::run($debitNoteInput);
    $debitNoteInput['items'][0]['total'] = -10000;
    $debitNoteInput['items'][0]['gst_id'] = $gst->id;
    $debitNoteInput['items'][0]['gst_tax_percentage'] = $gst->tax_rate;

    //act
    $response = $this->postJson(route('income-debit-note-update', $debitNoteData['transaction_id']), $debitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Debit Note Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_debit_note_transactions', [
        'id' => $debitNoteData['transaction_id'],
        'company_id' => 1,
        'debit_note_number' => $debitNoteInput['debit_note_number'],
        'dn_item_type' => IncomeDebitNoteTransaction::ITEM_INVOICE,
    ]);
    $debitNoteTransaction = IncomeDebitNoteTransaction::with('incomeDebitNoteItems')->whereId($debitNoteData['transaction_id'])->first();
    $this->assertEquals(78.13, $debitNoteTransaction->incomeDebitNoteItems->first()->total);
});

test('validation with update negative and wrong sub total for update income debit note transaction item invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $debitNoteInput = IncomeDebitNoteTransactionFactory::new()->make()->toArray();
    $debitNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $debitNoteData = StoreIncomeDebitNoteAction::run($debitNoteInput);
    $debitNoteInput['items'][0]['total'] = -10000;

    //act
    $response = $this->postJson(route('income-debit-note-update', $debitNoteData['transaction_id']), $debitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Debit Note Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_debit_note_transactions', [
        'id' => $debitNoteData['transaction_id'],
        'company_id' => 1,
        'debit_note_number' => $debitNoteInput['debit_note_number'],
        'dn_item_type' => IncomeDebitNoteTransaction::ITEM_INVOICE,
    ]);
    $debitNoteTransaction = IncomeDebitNoteTransaction::with('incomeDebitNoteItems')->whereId($debitNoteData['transaction_id'])->first();
    $this->assertEquals(100, intval($debitNoteTransaction->incomeDebitNoteItems->first()->total));
});

test('validation with update negative and wrong total for update income debit note transaction item invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $debitNoteInput = IncomeDebitNoteTransactionFactory::new()->make()->toArray();
    $debitNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $debitNoteData = StoreIncomeDebitNoteAction::run($debitNoteInput);
    $debitNoteInput['total'] = -620;

    //act
    $response = $this->postJson(route('income-debit-note-update', $debitNoteData['transaction_id']), $debitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Debit Note Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_debit_note_transactions', [
        'id' => $debitNoteData['transaction_id'],
        'company_id' => 1,
        'debit_note_number' => $debitNoteInput['debit_note_number'],
        'dn_item_type' => IncomeDebitNoteTransaction::ITEM_INVOICE,
        'total' => 620.00,
    ]);
});

test('validation with update negative and wrong gross value for update income debit note transaction item invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $debitNoteInput = IncomeDebitNoteTransactionFactory::new()->make()->toArray();
    $debitNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $debitNoteData = StoreIncomeDebitNoteAction::run($debitNoteInput);
    $debitNoteInput['gross_value'] = -100;

    //act
    $response = $this->postJson(route('income-debit-note-update', $debitNoteData['transaction_id']), $debitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Debit Note Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_debit_note_transactions', [
        'id' => $debitNoteData['transaction_id'],
        'company_id' => 1,
        'debit_note_number' => $debitNoteInput['debit_note_number'],
        'dn_item_type' => IncomeDebitNoteTransaction::ITEM_INVOICE,
        'gross_value' => 600.00,
    ]);
});

test('validation with update negative and wrong additional charges gst tax percentage for update income debit note transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $debitNoteInput = IncomeDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $debitNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $debitNoteData = StoreIncomeDebitNoteAction::run($debitNoteInput);
    $debitNoteInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    $debitNoteInput['additional_charges'][0]['ac_total'] = -2000;

    //act
    $response = $this->postJson(route('income-debit-note-update', $debitNoteData['transaction_id']), $debitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Debit Note Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_debit_note_transactions', [
        'id' => $debitNoteData['transaction_id'],
        'company_id' => 1,
        'debit_note_number' => $debitNoteInput['debit_note_number'],
        'dn_item_type' => IncomeDebitNoteTransaction::ITEM_INVOICE,
    ]);
    $debitNoteTransaction = IncomeDebitNoteTransaction::with('additionalCharges')->whereId($debitNoteData['transaction_id'])->first();
    $this->assertNotEquals(10.00, $debitNoteTransaction->additionalCharges->first()->total);
});

test('validation with update negative and wrong additional charges total for update income debit note transaction item invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $debitNoteInput = IncomeDebitNoteTransactionFactory::new()->make()->toArray();
    $debitNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $debitNoteData = StoreIncomeDebitNoteAction::run($debitNoteInput);
    $debitNoteInput['additional_charges'][0]['ac_total'] = -200;

    //act
    $response = $this->postJson(route('income-debit-note-update', $debitNoteData['transaction_id']), $debitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Debit Note Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_debit_note_transactions', [
        'id' => $debitNoteData['transaction_id'],
        'company_id' => 1,
        'debit_note_number' => $debitNoteInput['debit_note_number'],
        'dn_item_type' => IncomeDebitNoteTransaction::ITEM_INVOICE,
        'gross_value' => 600.00,
    ]);
    $debitNoteTransaction = IncomeDebitNoteTransaction::with('additionalCharges')->whereId($debitNoteData['transaction_id'])->first();
    $this->assertEquals(10, intval($debitNoteTransaction->additionalCharges->first()->total));
});

test('validation with update negative and wrong cgst and sgst when sale intra state taxable classification for update income debit note transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $debitNoteInput = IncomeDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'main_classification_nature_type' => 'Sale Intra State Taxable',
    ])->toArray();
    $debitNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $debitNoteData = StoreIncomeDebitNoteAction::run($debitNoteInput);
    $debitNoteInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    foreach ($debitNoteInput['items'] as $key => $item) {
        $debitNoteInput['items'][$key]['gst_id'] = $gst->id;
    }

    //act
    $response = $this->postJson(route('income-debit-note-update', $debitNoteData['transaction_id']), $debitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Debit Note Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_debit_note_transactions', [
        'id' => $debitNoteData['transaction_id'],
        'company_id' => 1,
        'debit_note_number' => $debitNoteInput['debit_note_number'],
        'dn_item_type' => IncomeDebitNoteTransaction::ITEM_INVOICE,
    ]);
    $debitNoteTransaction = IncomeDebitNoteTransaction::whereId($debitNoteData['transaction_id'])->first();
    $this->assertEquals(67.04, $debitNoteTransaction->cgst);
    $this->assertEquals(67.04, $debitNoteTransaction->sgst);
});

test('validation with update negative and wrong igst when sale inter state taxable classification for update income debit note transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $debitNoteInput = IncomeDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'main_classification_nature_type' => 'Sale Inter State Taxable',
    ])->toArray();
    $debitNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $debitNoteData = StoreIncomeDebitNoteAction::run($debitNoteInput);
    $debitNoteInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    foreach ($debitNoteInput['items'] as $key => $item) {
        $debitNoteInput['items'][$key]['gst_id'] = $gst->id;
    }

    //act
    $response = $this->postJson(route('income-debit-note-update', $debitNoteData['transaction_id']), $debitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Debit Note Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_debit_note_transactions', [
        'id' => $debitNoteData['transaction_id'],
        'company_id' => 1,
        'debit_note_number' => $debitNoteInput['debit_note_number'],
        'dn_item_type' => IncomeDebitNoteTransaction::ITEM_INVOICE,
    ]);
    $debitNoteTransaction = IncomeDebitNoteTransaction::whereId($debitNoteData['transaction_id'])->first();
    $this->assertEquals(134.08, $debitNoteTransaction->igst);
});

test('validation with update negative and wrong taxable value for update income debit note transaction item invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $debitNoteInput = IncomeDebitNoteTransactionFactory::new()->make()->toArray();
    $debitNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $debitNoteData = StoreIncomeDebitNoteAction::run($debitNoteInput);
    $debitNoteInput['taxable_value'] = -100;

    //act
    $response = $this->postJson(route('income-debit-note-update', $debitNoteData['transaction_id']), $debitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Debit Note Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_debit_note_transactions', [
        'id' => $debitNoteData['transaction_id'],
        'company_id' => 1,
        'debit_note_number' => $debitNoteInput['debit_note_number'],
        'dn_item_type' => IncomeDebitNoteTransaction::ITEM_INVOICE,
        'taxable_value' => 610.00,
    ]);
});

test('validation with update negative and wrong addless total for update income debit note transaction item invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $debitNoteInput = IncomeDebitNoteTransactionFactory::new()->make()->toArray();
    $debitNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $debitNoteData = StoreIncomeDebitNoteAction::run($debitNoteInput);
    $debitNoteInput['add_less'][0]['al_total'] = -100;

    //act
    $response = $this->postJson(route('income-debit-note-update', $debitNoteData['transaction_id']), $debitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Debit Note Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_debit_note_transactions', [
        'id' => $debitNoteData['transaction_id'],
        'company_id' => 1,
        'debit_note_number' => $debitNoteInput['debit_note_number'],
        'dn_item_type' => IncomeDebitNoteTransaction::ITEM_INVOICE,
        'gross_value' => 600.00,
    ]);
    $debitNoteTransaction = IncomeDebitNoteTransaction::with('addLess')->whereId($debitNoteData['transaction_id'])->first();
    $this->assertEquals(10, intval($debitNoteTransaction->addLess->first()->total));
});

test('validation with update negative and wrong grand total for update income debit note transaction item invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $debitNoteInput = IncomeDebitNoteTransactionFactory::new()->make()->toArray();
    $debitNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $debitNoteData = StoreIncomeDebitNoteAction::run($debitNoteInput);
    $debitNoteInput['grand_total'] = -100;

    //act
    $response = $this->postJson(route('income-debit-note-update', $debitNoteData['transaction_id']), $debitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Debit Note Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_debit_note_transactions', [
        'id' => $debitNoteData['transaction_id'],
        'company_id' => 1,
        'debit_note_number' => $debitNoteInput['debit_note_number'],
        'dn_item_type' => IncomeDebitNoteTransaction::ITEM_INVOICE,
        'grand_total' => 620.00,
    ]);
});

/*
|----------------------------------------------------------------------------------------------------------------------------------------------------
| Update Income Debit Note Transaction Account Invoice Backend Validation with update Test Case
|----------------------------------------------------------------------------------------------------------------------------------------------------
*/

test('validation with update decimal rpu amount when decimal place is 1 for update income debit note transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $debitNoteInput = IncomeDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'dn_item_type' => IncomeDebitNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $debitNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $debitNoteData = StoreIncomeDebitNoteAction::run($debitNoteInput);
    $debitNoteInput['ledgers'][0]['decimal'] = 1;
    $debitNoteInput['ledgers'][0]['rpu'] = 100.********;
    $debitNoteInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $debitNoteInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $debitNoteInput['ledgers'][0]['gst_id'] = $gst->id;
    $debitNoteInput['ledgers'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('income-debit-note-update', $debitNoteData['transaction_id']), $debitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Debit Note Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_debit_note_transactions', [
        'id' => $debitNoteData['transaction_id'],
        'company_id' => 1,
        'debit_note_number' => $debitNoteInput['debit_note_number'],
        'dn_item_type' => IncomeDebitNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $debitNoteTransaction = IncomeDebitNoteTransaction::with('incomeDebitNoteLedgers')->whereId($debitNoteData['transaction_id'])->first();
    $this->assertEquals(78.2, $debitNoteTransaction->incomeDebitNoteLedgers->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 2 for update income debit note transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $debitNoteInput = IncomeDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'dn_item_type' => IncomeDebitNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $debitNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $debitNoteData = StoreIncomeDebitNoteAction::run($debitNoteInput);
    $debitNoteInput['ledgers'][0]['decimal'] = 2;
    $debitNoteInput['ledgers'][0]['rpu'] = 100.********;
    $debitNoteInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $debitNoteInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $debitNoteInput['ledgers'][0]['gst_id'] = $gst->id;
    $debitNoteInput['ledgers'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('income-debit-note-update', $debitNoteData['transaction_id']), $debitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Debit Note Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_debit_note_transactions', [
        'id' => $debitNoteData['transaction_id'],
        'company_id' => 1,
        'debit_note_number' => $debitNoteInput['debit_note_number'],
        'dn_item_type' => IncomeDebitNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $debitNoteTransaction = IncomeDebitNoteTransaction::with('incomeDebitNoteLedgers')->whereId($debitNoteData['transaction_id'])->first();
    $this->assertEquals(78.22, $debitNoteTransaction->incomeDebitNoteLedgers->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 3 for update income debit note transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $debitNoteInput = IncomeDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'dn_item_type' => IncomeDebitNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $debitNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $debitNoteData = StoreIncomeDebitNoteAction::run($debitNoteInput);
    $debitNoteInput['ledgers'][0]['decimal'] = 3;
    $debitNoteInput['ledgers'][0]['rpu'] = 100.********;
    $debitNoteInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $debitNoteInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $debitNoteInput['ledgers'][0]['gst_id'] = $gst->id;
    $debitNoteInput['ledgers'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('income-debit-note-update', $debitNoteData['transaction_id']), $debitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Debit Note Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_debit_note_transactions', [
        'id' => $debitNoteData['transaction_id'],
        'company_id' => 1,
        'debit_note_number' => $debitNoteInput['debit_note_number'],
        'dn_item_type' => IncomeDebitNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $debitNoteTransaction = IncomeDebitNoteTransaction::with('incomeDebitNoteLedgers')->whereId($debitNoteData['transaction_id'])->first();
    $this->assertEquals(78.220, $debitNoteTransaction->incomeDebitNoteLedgers->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 4 for update income debit note transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $debitNoteInput = IncomeDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'dn_item_type' => IncomeDebitNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $debitNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $debitNoteData = StoreIncomeDebitNoteAction::run($debitNoteInput);
    $debitNoteInput['ledgers'][0]['decimal'] = 4;
    $debitNoteInput['ledgers'][0]['rpu'] = 100.********;
    $debitNoteInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $debitNoteInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $debitNoteInput['ledgers'][0]['gst_id'] = $gst->id;
    $debitNoteInput['ledgers'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('income-debit-note-update', $debitNoteData['transaction_id']), $debitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Debit Note Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_debit_note_transactions', [
        'id' => $debitNoteData['transaction_id'],
        'company_id' => 1,
        'debit_note_number' => $debitNoteInput['debit_note_number'],
        'dn_item_type' => IncomeDebitNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $debitNoteTransaction = IncomeDebitNoteTransaction::with('incomeDebitNoteLedgers')->whereId($debitNoteData['transaction_id'])->first();
    $this->assertEquals(78.2197, $debitNoteTransaction->incomeDebitNoteLedgers->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 5 for update income debit note transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $debitNoteInput = IncomeDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'dn_item_type' => IncomeDebitNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $debitNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $debitNoteData = StoreIncomeDebitNoteAction::run($debitNoteInput);
    $debitNoteInput['ledgers'][0]['decimal'] = 5;
    $debitNoteInput['ledgers'][0]['rpu'] = 100.********;
    $debitNoteInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $debitNoteInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $debitNoteInput['ledgers'][0]['gst_id'] = $gst->id;
    $debitNoteInput['ledgers'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('income-debit-note-update', $debitNoteData['transaction_id']), $debitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Debit Note Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_debit_note_transactions', [
        'id' => $debitNoteData['transaction_id'],
        'company_id' => 1,
        'debit_note_number' => $debitNoteInput['debit_note_number'],
        'dn_item_type' => IncomeDebitNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $debitNoteTransaction = IncomeDebitNoteTransaction::with('incomeDebitNoteLedgers')->whereId($debitNoteData['transaction_id'])->first();
    $this->assertEquals(78.21970, $debitNoteTransaction->incomeDebitNoteLedgers->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 1 for update income debit note transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $debitNoteInput = IncomeDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'dn_item_type' => IncomeDebitNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $debitNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $debitNoteData = StoreIncomeDebitNoteAction::run($debitNoteInput);
    $debitNoteInput['ledgers'][0]['decimal'] = 1;
    $debitNoteInput['ledgers'][0]['rpu'] = 100.********;
    $debitNoteInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $debitNoteInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $debitNoteInput['ledgers'][0]['gst_id'] = $gst->id;
    $debitNoteInput['ledgers'][0]['gst_tax_percentage'] = 2;
    $debitNoteInput['ledgers'][0]['with_tax'] = false;

    //act
    $response = $this->postJson(route('income-debit-note-update', $debitNoteData['transaction_id']), $debitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Debit Note Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_debit_note_transactions', [
        'id' => $debitNoteData['transaction_id'],
        'company_id' => 1,
        'debit_note_number' => $debitNoteInput['debit_note_number'],
        'dn_item_type' => IncomeDebitNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $debitNoteTransaction = IncomeDebitNoteTransaction::with('incomeDebitNoteLedgers')->whereId($debitNoteData['transaction_id'])->first();
    $this->assertEquals(128.2, $debitNoteTransaction->incomeDebitNoteLedgers->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 2 for update income debit note transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $debitNoteInput = IncomeDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'dn_item_type' => IncomeDebitNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $debitNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $debitNoteData = StoreIncomeDebitNoteAction::run($debitNoteInput);
    $debitNoteInput['ledgers'][0]['decimal'] = 2;
    $debitNoteInput['ledgers'][0]['rpu'] = 100.********;
    $debitNoteInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $debitNoteInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $debitNoteInput['ledgers'][0]['gst_id'] = $gst->id;
    $debitNoteInput['ledgers'][0]['gst_tax_percentage'] = 2;
    $debitNoteInput['ledgers'][0]['with_tax'] = false;

    //act
    $response = $this->postJson(route('income-debit-note-update', $debitNoteData['transaction_id']), $debitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Debit Note Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_debit_note_transactions', [
        'id' => $debitNoteData['transaction_id'],
        'company_id' => 1,
        'debit_note_number' => $debitNoteInput['debit_note_number'],
        'dn_item_type' => IncomeDebitNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $debitNoteTransaction = IncomeDebitNoteTransaction::with('incomeDebitNoteLedgers')->whereId($debitNoteData['transaction_id'])->first();
    $this->assertEquals(128.16, $debitNoteTransaction->incomeDebitNoteLedgers->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 3 for update income debit note transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $debitNoteInput = IncomeDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'dn_item_type' => IncomeDebitNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $debitNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $debitNoteData = StoreIncomeDebitNoteAction::run($debitNoteInput);
    $debitNoteInput['ledgers'][0]['decimal'] = 3;
    $debitNoteInput['ledgers'][0]['rpu'] = 100.********;
    $debitNoteInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $debitNoteInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $debitNoteInput['ledgers'][0]['gst_id'] = $gst->id;
    $debitNoteInput['ledgers'][0]['gst_tax_percentage'] = 2;
    $debitNoteInput['ledgers'][0]['with_tax'] = false;

    //act
    $response = $this->postJson(route('income-debit-note-update', $debitNoteData['transaction_id']), $debitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Debit Note Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_debit_note_transactions', [
        'id' => $debitNoteData['transaction_id'],
        'company_id' => 1,
        'debit_note_number' => $debitNoteInput['debit_note_number'],
        'dn_item_type' => IncomeDebitNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $debitNoteTransaction = IncomeDebitNoteTransaction::with('incomeDebitNoteLedgers')->whereId($debitNoteData['transaction_id'])->first();
    $this->assertEquals(128.155, $debitNoteTransaction->incomeDebitNoteLedgers->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 4 for update income debit note transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $debitNoteInput = IncomeDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'dn_item_type' => IncomeDebitNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $debitNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $debitNoteData = StoreIncomeDebitNoteAction::run($debitNoteInput);
    $debitNoteInput['ledgers'][0]['decimal'] = 4;
    $debitNoteInput['ledgers'][0]['rpu'] = 100.********;
    $debitNoteInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $debitNoteInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $debitNoteInput['ledgers'][0]['gst_id'] = $gst->id;
    $debitNoteInput['ledgers'][0]['gst_tax_percentage'] = 2;
    $debitNoteInput['ledgers'][0]['with_tax'] = false;

    //act
    $response = $this->postJson(route('income-debit-note-update', $debitNoteData['transaction_id']), $debitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Debit Note Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_debit_note_transactions', [
        'id' => $debitNoteData['transaction_id'],
        'company_id' => 1,
        'debit_note_number' => $debitNoteInput['debit_note_number'],
        'dn_item_type' => IncomeDebitNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $debitNoteTransaction = IncomeDebitNoteTransaction::with('incomeDebitNoteLedgers')->whereId($debitNoteData['transaction_id'])->first();
    $this->assertEquals(128.1552, $debitNoteTransaction->incomeDebitNoteLedgers->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 5 for update income debit note transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $debitNoteInput = IncomeDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'dn_item_type' => IncomeDebitNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $debitNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $debitNoteData = StoreIncomeDebitNoteAction::run($debitNoteInput);
    $debitNoteInput['ledgers'][0]['decimal'] = 5;
    $debitNoteInput['ledgers'][0]['rpu'] = 100.********;
    $debitNoteInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $debitNoteInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $debitNoteInput['ledgers'][0]['gst_id'] = $gst->id;
    $debitNoteInput['ledgers'][0]['gst_tax_percentage'] = 2;
    $debitNoteInput['ledgers'][0]['with_tax'] = false;

    //act
    $response = $this->postJson(route('income-debit-note-update', $debitNoteData['transaction_id']), $debitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Debit Note Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_debit_note_transactions', [
        'id' => $debitNoteData['transaction_id'],
        'company_id' => 1,
        'debit_note_number' => $debitNoteInput['debit_note_number'],
        'dn_item_type' => IncomeDebitNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $debitNoteTransaction = IncomeDebitNoteTransaction::with('incomeDebitNoteLedgers')->whereId($debitNoteData['transaction_id'])->first();
    $this->assertEquals(128.15515, $debitNoteTransaction->incomeDebitNoteLedgers->first()->rpu_with_gst);
});

test('validation with update negative and wrong rpu without or with gst for update income debit note transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $debitNoteInput = IncomeDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'dn_item_type' => IncomeDebitNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $debitNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $debitNoteData = StoreIncomeDebitNoteAction::run($debitNoteInput);
    $debitNoteInput['ledgers'][0]['rpu_without_gst'] = -10000;
    $debitNoteInput['ledgers'][0]['rpu_with_gst'] = -10000;
    $debitNoteInput['ledgers'][0]['gst_id'] = $gst->id;
    $debitNoteInput['ledgers'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('income-debit-note-update', $debitNoteData['transaction_id']), $debitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Debit Note Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_debit_note_transactions', [
        'id' => $debitNoteData['transaction_id'],
        'company_id' => 1,
        'debit_note_number' => $debitNoteInput['debit_note_number'],
        'dn_item_type' => IncomeDebitNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $debitNoteTransaction = IncomeDebitNoteTransaction::with('incomeDebitNoteLedgers')->whereId($debitNoteData['transaction_id'])->first();
    $this->assertNotEquals(100, $debitNoteTransaction->incomeDebitNoteLedgers->first()->rpu_without_gst);
    $this->assertEquals(100.0, $debitNoteTransaction->incomeDebitNoteLedgers->first()->rpu_with_gst);
});

test('validation with update negative and wrong gst tax percentage for update income debit note transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $debitNoteInput = IncomeDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'dn_item_type' => IncomeDebitNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $debitNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $debitNoteData = StoreIncomeDebitNoteAction::run($debitNoteInput);
    $debitNoteInput['ledgers'][0]['total'] = -10000;
    $debitNoteInput['ledgers'][0]['gst_id'] = $gst->id;
    $debitNoteInput['ledgers'][0]['gst_tax_percentage'] = $gst->tax_rate;

    //act
    $response = $this->postJson(route('income-debit-note-update', $debitNoteData['transaction_id']), $debitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Debit Note Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_debit_note_transactions', [
        'id' => $debitNoteData['transaction_id'],
        'company_id' => 1,
        'debit_note_number' => $debitNoteInput['debit_note_number'],
        'dn_item_type' => IncomeDebitNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $debitNoteTransaction = IncomeDebitNoteTransaction::with('incomeDebitNoteLedgers')->whereId($debitNoteData['transaction_id'])->first();
    $this->assertEquals(28, intval($debitNoteTransaction->incomeDebitNoteLedgers->first()->gst_tax_percentage));
});

test('validation with update negative and wrong total discount for update income debit note transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(5.00)->first();

    $debitNoteInput = IncomeDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'dn_item_type' => IncomeDebitNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $debitNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $debitNoteData = StoreIncomeDebitNoteAction::run($debitNoteInput);
    foreach ($debitNoteInput['ledgers'] as $key => $ledger) {
        $debitNoteInput['ledgers'][$key]['gst_id'] = $gst->id;
        $debitNoteInput['ledgers'][$key]['discount_type'] = 2;
        $debitNoteInput['ledgers'][$key]['discount_value'] = 10;
        $debitNoteInput['ledgers'][$key]['discount_type_2'] = 2;
        $debitNoteInput['ledgers'][$key]['discount_value_2'] = 10;
        $debitNoteInput['ledgers'][$key]['total_discount_amount'] = 10;
    }

    //act
    $response = $this->postJson(route('income-debit-note-update', $debitNoteData['transaction_id']), $debitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Debit Note Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_debit_note_transactions', [
        'id' => $debitNoteData['transaction_id'],
        'company_id' => 1,
        'debit_note_number' => $debitNoteInput['debit_note_number'],
        'dn_item_type' => IncomeDebitNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $debitNoteTransaction = IncomeDebitNoteTransaction::with('incomeDebitNoteLedgers')->whereId($debitNoteData['transaction_id'])->first();
    foreach ($debitNoteTransaction->incomeDebitNoteLedgers as $ledger) {
        $this->assertEquals(18.1, $ledger->total_discount_amount);
    }
});

test('validation with update negative and wrong sub total for update income debit note transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $debitNoteInput = IncomeDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'dn_item_type' => IncomeDebitNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $debitNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $debitNoteData = StoreIncomeDebitNoteAction::run($debitNoteInput);
    $debitNoteInput['ledgers'][0]['total'] = -10000;
    $debitNoteInput['ledgers'][0]['gst_id'] = $gst->id;
    $debitNoteInput['ledgers'][0]['gst_tax_percentage'] = $gst->tax_rate;

    //act
    $response = $this->postJson(route('income-debit-note-update', $debitNoteData['transaction_id']), $debitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Debit Note Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_debit_note_transactions', [
        'id' => $debitNoteData['transaction_id'],
        'company_id' => 1,
        'debit_note_number' => $debitNoteInput['debit_note_number'],
        'dn_item_type' => IncomeDebitNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $debitNoteTransaction = IncomeDebitNoteTransaction::with('incomeDebitNoteLedgers')->whereId($debitNoteData['transaction_id'])->first();
    $this->assertEquals(78.13, $debitNoteTransaction->incomeDebitNoteLedgers->first()->total);
});

test('validation with update negative and wrong sub total for update income debit note transaction account invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $debitNoteInput = IncomeDebitNoteTransactionFactory::new()->make(['dn_item_type' => IncomeDebitNoteTransaction::ACCOUNTING_INVOICE])->toArray();
    $debitNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $debitNoteData = StoreIncomeDebitNoteAction::run($debitNoteInput);
    $debitNoteInput['ledgers'][0]['total'] = -10000;

    //act
    $response = $this->postJson(route('income-debit-note-update', $debitNoteData['transaction_id']), $debitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Debit Note Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_debit_note_transactions', [
        'id' => $debitNoteData['transaction_id'],
        'company_id' => 1,
        'debit_note_number' => $debitNoteInput['debit_note_number'],
        'dn_item_type' => IncomeDebitNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $debitNoteTransaction = IncomeDebitNoteTransaction::with('incomeDebitNoteLedgers')->whereId($debitNoteData['transaction_id'])->first();
    $this->assertEquals(100, intval($debitNoteTransaction->incomeDebitNoteLedgers->first()->total));
});

test('validation with update negative and wrong total for update income debit note transaction account invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $debitNoteInput = IncomeDebitNoteTransactionFactory::new()->make([
        'dn_item_type' => IncomeDebitNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $debitNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $debitNoteData = StoreIncomeDebitNoteAction::run($debitNoteInput);
    $debitNoteInput['total'] = 620;

    //act
    $response = $this->postJson(route('income-debit-note-update', $debitNoteData['transaction_id']), $debitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Debit Note Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_debit_note_transactions', [
        'id' => $debitNoteData['transaction_id'],
        'company_id' => 1,
        'debit_note_number' => $debitNoteInput['debit_note_number'],
        'dn_item_type' => IncomeDebitNoteTransaction::ACCOUNTING_INVOICE,
        'total' => 620.00,
    ]);
});

test('validation with update negative and wrong gross value for update income debit note transaction account invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $debitNoteInput = IncomeDebitNoteTransactionFactory::new()->make([
        'dn_item_type' => IncomeDebitNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $debitNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $debitNoteData = StoreIncomeDebitNoteAction::run($debitNoteInput);
    $debitNoteInput['gross_value'] = -100;

    //act
    $response = $this->postJson(route('income-debit-note-update', $debitNoteData['transaction_id']), $debitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Debit Note Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_debit_note_transactions', [
        'id' => $debitNoteData['transaction_id'],
        'company_id' => 1,
        'debit_note_number' => $debitNoteInput['debit_note_number'],
        'dn_item_type' => IncomeDebitNoteTransaction::ACCOUNTING_INVOICE,
        'gross_value' => 600.00,
    ]);
});

test('validation with update negative and wrong additional charges gst tax percentage for update income debit note transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $debitNoteInput = IncomeDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'dn_item_type' => IncomeDebitNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $debitNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $debitNoteData = StoreIncomeDebitNoteAction::run($debitNoteInput);
    $debitNoteInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    $debitNoteInput['additional_charges'][0]['ac_total'] = -2000;

    //act
    $response = $this->postJson(route('income-debit-note-update', $debitNoteData['transaction_id']), $debitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Debit Note Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_debit_note_transactions', [
        'id' => $debitNoteData['transaction_id'],
        'company_id' => 1,
        'debit_note_number' => $debitNoteInput['debit_note_number'],
        'dn_item_type' => IncomeDebitNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $debitNoteTransaction = IncomeDebitNoteTransaction::with('additionalCharges')->whereId($debitNoteData['transaction_id'])->first();
    $this->assertNotEquals(10.00, $debitNoteTransaction->additionalCharges->first()->total);
});

test('validation with update negative and wrong additional charges total for update income debit note transaction account invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $debitNoteInput = IncomeDebitNoteTransactionFactory::new()->make(['dn_item_type' => IncomeDebitNoteTransaction::ACCOUNTING_INVOICE])->toArray();
    $debitNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $debitNoteData = StoreIncomeDebitNoteAction::run($debitNoteInput);
    $debitNoteInput['additional_charges'][0]['ac_total'] = -200;

    //act
    $response = $this->postJson(route('income-debit-note-update', $debitNoteData['transaction_id']), $debitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Debit Note Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_debit_note_transactions', [
        'id' => $debitNoteData['transaction_id'],
        'company_id' => 1,
        'debit_note_number' => $debitNoteInput['debit_note_number'],
        'dn_item_type' => IncomeDebitNoteTransaction::ACCOUNTING_INVOICE,
        'gross_value' => 600.00,
    ]);
    $debitNoteTransaction = IncomeDebitNoteTransaction::with('additionalCharges')->whereId($debitNoteData['transaction_id'])->first();
    $this->assertEquals(10, intval($debitNoteTransaction->additionalCharges->first()->total));
});

test('validation with update negative and wrong cgst and sgst when sale intra state taxable classification for update income debit note transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $debitNoteInput = IncomeDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'main_classification_nature_type' => 'Sale Intra State Taxable',
        'dn_item_type' => IncomeDebitNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $debitNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $debitNoteData = StoreIncomeDebitNoteAction::run($debitNoteInput);
    $debitNoteInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    foreach ($debitNoteInput['ledgers'] as $key => $ledger) {
        $debitNoteInput['ledgers'][$key]['gst_id'] = $gst->id;
    }

    //act
    $response = $this->postJson(route('income-debit-note-update', $debitNoteData['transaction_id']), $debitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Debit Note Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_debit_note_transactions', [
        'id' => $debitNoteData['transaction_id'],
        'company_id' => 1,
        'debit_note_number' => $debitNoteInput['debit_note_number'],
        'dn_item_type' => IncomeDebitNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $debitNoteTransaction = IncomeDebitNoteTransaction::whereId($debitNoteData['transaction_id'])->first();
    $this->assertEquals(67.04, $debitNoteTransaction->cgst);
    $this->assertEquals(67.04, $debitNoteTransaction->sgst);
});

test('validation with update negative and wrong igst when sale inter state taxable classification for update income debit note transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $debitNoteInput = IncomeDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'main_classification_nature_type' => 'Sale Inter State Taxable',
        'dn_item_type' => IncomeDebitNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $debitNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $debitNoteData = StoreIncomeDebitNoteAction::run($debitNoteInput);
    $debitNoteInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    foreach ($debitNoteInput['ledgers'] as $key => $ledger) {
        $debitNoteInput['ledgers'][$key]['gst_id'] = $gst->id;
    }

    //act
    $response = $this->postJson(route('income-debit-note-update', $debitNoteData['transaction_id']), $debitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Debit Note Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_debit_note_transactions', [
        'id' => $debitNoteData['transaction_id'],
        'company_id' => 1,
        'debit_note_number' => $debitNoteInput['debit_note_number'],
        'dn_item_type' => IncomeDebitNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $debitNoteTransaction = IncomeDebitNoteTransaction::whereId($debitNoteData['transaction_id'])->first();
    $this->assertEquals(134.08, $debitNoteTransaction->igst);
});

test('validation with update negative and wrong taxable value for update income debit note transaction account invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $debitNoteInput = IncomeDebitNoteTransactionFactory::new()->make([
        'dn_item_type' => IncomeDebitNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $debitNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $debitNoteData = StoreIncomeDebitNoteAction::run($debitNoteInput);
    $debitNoteInput['taxable_value'] = -100;

    //act
    $response = $this->postJson(route('income-debit-note-update', $debitNoteData['transaction_id']), $debitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Debit Note Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_debit_note_transactions', [
        'id' => $debitNoteData['transaction_id'],
        'company_id' => 1,
        'debit_note_number' => $debitNoteInput['debit_note_number'],
        'dn_item_type' => IncomeDebitNoteTransaction::ACCOUNTING_INVOICE,
        'taxable_value' => 610.00,
    ]);
});

test('validation with update negative and wrong addless total for update income debit note transaction account invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $debitNoteInput = IncomeDebitNoteTransactionFactory::new()->make([
        'dn_item_type' => IncomeDebitNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $debitNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $debitNoteData = StoreIncomeDebitNoteAction::run($debitNoteInput);
    $debitNoteInput['add_less'][0]['al_total'] = -100;

    //act
    $response = $this->postJson(route('income-debit-note-update', $debitNoteData['transaction_id']), $debitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Debit Note Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_debit_note_transactions', [
        'id' => $debitNoteData['transaction_id'],
        'company_id' => 1,
        'debit_note_number' => $debitNoteInput['debit_note_number'],
        'dn_item_type' => IncomeDebitNoteTransaction::ACCOUNTING_INVOICE,
        'gross_value' => 600.00,
    ]);
    $debitNoteTransaction = IncomeDebitNoteTransaction::with('addLess')->whereId($debitNoteData['transaction_id'])->first();
    $this->assertEquals(10, intval($debitNoteTransaction->addLess->first()->total));
});

test('validation with update negative and wrong grand total for update income debit note transaction account invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $debitNoteInput = IncomeDebitNoteTransactionFactory::new()->make([
        'dn_item_type' => IncomeDebitNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $debitNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $debitNoteData = StoreIncomeDebitNoteAction::run($debitNoteInput);
    $debitNoteInput['grand_total'] = -100;

    //act
    $response = $this->postJson(route('income-debit-note-update', $debitNoteData['transaction_id']), $debitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Debit Note Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_debit_note_transactions', [
        'id' => $debitNoteData['transaction_id'],
        'company_id' => 1,
        'debit_note_number' => $debitNoteInput['debit_note_number'],
        'dn_item_type' => IncomeDebitNoteTransaction::ACCOUNTING_INVOICE,
        'grand_total' => 620.00,
    ]);
});

test('validation with update negative and wrong round off amount and method for update income debit note transaction', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);
    $debitNoteInput = IncomeDebitNoteTransactionFactory::new()->make()->toArray();
    $debitNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $debitNoteInput['additional_charges'][0]['ac_value'] = 10.54;

    $roundOffMethods = [
        ['method' => 1, 'correct_amount' => 0],
        ['method' => 2, 'correct_amount' => -0.54],
        ['method' => 3, 'correct_amount' => 0.46],
        ['method' => 4, 'correct_amount' => 0.46],
    ];
    foreach ($roundOffMethods as $roundOffMethod) {
        $debitNoteInput['debit_note_number'] = 'DN-'.$roundOffMethod['method'];
        $debitNoteData = StoreIncomeDebitNoteAction::run($debitNoteInput);
        $debitNoteInput['round_off_method'] = 0;
        IncomeDebitNoteTransactionMaster::whereCompanyId(1)->update(['round_off_method' => $roundOffMethod['method']]);
        $debitNoteInput['rounding_amount'] = -10.50;

        //act
        $response = $this->postJson(route('income-debit-note-update', $debitNoteData['transaction_id']), $debitNoteInput);

        //assert
        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Income Debit Note Transaction Updated Successfully',
            ]);
        $this->assertDatabaseHas('income_debit_note_transactions', [
            'id' => $debitNoteData['transaction_id'],
            'company_id' => 1,
            'debit_note_number' => $debitNoteInput['debit_note_number'],
            'round_off_method' => $roundOffMethod,
            'rounding_amount' => $roundOffMethod['correct_amount'],
        ]);
    }
});
