<?php

use App\Actions\v1\Transactions\DeliveryChallan\StoreDeliveryChallanTransactionAction;
use App\Models\DeliveryChallanTransaction;
use App\Models\DeliveryChallanTransactionMaster;
use App\Models\GstTax;
use App\Models\Master\ItemMasterGoods;
use Database\Factories\Api\DeliveryChallanTransactionFactory;

/*
|----------------------------------------------------------------------------------------------------------------------------------------------------
| Delivery Challan Transaction With Amount Invoice Backend Validation Test Case
|----------------------------------------------------------------------------------------------------------------------------------------------------
*/

test('validate delivery challan transaction with amount invoice will return gst na incorrect', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false, 'app.is_validation_testing' => false]);

    $deliveryChallanInput = DeliveryChallanTransactionFactory::new()->make([
        'invoice_type' => DeliveryChallanTransaction::WITH_AMOUNT,
    ])->toArray();

    //act
    $response = $this->postJson(route('delivery-challan-transaction'), $deliveryChallanInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Is GST NA is not correct.',
        ]);
});

test('validate delivery challan transaction with amount invoice will return cgst, sgst, and igst calculated is not correct', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false, 'app.is_validation_testing' => false]);

    $deliveryChallanInput = DeliveryChallanTransactionFactory::new()->make([
        'invoice_type' => DeliveryChallanTransaction::WITH_AMOUNT,
        'is_gst_na' => true,
        'is_cgst_sgst_igst_calculated' => true,
    ])->toArray();

    //act
    $response = $this->postJson(route('delivery-challan-transaction'), $deliveryChallanInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Is CGST, SGST, and IGST Calculated is not correct.',
        ]);
});

test('validate delivery challan transaction with amount invoice will return subtotal is not correct when pass wrong total', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $deliveryChallanInput = DeliveryChallanTransactionFactory::new()->make([
        'invoice_type' => DeliveryChallanTransaction::WITH_AMOUNT,
        'is_gst_na' => true,
    ])->toArray();
    $deliveryChallanInput['items'][0]['total'] = 10;

    //act
    $response = $this->postJson(route('delivery-challan-transaction'), $deliveryChallanInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Subtotal is not correct.',
        ]);
});

test('validate delivery challan transaction with amount invoice will return subtotal is not correct when pass wrong total with gst na, exempt, zero in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $deliveryChallanInput = DeliveryChallanTransactionFactory::new()->make([
        'invoice_type' => DeliveryChallanTransaction::WITH_AMOUNT,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $deliveryChallanInput['items'][0]['total'] = 10;

    //act
    $response = $this->postJson(route('delivery-challan-transaction'), $deliveryChallanInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Subtotal is not correct.',
        ]);
});

test('validate delivery challan transaction with amount invoice will return subtotal is not correct when pass wrong total with gst percentage', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $gst = GstTax::whereTaxRate(28.00)->first();
    $deliveryChallanInput = DeliveryChallanTransactionFactory::new()->make([
        'invoice_type' => DeliveryChallanTransaction::WITH_AMOUNT,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $deliveryChallanInput['items'][0]['gst_id'] = $gst->id;
    $deliveryChallanInput['items'][0]['gst_tax_percentage'] = $gst->tax_rate;

    //act
    $response = $this->postJson(route('delivery-challan-transaction'), $deliveryChallanInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Subtotal is not correct.',
        ]);
});

test('validate delivery challan transaction with amount invoice will return gross value is not correct', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $deliveryChallanInput = DeliveryChallanTransactionFactory::new()->make([
        'invoice_type' => DeliveryChallanTransaction::WITH_AMOUNT,
        'is_gst_na' => true,
        'gross_value' => 10,
    ])->toArray();

    //act
    $response = $this->postJson(route('delivery-challan-transaction'), $deliveryChallanInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Gross value is not correct.',
        ]);
});

test('validate delivery challan transaction with amount invoice will return addition charge total is not correct', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $deliveryChallanInput = DeliveryChallanTransactionFactory::new()->make([
        'invoice_type' => DeliveryChallanTransaction::WITH_AMOUNT,
        'is_gst_na' => true,
    ])->toArray();
    $deliveryChallanInput['additional_charges'][0]['ac_value'] = 20;

    //act
    $response = $this->postJson(route('delivery-challan-transaction'), $deliveryChallanInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Additional charge total is not correct.',
        ]);
});

test('validate delivery challan transaction with amount invoice will return Taxable value is not correct when pass additional gst percentage in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $gst = GstTax::whereTaxRate(28.00)->first();
    $deliveryChallanInput = DeliveryChallanTransactionFactory::new()->make([
        'invoice_type' => DeliveryChallanTransaction::WITH_AMOUNT,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $deliveryChallanInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    foreach ($deliveryChallanInput['items'] as $key => $item) {
        $deliveryChallanInput['items'][$key]['gst_id'] = 2;
    }

    //act
    $response = $this->postJson(route('delivery-challan-transaction'), $deliveryChallanInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Taxable value is not correct.',
        ]);
});

test('validate delivery challan transaction with amount invoice will return taxable value is not correct', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $deliveryChallanInput = DeliveryChallanTransactionFactory::new()->make([
        'invoice_type' => DeliveryChallanTransaction::WITH_AMOUNT,
        'is_gst_na' => true,
        'taxable_value' => 10,
    ])->toArray();

    //act
    $response = $this->postJson(route('delivery-challan-transaction'), $deliveryChallanInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Taxable value is not correct.',
        ]);
});

test('validate delivery challan transaction with amount invoice will return cgst is not correct in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $deliveryChallanInput = DeliveryChallanTransactionFactory::new()->make([
        'invoice_type' => DeliveryChallanTransaction::WITH_AMOUNT,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'gross_value' => 468.78,
        'taxable_value' => 478.78,
        'main_classification_nature_type' => 'Sale Intra State Taxable',
    ])->toArray();
    foreach ($deliveryChallanInput['items'] as $key => $item) {
        $deliveryChallanInput['items'][$key]['gst_id'] = $gst->id;
        $deliveryChallanInput['items'][$key]['total'] = 78.13;
    }

    //act
    $response = $this->postJson(route('delivery-challan-transaction'), $deliveryChallanInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'CGST is not correct.',
        ]);
});

test('validate delivery challan transaction with amount invoice will return sgst is not correct in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $deliveryChallanInput = DeliveryChallanTransactionFactory::new()->make([
        'invoice_type' => DeliveryChallanTransaction::WITH_AMOUNT,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'gross_value' => 468.78,
        'taxable_value' => 478.78,
        'main_classification_nature_type' => 'Sale Intra State Taxable',
        'cgst' => 65.64,
    ])->toArray();
    foreach ($deliveryChallanInput['items'] as $key => $item) {
        $deliveryChallanInput['items'][$key]['gst_id'] = $gst->id;
        $deliveryChallanInput['items'][$key]['total'] = 78.13;
    }

    //act
    $response = $this->postJson(route('delivery-challan-transaction'), $deliveryChallanInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'SGST is not correct.',
        ]);
});

test('validate delivery challan transaction with amount invoice will return igst is not correct in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $deliveryChallanInput = DeliveryChallanTransactionFactory::new()->make([
        'invoice_type' => DeliveryChallanTransaction::WITH_AMOUNT,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'main_classification_nature_type' => 'Sale Inter State Taxable',
        'gross_value' => 468.78,
        'taxable_value' => 478.78,
    ])->toArray();
    foreach ($deliveryChallanInput['items'] as $key => $item) {
        $deliveryChallanInput['items'][$key]['gst_id'] = $gst->id;
        $deliveryChallanInput['items'][$key]['total'] = 78.13;
    }

    //act
    $response = $this->postJson(route('delivery-challan-transaction'), $deliveryChallanInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'IGST is not correct.',
        ]);
});

test('validate delivery challan transaction with amount invoice will return classification nature type is empty', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(0.00)->first();

    $deliveryChallanInput = DeliveryChallanTransactionFactory::new()->make([
        'invoice_type' => DeliveryChallanTransaction::WITH_AMOUNT,
        'is_gst_enabled' => true,
        'main_classification_nature_type' => null,
        'taxable_value' => 610,
    ])->toArray();
    foreach ($deliveryChallanInput['items'] as $key => $item) {
        $deliveryChallanInput['items'][$key]['gst_id'] = $gst->id;
    }

    //act
    $response = $this->postJson(route('delivery-challan-transaction'), $deliveryChallanInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'The main classification nature type field is required.',
        ]);
});

test('validate delivery challan transaction with amount invoice will return add less total is not correct', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $deliveryChallanInput = DeliveryChallanTransactionFactory::new()->make([
        'invoice_type' => DeliveryChallanTransaction::WITH_AMOUNT,
        'is_gst_na' => true,
        'taxable_value' => 610,
    ])->toArray();
    $deliveryChallanInput['add_less'][0]['al_total'] = 100;

    //act
    $response = $this->postJson(route('delivery-challan-transaction'), $deliveryChallanInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Add Less total is not correct.',
        ]);
});

test('validate delivery challan transaction with amount invoice will return round off amount is not correct', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $deliveryChallanInput = DeliveryChallanTransactionFactory::new()->make([
        'invoice_type' => DeliveryChallanTransaction::WITH_AMOUNT,
        'is_gst_na' => true,
        'is_round_off_not_changed' => 1,
        'taxable_value' => 610,
        'rounding_amount' => 10.50,
    ])->toArray();

    //act
    $response = $this->postJson(route('delivery-challan-transaction'), $deliveryChallanInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Round off amount is not correct.',
        ]);
});

test('validate delivery challan transaction with amount invoice will return grand total is not correct', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $deliveryChallanInput = DeliveryChallanTransactionFactory::new()->make([
        'invoice_type' => DeliveryChallanTransaction::WITH_AMOUNT,
        'is_gst_na' => true,
        'taxable_value' => 610,
        'grand_total' => 100,
    ])->toArray();

    //act
    $response = $this->postJson(route('delivery-challan-transaction'), $deliveryChallanInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Grand total is not correct.',
        ]);
});

/*
|----------------------------------------------------------------------------------------------------------------------------------------------------
| Store Delivery Challan Transaction with Amount Invoice Backend Validation with update Test Case
|----------------------------------------------------------------------------------------------------------------------------------------------------
*/

test('validation with update decimal rpu amount when decimal place is 1 for delivery challan transaction with amount invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 1]);
    $deliveryChallanInput = DeliveryChallanTransactionFactory::new()->make([
        'invoice_type' => DeliveryChallanTransaction::WITH_AMOUNT,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $deliveryChallanInput['items'][0]['rpu'] = 100.12121112;
    $deliveryChallanInput['items'][0]['rpu_without_gst'] = 100.12121112;
    $deliveryChallanInput['items'][0]['rpu_with_gst'] = 100.12121112;
    $deliveryChallanInput['items'][0]['gst_id'] = $gst->id;
    $deliveryChallanInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('delivery-challan-transaction'), $deliveryChallanInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Delivery Challan Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('delivery_challan_transactions', [
        'id' => $response->json('data.delivery_challan_transaction.id'),
        'company_id' => 1,
        'challan_number' => $deliveryChallanInput['challan_number'],
        'invoice_type' => DeliveryChallanTransaction::WITH_AMOUNT,
    ]);
    $deliveryChallanTransaction = DeliveryChallanTransaction::with('transactionItems')->whereId($response->json('data.delivery_challan_transaction.id'))->first();
    $this->assertEquals(78.2, $deliveryChallanTransaction->transactionItems->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 2 for delivery challan transaction with amount invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 2]);
    $deliveryChallanInput = DeliveryChallanTransactionFactory::new()->make([
        'invoice_type' => DeliveryChallanTransaction::WITH_AMOUNT,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $deliveryChallanInput['items'][0]['rpu'] = 100.12121112;
    $deliveryChallanInput['items'][0]['rpu_without_gst'] = 100.12121112;
    $deliveryChallanInput['items'][0]['rpu_with_gst'] = 100.12121112;
    $deliveryChallanInput['items'][0]['gst_id'] = $gst->id;
    $deliveryChallanInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('delivery-challan-transaction'), $deliveryChallanInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Delivery Challan Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('delivery_challan_transactions', [
        'id' => $response->json('data.delivery_challan_transaction.id'),
        'company_id' => 1,
        'challan_number' => $deliveryChallanInput['challan_number'],
        'invoice_type' => DeliveryChallanTransaction::WITH_AMOUNT,
    ]);
    $deliveryChallanTransaction = DeliveryChallanTransaction::with('transactionItems')->whereId($response->json('data.delivery_challan_transaction.id'))->first();
    $this->assertEquals(78.22, $deliveryChallanTransaction->transactionItems->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 3 for delivery challan transaction with amount invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 3]);
    $deliveryChallanInput = DeliveryChallanTransactionFactory::new()->make([
        'invoice_type' => DeliveryChallanTransaction::WITH_AMOUNT,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $deliveryChallanInput['items'][0]['rpu'] = 100.12121112;
    $deliveryChallanInput['items'][0]['rpu_without_gst'] = 100.12121112;
    $deliveryChallanInput['items'][0]['rpu_with_gst'] = 100.12121112;
    $deliveryChallanInput['items'][0]['gst_id'] = $gst->id;
    $deliveryChallanInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('delivery-challan-transaction'), $deliveryChallanInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Delivery Challan Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('delivery_challan_transactions', [
        'id' => $response->json('data.delivery_challan_transaction.id'),
        'company_id' => 1,
        'challan_number' => $deliveryChallanInput['challan_number'],
        'invoice_type' => DeliveryChallanTransaction::WITH_AMOUNT,
    ]);
    $deliveryChallanTransaction = DeliveryChallanTransaction::with('transactionItems')->whereId($response->json('data.delivery_challan_transaction.id'))->first();
    $this->assertEquals(78.220, $deliveryChallanTransaction->transactionItems->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 4 for delivery challan transaction with amount invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 4]);
    $deliveryChallanInput = DeliveryChallanTransactionFactory::new()->make([
        'invoice_type' => DeliveryChallanTransaction::WITH_AMOUNT,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $deliveryChallanInput['items'][0]['rpu'] = 100.12121112;
    $deliveryChallanInput['items'][0]['rpu_without_gst'] = 100.12121112;
    $deliveryChallanInput['items'][0]['rpu_with_gst'] = 100.12121112;
    $deliveryChallanInput['items'][0]['gst_id'] = $gst->id;
    $deliveryChallanInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('delivery-challan-transaction'), $deliveryChallanInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Delivery Challan Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('delivery_challan_transactions', [
        'id' => $response->json('data.delivery_challan_transaction.id'),
        'company_id' => 1,
        'challan_number' => $deliveryChallanInput['challan_number'],
        'invoice_type' => DeliveryChallanTransaction::WITH_AMOUNT,
    ]);
    $deliveryChallanTransaction = DeliveryChallanTransaction::with('transactionItems')->whereId($response->json('data.delivery_challan_transaction.id'))->first();
    $this->assertEquals(78.2197, $deliveryChallanTransaction->transactionItems->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 5 for delivery challan transaction with amount invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 5]);
    $deliveryChallanInput = DeliveryChallanTransactionFactory::new()->make([
        'invoice_type' => DeliveryChallanTransaction::WITH_AMOUNT,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $deliveryChallanInput['items'][0]['rpu'] = 100.12121112;
    $deliveryChallanInput['items'][0]['rpu_without_gst'] = 100.12121112;
    $deliveryChallanInput['items'][0]['rpu_with_gst'] = 100.12121112;
    $deliveryChallanInput['items'][0]['gst_id'] = $gst->id;
    $deliveryChallanInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('delivery-challan-transaction'), $deliveryChallanInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Delivery Challan Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('delivery_challan_transactions', [
        'id' => $response->json('data.delivery_challan_transaction.id'),
        'company_id' => 1,
        'challan_number' => $deliveryChallanInput['challan_number'],
        'invoice_type' => DeliveryChallanTransaction::WITH_AMOUNT,
    ]);
    $deliveryChallanTransaction = DeliveryChallanTransaction::with('transactionItems')->whereId($response->json('data.delivery_challan_transaction.id'))->first();
    $this->assertEquals(78.21970, $deliveryChallanTransaction->transactionItems->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 1 for delivery challan transaction with amount invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 1]);
    $deliveryChallanInput = DeliveryChallanTransactionFactory::new()->make([
        'invoice_type' => DeliveryChallanTransaction::WITH_AMOUNT,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $deliveryChallanInput['items'][0]['rpu'] = 100.12121112;
    $deliveryChallanInput['items'][0]['rpu_without_gst'] = 100.12121112;
    $deliveryChallanInput['items'][0]['rpu_with_gst'] = 100.12121112;
    $deliveryChallanInput['items'][0]['gst_id'] = $gst->id;
    $deliveryChallanInput['items'][0]['gst_tax_percentage'] = 2;
    $deliveryChallanInput['items'][0]['with_tax'] = false;

    //act
    $response = $this->postJson(route('delivery-challan-transaction'), $deliveryChallanInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Delivery Challan Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('delivery_challan_transactions', [
        'id' => $response->json('data.delivery_challan_transaction.id'),
        'company_id' => 1,
        'challan_number' => $deliveryChallanInput['challan_number'],
        'invoice_type' => DeliveryChallanTransaction::WITH_AMOUNT,
    ]);
    $deliveryChallanTransaction = DeliveryChallanTransaction::with('transactionItems')->whereId($response->json('data.delivery_challan_transaction.id'))->first();
    $this->assertEquals(128.2, $deliveryChallanTransaction->transactionItems->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 2 for delivery challan transaction with amount invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 2]);
    $deliveryChallanInput = DeliveryChallanTransactionFactory::new()->make([
        'invoice_type' => DeliveryChallanTransaction::WITH_AMOUNT,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $deliveryChallanInput['items'][0]['rpu'] = 100.12121112;
    $deliveryChallanInput['items'][0]['rpu_without_gst'] = 100.12121112;
    $deliveryChallanInput['items'][0]['rpu_with_gst'] = 100.12121112;
    $deliveryChallanInput['items'][0]['gst_id'] = $gst->id;
    $deliveryChallanInput['items'][0]['gst_tax_percentage'] = 2;
    $deliveryChallanInput['items'][0]['with_tax'] = false;

    //act
    $response = $this->postJson(route('delivery-challan-transaction'), $deliveryChallanInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Delivery Challan Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('delivery_challan_transactions', [
        'id' => $response->json('data.delivery_challan_transaction.id'),
        'company_id' => 1,
        'challan_number' => $deliveryChallanInput['challan_number'],
        'invoice_type' => DeliveryChallanTransaction::WITH_AMOUNT,
    ]);
    $deliveryChallanTransaction = DeliveryChallanTransaction::with('transactionItems')->whereId($response->json('data.delivery_challan_transaction.id'))->first();
    $this->assertEquals(128.16, $deliveryChallanTransaction->transactionItems->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 3 for delivery challan transaction with amount invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 3]);
    $deliveryChallanInput = DeliveryChallanTransactionFactory::new()->make([
        'invoice_type' => DeliveryChallanTransaction::WITH_AMOUNT,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $deliveryChallanInput['items'][0]['rpu'] = 100.12121112;
    $deliveryChallanInput['items'][0]['rpu_without_gst'] = 100.12121112;
    $deliveryChallanInput['items'][0]['rpu_with_gst'] = 100.12121112;
    $deliveryChallanInput['items'][0]['gst_id'] = $gst->id;
    $deliveryChallanInput['items'][0]['gst_tax_percentage'] = 2;
    $deliveryChallanInput['items'][0]['with_tax'] = false;

    //act
    $response = $this->postJson(route('delivery-challan-transaction'), $deliveryChallanInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Delivery Challan Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('delivery_challan_transactions', [
        'id' => $response->json('data.delivery_challan_transaction.id'),
        'company_id' => 1,
        'challan_number' => $deliveryChallanInput['challan_number'],
        'invoice_type' => DeliveryChallanTransaction::WITH_AMOUNT,
    ]);
    $deliveryChallanTransaction = DeliveryChallanTransaction::with('transactionItems')->whereId($response->json('data.delivery_challan_transaction.id'))->first();
    $this->assertEquals(128.155, $deliveryChallanTransaction->transactionItems->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 4 for delivery challan transaction with amount invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 4]);
    $deliveryChallanInput = DeliveryChallanTransactionFactory::new()->make([
        'invoice_type' => DeliveryChallanTransaction::WITH_AMOUNT,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $deliveryChallanInput['items'][0]['rpu'] = 100.12121112;
    $deliveryChallanInput['items'][0]['rpu_without_gst'] = 100.12121112;
    $deliveryChallanInput['items'][0]['rpu_with_gst'] = 100.12121112;
    $deliveryChallanInput['items'][0]['gst_id'] = $gst->id;
    $deliveryChallanInput['items'][0]['gst_tax_percentage'] = 2;
    $deliveryChallanInput['items'][0]['with_tax'] = false;

    //act
    $response = $this->postJson(route('delivery-challan-transaction'), $deliveryChallanInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Delivery Challan Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('delivery_challan_transactions', [
        'id' => $response->json('data.delivery_challan_transaction.id'),
        'company_id' => 1,
        'challan_number' => $deliveryChallanInput['challan_number'],
        'invoice_type' => DeliveryChallanTransaction::WITH_AMOUNT,
    ]);
    $deliveryChallanTransaction = DeliveryChallanTransaction::with('transactionItems')->whereId($response->json('data.delivery_challan_transaction.id'))->first();
    $this->assertEquals(128.1552, $deliveryChallanTransaction->transactionItems->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 5 for delivery challan transaction with amount invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 5]);
    $deliveryChallanInput = DeliveryChallanTransactionFactory::new()->make([
        'invoice_type' => DeliveryChallanTransaction::WITH_AMOUNT,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $deliveryChallanInput['items'][0]['rpu'] = 100.12121112;
    $deliveryChallanInput['items'][0]['rpu_without_gst'] = 100.12121112;
    $deliveryChallanInput['items'][0]['rpu_with_gst'] = 100.12121112;
    $deliveryChallanInput['items'][0]['gst_id'] = $gst->id;
    $deliveryChallanInput['items'][0]['gst_tax_percentage'] = 2;
    $deliveryChallanInput['items'][0]['with_tax'] = false;

    //act
    $response = $this->postJson(route('delivery-challan-transaction'), $deliveryChallanInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Delivery Challan Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('delivery_challan_transactions', [
        'id' => $response->json('data.delivery_challan_transaction.id'),
        'company_id' => 1,
        'challan_number' => $deliveryChallanInput['challan_number'],
        'invoice_type' => DeliveryChallanTransaction::WITH_AMOUNT,
    ]);
    $deliveryChallanTransaction = DeliveryChallanTransaction::with('transactionItems')->whereId($response->json('data.delivery_challan_transaction.id'))->first();
    $this->assertEquals(128.15515, $deliveryChallanTransaction->transactionItems->first()->rpu_with_gst);
});

test('validation with update negative and wrong rpu without or with gst for delivery challan transaction with amount invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $deliveryChallanInput = DeliveryChallanTransactionFactory::new()->make([
        'invoice_type' => DeliveryChallanTransaction::WITH_AMOUNT,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $deliveryChallanInput['items'][0]['rpu_without_gst'] = -10000;
    $deliveryChallanInput['items'][0]['rpu_with_gst'] = -10000;
    $deliveryChallanInput['items'][0]['gst_id'] = $gst->id;
    $deliveryChallanInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('delivery-challan-transaction'), $deliveryChallanInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Delivery Challan Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('delivery_challan_transactions', [
        'id' => $response->json('data.delivery_challan_transaction.id'),
        'company_id' => 1,
        'challan_number' => $deliveryChallanInput['challan_number'],
        'invoice_type' => DeliveryChallanTransaction::WITH_AMOUNT,
    ]);
    $deliveryChallanTransaction = DeliveryChallanTransaction::with('transactionItems')->whereId($response->json('data.delivery_challan_transaction.id'))->first();
    $this->assertNotEquals(100, $deliveryChallanTransaction->transactionItems->first()->rpu_without_gst);
    $this->assertEquals(100.0, $deliveryChallanTransaction->transactionItems->first()->rpu_with_gst);
});

test('validation with update negative and wrong gst tax percentage for delivery challan transaction with amount invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $deliveryChallanInput = DeliveryChallanTransactionFactory::new()->make([
        'invoice_type' => DeliveryChallanTransaction::WITH_AMOUNT,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $deliveryChallanInput['items'][0]['total'] = -10000;
    $deliveryChallanInput['items'][0]['gst_id'] = $gst->id;
    $deliveryChallanInput['items'][0]['gst_tax_percentage'] = $gst->tax_rate;

    //act
    $response = $this->postJson(route('delivery-challan-transaction'), $deliveryChallanInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Delivery Challan Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('delivery_challan_transactions', [
        'id' => $response->json('data.delivery_challan_transaction.id'),
        'company_id' => 1,
        'challan_number' => $deliveryChallanInput['challan_number'],
        'invoice_type' => DeliveryChallanTransaction::WITH_AMOUNT,
    ]);
    $deliveryChallanTransaction = DeliveryChallanTransaction::with('transactionItems')->whereId($response->json('data.delivery_challan_transaction.id'))->first();
    $this->assertEquals(28, intval($deliveryChallanTransaction->transactionItems->first()->gst_tax_percentage));
});

test('validation with update negative and wrong total discount for delivery challan transaction with amount invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(5.00)->first();

    $deliveryChallanInput = DeliveryChallanTransactionFactory::new()->make([
        'invoice_type' => DeliveryChallanTransaction::WITH_AMOUNT,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    foreach ($deliveryChallanInput['items'] as $key => $item) {
        $deliveryChallanInput['items'][$key]['gst_id'] = $gst->id;
        $deliveryChallanInput['items'][$key]['discount_type'] = 2;
        $deliveryChallanInput['items'][$key]['discount_value'] = 10;
        $deliveryChallanInput['items'][$key]['discount_type_2'] = 2;
        $deliveryChallanInput['items'][$key]['discount_value_2'] = 10;
        $deliveryChallanInput['items'][$key]['total_discount_amount'] = 10;
    }

    //act
    $response = $this->postJson(route('delivery-challan-transaction'), $deliveryChallanInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Delivery Challan Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('delivery_challan_transactions', [
        'id' => $response->json('data.delivery_challan_transaction.id'),
        'company_id' => 1,
        'challan_number' => $deliveryChallanInput['challan_number'],
        'invoice_type' => DeliveryChallanTransaction::WITH_AMOUNT,
    ]);
    $deliveryChallanTransaction = DeliveryChallanTransaction::with('transactionItems')->whereId($response->json('data.delivery_challan_transaction.id'))->first();
    foreach ($deliveryChallanTransaction->transactionItems as $item) {
        $this->assertEquals(18.1, $item->total_discount_amount);
    }
});

test('validation with update negative and wrong sub total for delivery challan transaction with amount invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $deliveryChallanInput = DeliveryChallanTransactionFactory::new()->make([
        'invoice_type' => DeliveryChallanTransaction::WITH_AMOUNT,
    ])->toArray();
    $deliveryChallanInput['items'][0]['total'] = -10000;

    //act
    $response = $this->postJson(route('delivery-challan-transaction'), $deliveryChallanInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Delivery Challan Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('delivery_challan_transactions', [
        'id' => $response->json('data.delivery_challan_transaction.id'),
        'company_id' => 1,
        'challan_number' => $deliveryChallanInput['challan_number'],
        'invoice_type' => DeliveryChallanTransaction::WITH_AMOUNT,
    ]);
    $deliveryChallanTransaction = DeliveryChallanTransaction::with('transactionItems')->whereId($response->json('data.delivery_challan_transaction.id'))->first();
    $this->assertEquals(100, intval($deliveryChallanTransaction->transactionItems->first()->total));
});

test('validation with update negative and wrong total for delivery challan transaction with amount invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $deliveryChallanInput = DeliveryChallanTransactionFactory::new()->make([
        'invoice_type' => DeliveryChallanTransaction::WITH_AMOUNT,
        'total' => -600,
    ])->toArray();

    //act
    $response = $this->postJson(route('delivery-challan-transaction'), $deliveryChallanInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Delivery Challan Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('delivery_challan_transactions', [
        'id' => $response->json('data.delivery_challan_transaction.id'),
        'company_id' => 1,
        'challan_number' => $deliveryChallanInput['challan_number'],
        'invoice_type' => DeliveryChallanTransaction::WITH_AMOUNT,
        'total' => 620.00,
    ]);
});

test('validation with update negative and wrong gross value for delivery challan transaction with amount invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $deliveryChallanInput = DeliveryChallanTransactionFactory::new()->make([
        'invoice_type' => DeliveryChallanTransaction::WITH_AMOUNT,
        'gross_value' => -100,
    ])->toArray();

    //act
    $response = $this->postJson(route('delivery-challan-transaction'), $deliveryChallanInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Delivery Challan Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('delivery_challan_transactions', [
        'id' => $response->json('data.delivery_challan_transaction.id'),
        'company_id' => 1,
        'challan_number' => $deliveryChallanInput['challan_number'],
        'invoice_type' => DeliveryChallanTransaction::WITH_AMOUNT,
        'gross_value' => 600.00,
    ]);
});

test('validation with update negative and wrong additional charges gst tax percentage for delivery challan transaction with amount invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $deliveryChallanInput = DeliveryChallanTransactionFactory::new()->make([
        'invoice_type' => DeliveryChallanTransaction::WITH_AMOUNT,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $deliveryChallanInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    $deliveryChallanInput['additional_charges'][0]['ac_total'] = -2000;

    //act
    $response = $this->postJson(route('delivery-challan-transaction'), $deliveryChallanInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Delivery Challan Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('delivery_challan_transactions', [
        'id' => $response->json('data.delivery_challan_transaction.id'),
        'company_id' => 1,
        'challan_number' => $deliveryChallanInput['challan_number'],
        'invoice_type' => DeliveryChallanTransaction::WITH_AMOUNT,
    ]);
    $deliveryChallanTransaction = DeliveryChallanTransaction::with('additionalCharges')->whereId($response->json('data.delivery_challan_transaction.id'))->first();
    $this->assertNotEquals(10.00, $deliveryChallanTransaction->additionalCharges->first()->total);
});

test('validation with update negative and wrong additional charges total for delivery challan transaction with amount invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $deliveryChallanInput = DeliveryChallanTransactionFactory::new()->make([
        'invoice_type' => DeliveryChallanTransaction::WITH_AMOUNT,
    ])->toArray();
    $deliveryChallanInput['additional_charges'][0]['ac_total'] = -200;

    //act
    $response = $this->postJson(route('delivery-challan-transaction'), $deliveryChallanInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Delivery Challan Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('delivery_challan_transactions', [
        'id' => $response->json('data.delivery_challan_transaction.id'),
        'company_id' => 1,
        'challan_number' => $deliveryChallanInput['challan_number'],
        'invoice_type' => DeliveryChallanTransaction::WITH_AMOUNT,
        'gross_value' => 600.00,
    ]);
    $deliveryChallanTransaction = DeliveryChallanTransaction::with('additionalCharges')->whereId($response->json('data.delivery_challan_transaction.id'))->first();
    $this->assertEquals(10, intval($deliveryChallanTransaction->additionalCharges->first()->total));
});

test('validation with update negative and wrong cgst and sgst when sale intra state taxable classification for delivery challan transaction with amount invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $deliveryChallanInput = DeliveryChallanTransactionFactory::new()->make([
        'invoice_type' => DeliveryChallanTransaction::WITH_AMOUNT,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'main_classification_nature_type' => 'Sale Intra State Taxable',
    ])->toArray();
    $deliveryChallanInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    foreach ($deliveryChallanInput['items'] as $key => $item) {
        $deliveryChallanInput['items'][$key]['gst_id'] = $gst->id;
    }

    //act
    $response = $this->postJson(route('delivery-challan-transaction'), $deliveryChallanInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Delivery Challan Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('delivery_challan_transactions', [
        'id' => $response->json('data.delivery_challan_transaction.id'),
        'company_id' => 1,
        'challan_number' => $deliveryChallanInput['challan_number'],
        'invoice_type' => DeliveryChallanTransaction::WITH_AMOUNT,
    ]);
    $deliveryChallanTransaction = DeliveryChallanTransaction::whereId($response->json('data.delivery_challan_transaction.id'))->first();
    $this->assertEquals(67.04, $deliveryChallanTransaction->cgst);
    $this->assertEquals(67.04, $deliveryChallanTransaction->sgst);
});

test('validation with update negative and wrong igst when sale inter state taxable classification for delivery challan transaction with amount invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $deliveryChallanInput = DeliveryChallanTransactionFactory::new()->make([
        'invoice_type' => DeliveryChallanTransaction::WITH_AMOUNT,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'main_classification_nature_type' => 'Sale Inter State Taxable',
    ])->toArray();
    $deliveryChallanInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    foreach ($deliveryChallanInput['items'] as $key => $item) {
        $deliveryChallanInput['items'][$key]['gst_id'] = $gst->id;
    }

    //act
    $response = $this->postJson(route('delivery-challan-transaction'), $deliveryChallanInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Delivery Challan Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('delivery_challan_transactions', [
        'id' => $response->json('data.delivery_challan_transaction.id'),
        'company_id' => 1,
        'challan_number' => $deliveryChallanInput['challan_number'],
        'invoice_type' => DeliveryChallanTransaction::WITH_AMOUNT,
    ]);
    $deliveryChallanTransaction = DeliveryChallanTransaction::whereId($response->json('data.delivery_challan_transaction.id'))->first();
    $this->assertEquals(134.08, $deliveryChallanTransaction->igst);
});

test('validation with update negative and wrong taxable value for delivery challan transaction with amount invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $deliveryChallanInput = DeliveryChallanTransactionFactory::new()->make([
        'invoice_type' => DeliveryChallanTransaction::WITH_AMOUNT,
        'taxable_value' => -100,
    ])->toArray();

    //act
    $response = $this->postJson(route('delivery-challan-transaction'), $deliveryChallanInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Delivery Challan Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('delivery_challan_transactions', [
        'id' => $response->json('data.delivery_challan_transaction.id'),
        'company_id' => 1,
        'challan_number' => $deliveryChallanInput['challan_number'],
        'invoice_type' => DeliveryChallanTransaction::WITH_AMOUNT,
        'taxable_value' => 610.00,
    ]);
});

test('validation with update negative and wrong addless total for delivery challan transaction with amount invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $deliveryChallanInput = DeliveryChallanTransactionFactory::new()->make([
        'invoice_type' => DeliveryChallanTransaction::WITH_AMOUNT,
    ])->toArray();
    $deliveryChallanInput['add_less'][0]['al_total'] = -100;

    //act
    $response = $this->postJson(route('delivery-challan-transaction'), $deliveryChallanInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Delivery Challan Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('delivery_challan_transactions', [
        'id' => $response->json('data.delivery_challan_transaction.id'),
        'company_id' => 1,
        'challan_number' => $deliveryChallanInput['challan_number'],
        'invoice_type' => DeliveryChallanTransaction::WITH_AMOUNT,
        'gross_value' => 600.00,
    ]);
    $deliveryChallanTransaction = DeliveryChallanTransaction::with('addLess')->whereId($response->json('data.delivery_challan_transaction.id'))->first();
    $this->assertEquals(10, intval($deliveryChallanTransaction->addLess->first()->total));
});

test('validation with update negative and wrong grand total for delivery challan transaction with amount invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $deliveryChallanInput = DeliveryChallanTransactionFactory::new()->make([
        'invoice_type' => DeliveryChallanTransaction::WITH_AMOUNT,
        'grand_total' => -100,
    ])->toArray();

    //act
    $response = $this->postJson(route('delivery-challan-transaction'), $deliveryChallanInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Delivery Challan Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('delivery_challan_transactions', [
        'id' => $response->json('data.delivery_challan_transaction.id'),
        'company_id' => 1,
        'challan_number' => $deliveryChallanInput['challan_number'],
        'invoice_type' => DeliveryChallanTransaction::WITH_AMOUNT,
        'grand_total' => 620.00,
    ]);
});

test('validation with update negative and wrong round off amount and method for delivery challan transaction', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);
    $deliveryChallanInput = DeliveryChallanTransactionFactory::new()->make([
        'invoice_type' => DeliveryChallanTransaction::WITH_AMOUNT,
    ])->toArray();
    $deliveryChallanInput['additional_charges'][0]['ac_value'] = 10.54;

    $roundOffMethods = [
        ['method' => 1, 'correct_amount' => 0],
        ['method' => 2, 'correct_amount' => -0.54],
        ['method' => 3, 'correct_amount' => 0.46],
        ['method' => 4, 'correct_amount' => 0.46],
    ];
    foreach ($roundOffMethods as $roundOffMethod) {
        $deliveryChallanInput['challan_number'] = 'DC-'.$roundOffMethod['method'];
        $deliveryChallanInput['round_off_method'] = 0;
        DeliveryChallanTransactionMaster::whereCompanyId(1)->update(['round_off_method' => $roundOffMethod['method']]);
        $deliveryChallanInput['rounding_amount'] = -10.50;

        //act
        $response = $this->postJson(route('delivery-challan-transaction'), $deliveryChallanInput);

        //assert
        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Delivery Challan Transaction Created Successfully',
            ]);
        $this->assertDatabaseHas('delivery_challan_transactions', [
            'id' => $response->json('data.delivery_challan_transaction.id'),
            'company_id' => 1,
            'challan_number' => $deliveryChallanInput['challan_number'],
            'invoice_type' => DeliveryChallanTransaction::WITH_AMOUNT,
            'round_off_method' => $roundOffMethod,
            'rounding_amount' => $roundOffMethod['correct_amount'],
        ]);
    }
});

/*
|----------------------------------------------------------------------------------------------------------------------------------------------------
| Update Delivery Challan Transaction With Amount Invoice Backend Validation with update Test Case
|----------------------------------------------------------------------------------------------------------------------------------------------------
*/

test('validation with update decimal rpu amount when decimal place is 1 for update delivery challan transaction with amount invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 1]);
    $deliveryChallanInput = DeliveryChallanTransactionFactory::new()->make([
        'invoice_type' => DeliveryChallanTransaction::WITH_AMOUNT,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $deliveryChallanData = StoreDeliveryChallanTransactionAction::run($deliveryChallanInput);
    $deliveryChallanInput['items'][0]['rpu'] = 100.12121112;
    $deliveryChallanInput['items'][0]['rpu_without_gst'] = 100.12121112;
    $deliveryChallanInput['items'][0]['rpu_with_gst'] = 100.12121112;
    $deliveryChallanInput['items'][0]['gst_id'] = $gst->id;
    $deliveryChallanInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('delivery-challan-transaction-update', $deliveryChallanData['delivery_challan_transaction']['id']), $deliveryChallanInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Delivery Challan Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('delivery_challan_transactions', [
        'id' => $deliveryChallanData['delivery_challan_transaction']['id'],
        'company_id' => 1,
        'challan_number' => $deliveryChallanInput['challan_number'],
        'invoice_type' => DeliveryChallanTransaction::WITH_AMOUNT,
    ]);
    $deliveryChallanTransaction = DeliveryChallanTransaction::with('transactionItems')->whereId($deliveryChallanData['delivery_challan_transaction']['id'])->first();
    $this->assertEquals(78.2, $deliveryChallanTransaction->transactionItems->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 2 for update delivery challan transaction with amount invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 2]);
    $deliveryChallanInput = DeliveryChallanTransactionFactory::new()->make([
        'invoice_type' => DeliveryChallanTransaction::WITH_AMOUNT,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $deliveryChallanData = StoreDeliveryChallanTransactionAction::run($deliveryChallanInput);
    $deliveryChallanInput['items'][0]['rpu'] = 100.12121112;
    $deliveryChallanInput['items'][0]['rpu_without_gst'] = 100.12121112;
    $deliveryChallanInput['items'][0]['rpu_with_gst'] = 100.12121112;
    $deliveryChallanInput['items'][0]['gst_id'] = $gst->id;
    $deliveryChallanInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('delivery-challan-transaction-update', $deliveryChallanData['delivery_challan_transaction']['id']), $deliveryChallanInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Delivery Challan Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('delivery_challan_transactions', [
        'id' => $deliveryChallanData['delivery_challan_transaction']['id'],
        'company_id' => 1,
        'challan_number' => $deliveryChallanInput['challan_number'],
        'invoice_type' => DeliveryChallanTransaction::WITH_AMOUNT,
    ]);
    $deliveryChallanTransaction = DeliveryChallanTransaction::with('transactionItems')->whereId($deliveryChallanData['delivery_challan_transaction']['id'])->first();
    $this->assertEquals(78.22, $deliveryChallanTransaction->transactionItems->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 3 for update delivery challan transaction with amount invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 3]);
    $deliveryChallanInput = DeliveryChallanTransactionFactory::new()->make([
        'invoice_type' => DeliveryChallanTransaction::WITH_AMOUNT,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $deliveryChallanData = StoreDeliveryChallanTransactionAction::run($deliveryChallanInput);
    $deliveryChallanInput['items'][0]['rpu'] = 100.12121112;
    $deliveryChallanInput['items'][0]['rpu_without_gst'] = 100.12121112;
    $deliveryChallanInput['items'][0]['rpu_with_gst'] = 100.12121112;
    $deliveryChallanInput['items'][0]['gst_id'] = $gst->id;
    $deliveryChallanInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('delivery-challan-transaction-update', $deliveryChallanData['delivery_challan_transaction']['id']), $deliveryChallanInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Delivery Challan Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('delivery_challan_transactions', [
        'id' => $deliveryChallanData['delivery_challan_transaction']['id'],
        'company_id' => 1,
        'challan_number' => $deliveryChallanInput['challan_number'],
        'invoice_type' => DeliveryChallanTransaction::WITH_AMOUNT,
    ]);
    $deliveryChallanTransaction = DeliveryChallanTransaction::with('transactionItems')->whereId($deliveryChallanData['delivery_challan_transaction']['id'])->first();
    $this->assertEquals(78.220, $deliveryChallanTransaction->transactionItems->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 4 for update delivery challan transaction with amount invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 4]);
    $deliveryChallanInput = DeliveryChallanTransactionFactory::new()->make([
        'invoice_type' => DeliveryChallanTransaction::WITH_AMOUNT,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $deliveryChallanData = StoreDeliveryChallanTransactionAction::run($deliveryChallanInput);
    $deliveryChallanInput['items'][0]['rpu'] = 100.12121112;
    $deliveryChallanInput['items'][0]['rpu_without_gst'] = 100.12121112;
    $deliveryChallanInput['items'][0]['rpu_with_gst'] = 100.12121112;
    $deliveryChallanInput['items'][0]['gst_id'] = $gst->id;
    $deliveryChallanInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('delivery-challan-transaction-update', $deliveryChallanData['delivery_challan_transaction']['id']), $deliveryChallanInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Delivery Challan Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('delivery_challan_transactions', [
        'id' => $deliveryChallanData['delivery_challan_transaction']['id'],
        'company_id' => 1,
        'challan_number' => $deliveryChallanInput['challan_number'],
        'invoice_type' => DeliveryChallanTransaction::WITH_AMOUNT,
    ]);
    $deliveryChallanTransaction = DeliveryChallanTransaction::with('transactionItems')->whereId($deliveryChallanData['delivery_challan_transaction']['id'])->first();
    $this->assertEquals(78.2197, $deliveryChallanTransaction->transactionItems->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 5 for update delivery challan transaction with amount invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 5]);
    $deliveryChallanInput = DeliveryChallanTransactionFactory::new()->make([
        'invoice_type' => DeliveryChallanTransaction::WITH_AMOUNT,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $deliveryChallanData = StoreDeliveryChallanTransactionAction::run($deliveryChallanInput);
    $deliveryChallanInput['items'][0]['rpu'] = 100.12121112;
    $deliveryChallanInput['items'][0]['rpu_without_gst'] = 100.12121112;
    $deliveryChallanInput['items'][0]['rpu_with_gst'] = 100.12121112;
    $deliveryChallanInput['items'][0]['gst_id'] = $gst->id;
    $deliveryChallanInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('delivery-challan-transaction-update', $deliveryChallanData['delivery_challan_transaction']['id']), $deliveryChallanInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Delivery Challan Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('delivery_challan_transactions', [
        'id' => $deliveryChallanData['delivery_challan_transaction']['id'],
        'company_id' => 1,
        'challan_number' => $deliveryChallanInput['challan_number'],
        'invoice_type' => DeliveryChallanTransaction::WITH_AMOUNT,
    ]);
    $deliveryChallanTransaction = DeliveryChallanTransaction::with('transactionItems')->whereId($deliveryChallanData['delivery_challan_transaction']['id'])->first();
    $this->assertEquals(78.21970, $deliveryChallanTransaction->transactionItems->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 1 for update delivery challan transaction with amount invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 1]);
    $deliveryChallanInput = DeliveryChallanTransactionFactory::new()->make([
        'invoice_type' => DeliveryChallanTransaction::WITH_AMOUNT,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $deliveryChallanData = StoreDeliveryChallanTransactionAction::run($deliveryChallanInput);
    $deliveryChallanInput['items'][0]['rpu'] = 100.12121112;
    $deliveryChallanInput['items'][0]['rpu_without_gst'] = 100.12121112;
    $deliveryChallanInput['items'][0]['rpu_with_gst'] = 100.12121112;
    $deliveryChallanInput['items'][0]['gst_id'] = $gst->id;
    $deliveryChallanInput['items'][0]['gst_tax_percentage'] = 2;
    $deliveryChallanInput['items'][0]['with_tax'] = false;

    //act
    $response = $this->postJson(route('delivery-challan-transaction-update', $deliveryChallanData['delivery_challan_transaction']['id']), $deliveryChallanInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Delivery Challan Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('delivery_challan_transactions', [
        'id' => $deliveryChallanData['delivery_challan_transaction']['id'],
        'company_id' => 1,
        'challan_number' => $deliveryChallanInput['challan_number'],
        'invoice_type' => DeliveryChallanTransaction::WITH_AMOUNT,
    ]);
    $deliveryChallanTransaction = DeliveryChallanTransaction::with('transactionItems')->whereId($deliveryChallanData['delivery_challan_transaction']['id'])->first();
    $this->assertEquals(128.2, $deliveryChallanTransaction->transactionItems->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 2 for update delivery challan transaction with amount invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 2]);
    $deliveryChallanInput = DeliveryChallanTransactionFactory::new()->make([
        'invoice_type' => DeliveryChallanTransaction::WITH_AMOUNT,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $deliveryChallanData = StoreDeliveryChallanTransactionAction::run($deliveryChallanInput);
    $deliveryChallanInput['items'][0]['rpu'] = 100.12121112;
    $deliveryChallanInput['items'][0]['rpu_without_gst'] = 100.12121112;
    $deliveryChallanInput['items'][0]['rpu_with_gst'] = 100.12121112;
    $deliveryChallanInput['items'][0]['gst_id'] = $gst->id;
    $deliveryChallanInput['items'][0]['gst_tax_percentage'] = 2;
    $deliveryChallanInput['items'][0]['with_tax'] = false;

    //act
    $response = $this->postJson(route('delivery-challan-transaction-update', $deliveryChallanData['delivery_challan_transaction']['id']), $deliveryChallanInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Delivery Challan Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('delivery_challan_transactions', [
        'id' => $deliveryChallanData['delivery_challan_transaction']['id'],
        'company_id' => 1,
        'challan_number' => $deliveryChallanInput['challan_number'],
        'invoice_type' => DeliveryChallanTransaction::WITH_AMOUNT,
    ]);
    $deliveryChallanTransaction = DeliveryChallanTransaction::with('transactionItems')->whereId($deliveryChallanData['delivery_challan_transaction']['id'])->first();
    $this->assertEquals(128.16, $deliveryChallanTransaction->transactionItems->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 3 for update delivery challan transaction with amount invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 3]);
    $deliveryChallanInput = DeliveryChallanTransactionFactory::new()->make([
        'invoice_type' => DeliveryChallanTransaction::WITH_AMOUNT,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $deliveryChallanData = StoreDeliveryChallanTransactionAction::run($deliveryChallanInput);
    $deliveryChallanInput['items'][0]['rpu'] = 100.12121112;
    $deliveryChallanInput['items'][0]['rpu_without_gst'] = 100.12121112;
    $deliveryChallanInput['items'][0]['rpu_with_gst'] = 100.12121112;
    $deliveryChallanInput['items'][0]['gst_id'] = $gst->id;
    $deliveryChallanInput['items'][0]['gst_tax_percentage'] = 2;
    $deliveryChallanInput['items'][0]['with_tax'] = false;

    //act
    $response = $this->postJson(route('delivery-challan-transaction-update', $deliveryChallanData['delivery_challan_transaction']['id']), $deliveryChallanInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Delivery Challan Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('delivery_challan_transactions', [
        'id' => $deliveryChallanData['delivery_challan_transaction']['id'],
        'company_id' => 1,
        'challan_number' => $deliveryChallanInput['challan_number'],
        'invoice_type' => DeliveryChallanTransaction::WITH_AMOUNT,
    ]);
    $deliveryChallanTransaction = DeliveryChallanTransaction::with('transactionItems')->whereId($deliveryChallanData['delivery_challan_transaction']['id'])->first();
    $this->assertEquals(128.155, $deliveryChallanTransaction->transactionItems->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 4 for update delivery challan transaction with amount invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 4]);
    $deliveryChallanInput = DeliveryChallanTransactionFactory::new()->make([
        'invoice_type' => DeliveryChallanTransaction::WITH_AMOUNT,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $deliveryChallanData = StoreDeliveryChallanTransactionAction::run($deliveryChallanInput);
    $deliveryChallanInput['items'][0]['rpu'] = 100.12121112;
    $deliveryChallanInput['items'][0]['rpu_without_gst'] = 100.12121112;
    $deliveryChallanInput['items'][0]['rpu_with_gst'] = 100.12121112;
    $deliveryChallanInput['items'][0]['gst_id'] = $gst->id;
    $deliveryChallanInput['items'][0]['gst_tax_percentage'] = 2;
    $deliveryChallanInput['items'][0]['with_tax'] = false;

    //act
    $response = $this->postJson(route('delivery-challan-transaction-update', $deliveryChallanData['delivery_challan_transaction']['id']), $deliveryChallanInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Delivery Challan Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('delivery_challan_transactions', [
        'id' => $deliveryChallanData['delivery_challan_transaction']['id'],
        'company_id' => 1,
        'challan_number' => $deliveryChallanInput['challan_number'],
        'invoice_type' => DeliveryChallanTransaction::WITH_AMOUNT,
    ]);
    $deliveryChallanTransaction = DeliveryChallanTransaction::with('transactionItems')->whereId($deliveryChallanData['delivery_challan_transaction']['id'])->first();
    $this->assertEquals(128.1552, $deliveryChallanTransaction->transactionItems->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 5 for update delivery challan transaction with amount invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 5]);
    $deliveryChallanInput = DeliveryChallanTransactionFactory::new()->make([
        'invoice_type' => DeliveryChallanTransaction::WITH_AMOUNT,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $deliveryChallanData = StoreDeliveryChallanTransactionAction::run($deliveryChallanInput);
    $deliveryChallanInput['items'][0]['rpu'] = 100.12121112;
    $deliveryChallanInput['items'][0]['rpu_without_gst'] = 100.12121112;
    $deliveryChallanInput['items'][0]['rpu_with_gst'] = 100.12121112;
    $deliveryChallanInput['items'][0]['gst_id'] = $gst->id;
    $deliveryChallanInput['items'][0]['gst_tax_percentage'] = 2;
    $deliveryChallanInput['items'][0]['with_tax'] = false;

    //act
    $response = $this->postJson(route('delivery-challan-transaction-update', $deliveryChallanData['delivery_challan_transaction']['id']), $deliveryChallanInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Delivery Challan Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('delivery_challan_transactions', [
        'id' => $deliveryChallanData['delivery_challan_transaction']['id'],
        'company_id' => 1,
        'challan_number' => $deliveryChallanInput['challan_number'],
        'invoice_type' => DeliveryChallanTransaction::WITH_AMOUNT,
    ]);
    $deliveryChallanTransaction = DeliveryChallanTransaction::with('transactionItems')->whereId($deliveryChallanData['delivery_challan_transaction']['id'])->first();
    $this->assertEquals(128.15515, $deliveryChallanTransaction->transactionItems->first()->rpu_with_gst);
});

test('validation with update negative and wrong rpu without or with gst for update delivery challan transaction with amount invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $deliveryChallanInput = DeliveryChallanTransactionFactory::new()->make([
        'invoice_type' => DeliveryChallanTransaction::WITH_AMOUNT,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $deliveryChallanData = StoreDeliveryChallanTransactionAction::run($deliveryChallanInput);
    $deliveryChallanInput['items'][0]['rpu_without_gst'] = -10000;
    $deliveryChallanInput['items'][0]['rpu_with_gst'] = -10000;
    $deliveryChallanInput['items'][0]['gst_id'] = $gst->id;
    $deliveryChallanInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('delivery-challan-transaction-update', $deliveryChallanData['delivery_challan_transaction']['id']), $deliveryChallanInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Delivery Challan Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('delivery_challan_transactions', [
        'id' => $deliveryChallanData['delivery_challan_transaction']['id'],
        'company_id' => 1,
        'challan_number' => $deliveryChallanInput['challan_number'],
        'invoice_type' => DeliveryChallanTransaction::WITH_AMOUNT,
    ]);
    $deliveryChallanTransaction = DeliveryChallanTransaction::with('transactionItems')->whereId($deliveryChallanData['delivery_challan_transaction']['id'])->first();
    $this->assertNotEquals(100, $deliveryChallanTransaction->transactionItems->first()->rpu_without_gst);
    $this->assertEquals(100.0, $deliveryChallanTransaction->transactionItems->first()->rpu_with_gst);
});

test('validation with update negative and wrong gst tax percentage for update delivery challan transaction with amount invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $deliveryChallanInput = DeliveryChallanTransactionFactory::new()->make([
        'invoice_type' => DeliveryChallanTransaction::WITH_AMOUNT,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $deliveryChallanData = StoreDeliveryChallanTransactionAction::run($deliveryChallanInput);
    $deliveryChallanInput['items'][0]['total'] = -10000;
    $deliveryChallanInput['items'][0]['gst_id'] = $gst->id;
    $deliveryChallanInput['items'][0]['gst_tax_percentage'] = $gst->tax_rate;

    //act
    $response = $this->postJson(route('delivery-challan-transaction-update', $deliveryChallanData['delivery_challan_transaction']['id']), $deliveryChallanInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Delivery Challan Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('delivery_challan_transactions', [
        'id' => $deliveryChallanData['delivery_challan_transaction']['id'],
        'company_id' => 1,
        'challan_number' => $deliveryChallanInput['challan_number'],
        'invoice_type' => DeliveryChallanTransaction::WITH_AMOUNT,
    ]);
    $deliveryChallanTransaction = DeliveryChallanTransaction::with('transactionItems')->whereId($deliveryChallanData['delivery_challan_transaction']['id'])->first();
    $this->assertEquals(28, intval($deliveryChallanTransaction->transactionItems->first()->gst_tax_percentage));
});

test('validation with update negative and wrong total discount for update delivery challan transaction with amount invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(5.00)->first();

    $deliveryChallanInput = DeliveryChallanTransactionFactory::new()->make([
        'invoice_type' => DeliveryChallanTransaction::WITH_AMOUNT,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $deliveryChallanData = StoreDeliveryChallanTransactionAction::run($deliveryChallanInput);
    foreach ($deliveryChallanInput['items'] as $key => $item) {
        $deliveryChallanInput['items'][$key]['gst_id'] = $gst->id;
        $deliveryChallanInput['items'][$key]['discount_type'] = 2;
        $deliveryChallanInput['items'][$key]['discount_value'] = 10;
        $deliveryChallanInput['items'][$key]['discount_type_2'] = 2;
        $deliveryChallanInput['items'][$key]['discount_value_2'] = 10;
        $deliveryChallanInput['items'][$key]['total_discount_amount'] = 10;
    }

    //act
    $response = $this->postJson(route('delivery-challan-transaction-update', $deliveryChallanData['delivery_challan_transaction']['id']), $deliveryChallanInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Delivery Challan Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('delivery_challan_transactions', [
        'id' => $deliveryChallanData['delivery_challan_transaction']['id'],
        'company_id' => 1,
        'challan_number' => $deliveryChallanInput['challan_number'],
        'invoice_type' => DeliveryChallanTransaction::WITH_AMOUNT,
    ]);
    $deliveryChallanTransaction = DeliveryChallanTransaction::with('transactionItems')->whereId($deliveryChallanData['delivery_challan_transaction']['id'])->first();
    foreach ($deliveryChallanTransaction->transactionItems as $item) {
        $this->assertEquals(18.1, $item->total_discount_amount);
    }
});

test('validation with update negative and wrong sub total for update delivery challan transaction with amount invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $deliveryChallanInput = DeliveryChallanTransactionFactory::new()->make([
        'invoice_type' => DeliveryChallanTransaction::WITH_AMOUNT,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $deliveryChallanData = StoreDeliveryChallanTransactionAction::run($deliveryChallanInput);
    $deliveryChallanInput['items'][0]['total'] = -10000;
    $deliveryChallanInput['items'][0]['gst_id'] = $gst->id;
    $deliveryChallanInput['items'][0]['gst_tax_percentage'] = $gst->tax_rate;

    //act
    $response = $this->postJson(route('delivery-challan-transaction-update', $deliveryChallanData['delivery_challan_transaction']['id']), $deliveryChallanInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Delivery Challan Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('delivery_challan_transactions', [
        'id' => $deliveryChallanData['delivery_challan_transaction']['id'],
        'company_id' => 1,
        'challan_number' => $deliveryChallanInput['challan_number'],
        'invoice_type' => DeliveryChallanTransaction::WITH_AMOUNT,
    ]);
    $deliveryChallanInput = DeliveryChallanTransaction::with('transactionItems')->whereId($deliveryChallanData['delivery_challan_transaction']['id'])->first();
    $this->assertEquals(78.13, $deliveryChallanInput->transactionItems->first()->total);
});

test('validation with update negative and wrong sub total for update delivery challan transaction with amount invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $deliveryChallanInput = DeliveryChallanTransactionFactory::new()->make([
        'invoice_type' => DeliveryChallanTransaction::WITH_AMOUNT,
    ])->toArray();
    $deliveryChallanData = StoreDeliveryChallanTransactionAction::run($deliveryChallanInput);
    $deliveryChallanInput['items'][0]['total'] = -10000;

    //act
    $response = $this->postJson(route('delivery-challan-transaction-update', $deliveryChallanData['delivery_challan_transaction']['id']), $deliveryChallanInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Delivery Challan Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('delivery_challan_transactions', [
        'id' => $deliveryChallanData['delivery_challan_transaction']['id'],
        'company_id' => 1,
        'challan_number' => $deliveryChallanInput['challan_number'],
        'invoice_type' => DeliveryChallanTransaction::WITH_AMOUNT,
    ]);
    $deliveryChallanTransaction = DeliveryChallanTransaction::with('transactionItems')->whereId($deliveryChallanData['delivery_challan_transaction']['id'])->first();
    $this->assertEquals(100, intval($deliveryChallanTransaction->transactionItems->first()->total));
});

test('validation with update negative and wrong total for update delivery challan transaction with amount invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $deliveryChallanInput = DeliveryChallanTransactionFactory::new()->make([
        'invoice_type' => DeliveryChallanTransaction::WITH_AMOUNT,
    ])->toArray();
    $deliveryChallanData = StoreDeliveryChallanTransactionAction::run($deliveryChallanInput);
    $deliveryChallanInput['total'] = -620;

    //act
    $response = $this->postJson(route('delivery-challan-transaction-update', $deliveryChallanData['delivery_challan_transaction']['id']), $deliveryChallanInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Delivery Challan Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('delivery_challan_transactions', [
        'id' => $deliveryChallanData['delivery_challan_transaction']['id'],
        'company_id' => 1,
        'challan_number' => $deliveryChallanInput['challan_number'],
        'invoice_type' => DeliveryChallanTransaction::WITH_AMOUNT,
        'total' => 620.00,
    ]);
});

test('validation with update negative and wrong gross value for update delivery challan transaction with amount invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $deliveryChallanInput = DeliveryChallanTransactionFactory::new()->make([
        'invoice_type' => DeliveryChallanTransaction::WITH_AMOUNT,
    ])->toArray();
    $deliveryChallanData = StoreDeliveryChallanTransactionAction::run($deliveryChallanInput);
    $deliveryChallanInput['gross_value'] = -100;

    //act
    $response = $this->postJson(route('delivery-challan-transaction-update', $deliveryChallanData['delivery_challan_transaction']['id']), $deliveryChallanInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Delivery Challan Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('delivery_challan_transactions', [
        'id' => $deliveryChallanData['delivery_challan_transaction']['id'],
        'company_id' => 1,
        'challan_number' => $deliveryChallanInput['challan_number'],
        'invoice_type' => DeliveryChallanTransaction::WITH_AMOUNT,
        'gross_value' => 600.00,
    ]);
});

test('validation with update negative and wrong additional charges gst tax percentage for update delivery challan transaction with amount invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $deliveryChallanInput = DeliveryChallanTransactionFactory::new()->make([
        'invoice_type' => DeliveryChallanTransaction::WITH_AMOUNT,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $deliveryChallanData = StoreDeliveryChallanTransactionAction::run($deliveryChallanInput);
    $deliveryChallanInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    $deliveryChallanInput['additional_charges'][0]['ac_total'] = -2000;

    //act
    $response = $this->postJson(route('delivery-challan-transaction-update', $deliveryChallanData['delivery_challan_transaction']['id']), $deliveryChallanInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Delivery Challan Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('delivery_challan_transactions', [
        'id' => $deliveryChallanData['delivery_challan_transaction']['id'],
        'company_id' => 1,
        'challan_number' => $deliveryChallanInput['challan_number'],
        'invoice_type' => DeliveryChallanTransaction::WITH_AMOUNT,
    ]);
    $deliveryChallanTransaction = DeliveryChallanTransaction::with('additionalCharges')->whereId($deliveryChallanData['delivery_challan_transaction']['id'])->first();
    $this->assertNotEquals(10.00, $deliveryChallanTransaction->additionalCharges->first()->total);
});

test('validation with update negative and wrong additional charges total for update delivery challan transaction with amount invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $deliveryChallanInput = DeliveryChallanTransactionFactory::new()->make([
        'invoice_type' => DeliveryChallanTransaction::WITH_AMOUNT,
    ])->toArray();
    $deliveryChallanData = StoreDeliveryChallanTransactionAction::run($deliveryChallanInput);
    $deliveryChallanInput['additional_charges'][0]['ac_total'] = -200;

    //act
    $response = $this->postJson(route('delivery-challan-transaction-update', $deliveryChallanData['delivery_challan_transaction']['id']), $deliveryChallanInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Delivery Challan Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('delivery_challan_transactions', [
        'id' => $deliveryChallanData['delivery_challan_transaction']['id'],
        'company_id' => 1,
        'challan_number' => $deliveryChallanInput['challan_number'],
        'invoice_type' => DeliveryChallanTransaction::WITH_AMOUNT,
        'gross_value' => 600.00,
    ]);
    $deliveryChallanTransaction = DeliveryChallanTransaction::with('additionalCharges')->whereId($deliveryChallanData['delivery_challan_transaction']['id'])->first();
    $this->assertEquals(10, intval($deliveryChallanTransaction->additionalCharges->first()->total));
});

test('validation with update negative and wrong cgst and sgst when sale intra state taxable classification for update delivery challan transaction with amount invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $deliveryChallanInput = DeliveryChallanTransactionFactory::new()->make([
        'invoice_type' => DeliveryChallanTransaction::WITH_AMOUNT,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'main_classification_nature_type' => 'Sale Intra State Taxable',
    ])->toArray();
    $deliveryChallanData = StoreDeliveryChallanTransactionAction::run($deliveryChallanInput);
    $deliveryChallanInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    foreach ($deliveryChallanInput['items'] as $key => $item) {
        $deliveryChallanInput['items'][$key]['gst_id'] = $gst->id;
    }

    //act
    $response = $this->postJson(route('delivery-challan-transaction-update', $deliveryChallanData['delivery_challan_transaction']['id']), $deliveryChallanInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Delivery Challan Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('delivery_challan_transactions', [
        'id' => $deliveryChallanData['delivery_challan_transaction']['id'],
        'company_id' => 1,
        'challan_number' => $deliveryChallanInput['challan_number'],
        'invoice_type' => DeliveryChallanTransaction::WITH_AMOUNT,
    ]);
    $deliveryChallanTransaction = DeliveryChallanTransaction::whereId($deliveryChallanData['delivery_challan_transaction']['id'])->first();
    $this->assertEquals(67.04, $deliveryChallanTransaction->cgst);
    $this->assertEquals(67.04, $deliveryChallanTransaction->sgst);
});

test('validation with update negative and wrong igst when sale inter state taxable classification for update delivery challan transaction with amount invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $deliveryChallanInput = DeliveryChallanTransactionFactory::new()->make([
        'invoice_type' => DeliveryChallanTransaction::WITH_AMOUNT,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'main_classification_nature_type' => 'Sale Inter State Taxable',
    ])->toArray();
    $deliveryChallanData = StoreDeliveryChallanTransactionAction::run($deliveryChallanInput);
    $deliveryChallanInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    foreach ($deliveryChallanInput['items'] as $key => $item) {
        $deliveryChallanInput['items'][$key]['gst_id'] = $gst->id;
    }

    //act
    $response = $this->postJson(route('delivery-challan-transaction-update', $deliveryChallanData['delivery_challan_transaction']['id']), $deliveryChallanInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Delivery Challan Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('delivery_challan_transactions', [
        'id' => $deliveryChallanData['delivery_challan_transaction']['id'],
        'company_id' => 1,
        'challan_number' => $deliveryChallanInput['challan_number'],
        'invoice_type' => DeliveryChallanTransaction::WITH_AMOUNT,
    ]);
    $deliveryChallanTransaction = DeliveryChallanTransaction::whereId($deliveryChallanData['delivery_challan_transaction']['id'])->first();
    $this->assertEquals(134.08, $deliveryChallanTransaction->igst);
});

test('validation with update negative and wrong taxable value for update delivery challan transaction with amount invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $deliveryChallanInput = DeliveryChallanTransactionFactory::new()->make([
        'invoice_type' => DeliveryChallanTransaction::WITH_AMOUNT,
    ])->toArray();
    $deliveryChallanData = StoreDeliveryChallanTransactionAction::run($deliveryChallanInput);
    $deliveryChallanInput['taxable_value'] = -100;

    //act
    $response = $this->postJson(route('delivery-challan-transaction-update', $deliveryChallanData['delivery_challan_transaction']['id']), $deliveryChallanInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Delivery Challan Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('delivery_challan_transactions', [
        'id' => $deliveryChallanData['delivery_challan_transaction']['id'],
        'company_id' => 1,
        'challan_number' => $deliveryChallanInput['challan_number'],
        'invoice_type' => DeliveryChallanTransaction::WITH_AMOUNT,
        'taxable_value' => 610.00,
    ]);
});

test('validation with update negative and wrong addless total for update delivery challan transaction with amount invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $deliveryChallanInput = DeliveryChallanTransactionFactory::new()->make([
        'invoice_type' => DeliveryChallanTransaction::WITH_AMOUNT,
    ])->toArray();
    $deliveryChallanData = StoreDeliveryChallanTransactionAction::run($deliveryChallanInput);
    $deliveryChallanInput['add_less'][0]['al_total'] = -100;

    //act
    $response = $this->postJson(route('delivery-challan-transaction-update', $deliveryChallanData['delivery_challan_transaction']['id']), $deliveryChallanInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Delivery Challan Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('delivery_challan_transactions', [
        'id' => $deliveryChallanData['delivery_challan_transaction']['id'],
        'company_id' => 1,
        'challan_number' => $deliveryChallanInput['challan_number'],
        'invoice_type' => DeliveryChallanTransaction::WITH_AMOUNT,
        'gross_value' => 600.00,
    ]);
    $deliveryChallanTransaction = DeliveryChallanTransaction::with('addLess')->whereId($deliveryChallanData['delivery_challan_transaction']['id'])->first();
    $this->assertEquals(10, intval($deliveryChallanTransaction->addLess->first()->total));
});

test('validation with update negative and wrong grand total for update delivery challan transaction with amount invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $deliveryChallanInput = DeliveryChallanTransactionFactory::new()->make([
        'invoice_type' => DeliveryChallanTransaction::WITH_AMOUNT,
    ])->toArray();
    $deliveryChallanData = StoreDeliveryChallanTransactionAction::run($deliveryChallanInput);
    $deliveryChallanInput['grand_total'] = -100;

    //act
    $response = $this->postJson(route('delivery-challan-transaction-update', $deliveryChallanData['delivery_challan_transaction']['id']), $deliveryChallanInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Delivery Challan Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('delivery_challan_transactions', [
        'id' => $deliveryChallanData['delivery_challan_transaction']['id'],
        'company_id' => 1,
        'challan_number' => $deliveryChallanInput['challan_number'],
        'invoice_type' => DeliveryChallanTransaction::WITH_AMOUNT,
        'grand_total' => 620.00,
    ]);
});

test('validation with update negative and wrong round off amount and method for delivery challan transaction', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);
    $deliveryChallanInput = DeliveryChallanTransactionFactory::new()->make([
        'invoice_type' => DeliveryChallanTransaction::WITH_AMOUNT,
    ])->toArray();
    $deliveryChallanInput['additional_charges'][0]['ac_value'] = 10.54;

    $roundOffMethods = [
        ['method' => 1, 'correct_amount' => 0],
        ['method' => 2, 'correct_amount' => -0.54],
        ['method' => 3, 'correct_amount' => 0.46],
        ['method' => 4, 'correct_amount' => 0.46],
    ];
    foreach ($roundOffMethods as $roundOffMethod) {
        $deliveryChallanInput['challan_number'] = 'DC-'.$roundOffMethod['method'];
        $deliveryChallanData = StoreDeliveryChallanTransactionAction::run($deliveryChallanInput);
        $deliveryChallanInput['round_off_method'] = 0;
        DeliveryChallanTransactionMaster::whereCompanyId(1)->update(['round_off_method' => $roundOffMethod['method']]);
        $deliveryChallanInput['rounding_amount'] = -10.50;

        //act
        $response = $this->postJson(route('delivery-challan-transaction-update', $deliveryChallanData['delivery_challan_transaction']['id']), $deliveryChallanInput);

        //assert
        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Delivery Challan Transaction Updated Successfully',
            ]);
        $this->assertDatabaseHas('delivery_challan_transactions', [
            'id' => $deliveryChallanData['delivery_challan_transaction']['id'],
            'company_id' => 1,
            'challan_number' => $deliveryChallanInput['challan_number'],
            'invoice_type' => DeliveryChallanTransaction::WITH_AMOUNT,
            'round_off_method' => $roundOffMethod,
            'rounding_amount' => $roundOffMethod['correct_amount'],
        ]);
    }
});
