<?php

use App\Actions\v1\Transactions\ExpenseDebitNote\StoreExpenseDebitNoteTransactionAction;
use App\Models\ExpenseDebitNoteTransaction;
use App\Models\GstTax;
use App\Models\Master\ExpenseDebitNoteTransactionMaster;
use App\Models\Master\ItemMasterGoods;
use Carbon\Carbon;
use Database\Factories\Api\ExpenseDebitNoteTransactionFactory;

/*
|----------------------------------------------------------------------------------------------------------------------------------------------------
| Expense Debit Note Transaction Item Invoice Backend Validation Test Case
|----------------------------------------------------------------------------------------------------------------------------------------------------
*/

test('validate expense debit note transaction item invoice will return gst na incorrect', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false, 'app.is_validation_testing' => false]);

    $expenseDebitNoteInput = ExpenseDebitNoteTransactionFactory::new()->make()->toArray();
    $expenseDebitNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = $this->postJson(route('expense-debit-note-transaction-create'), $expenseDebitNoteInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Is GST NA is not correct.',
        ]);
});

test('validate expense debit note transaction item invoice will return cgst, sgst, and igst calculated is not correct', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false, 'app.is_validation_testing' => false]);

    $expenseDebitNoteInput = ExpenseDebitNoteTransactionFactory::new()->make(['is_gst_na' => true, 'is_cgst_sgst_igst_calculated' => true])->toArray();
    $expenseDebitNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = $this->postJson(route('expense-debit-note-transaction-create'), $expenseDebitNoteInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Is CGST, SGST, and IGST Calculated is not correct.',
        ]);
});

test('validate expense debit note transaction item invoice will return subtotal is not correct when pass wrong total', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $expenseDebitNoteInput = ExpenseDebitNoteTransactionFactory::new()->make([
        'is_gst_na' => true,
    ])->toArray();
    $expenseDebitNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $expenseDebitNoteInput['items'][0]['total'] = 10;

    //act
    $response = $this->postJson(route('expense-debit-note-transaction-create'), $expenseDebitNoteInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Subtotal is not correct.',
        ]);
});

test('validate expense debit note transaction item invoice will return subtotal is not correct when pass wrong total with gst na, exempt, zero in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $expenseDebitNoteInput = ExpenseDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $expenseDebitNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $expenseDebitNoteInput['items'][0]['total'] = 10;

    //act
    $response = $this->postJson(route('expense-debit-note-transaction-create'), $expenseDebitNoteInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Subtotal is not correct.',
        ]);
});

test('validate expense debit note transaction item invoice will return subtotal is not correct when pass wrong total with gst percentage', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $gst = GstTax::whereTaxRate(28.00)->first();
    $expenseDebitNoteInput = ExpenseDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $expenseDebitNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $expenseDebitNoteInput['items'][0]['gst_id'] = $gst->id;
    $expenseDebitNoteInput['items'][0]['gst_tax_percentage'] = $gst->tax_rate;

    //act
    $response = $this->postJson(route('expense-debit-note-transaction-create'), $expenseDebitNoteInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Subtotal is not correct.',
        ]);
});

test('validate expense debit note transaction item invoice will return gross value is not correct', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $expenseDebitNoteInput = ExpenseDebitNoteTransactionFactory::new()->make([
        'is_gst_na' => true,
        'gross_value' => 10,
    ])->toArray();
    $expenseDebitNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = $this->postJson(route('expense-debit-note-transaction-create'), $expenseDebitNoteInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Gross value is not correct.',
        ]);
});

test('validate expense debit note transaction item invoice will return addition charge total is not correct', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $expenseDebitNoteInput = ExpenseDebitNoteTransactionFactory::new()->make([
        'is_gst_na' => true,
    ])->toArray();
    $expenseDebitNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $expenseDebitNoteInput['additional_charges'][0]['ac_value'] = 20;

    //act
    $response = $this->postJson(route('expense-debit-note-transaction-create'), $expenseDebitNoteInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Additional charge total is not correct.',
        ]);
});

test('validate expense debit note transaction item invoice will return Taxable value is not correct when pass additional gst percentage in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $gst = GstTax::whereTaxRate(28.00)->first();
    $expenseDebitNoteInput = ExpenseDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $expenseDebitNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $expenseDebitNoteInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    foreach ($expenseDebitNoteInput['items'] as $key => $item) {
        $expenseDebitNoteInput['items'][$key]['gst_id'] = 2;
    }

    //act
    $response = $this->postJson(route('expense-debit-note-transaction-create'), $expenseDebitNoteInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Taxable value is not correct.',
        ]);
});

test('validate expense debit note transaction item invoice will return taxable value is not correct', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $expenseDebitNoteInput = ExpenseDebitNoteTransactionFactory::new()->make([
        'is_gst_na' => true,
        'taxable_value' => 10,
    ])->toArray();
    $expenseDebitNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = $this->postJson(route('expense-debit-note-transaction-create'), $expenseDebitNoteInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Taxable value is not correct.',
        ]);
});

test('validate expense debit note transaction item invoice will return cgst is not correct in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $expenseDebitNoteInput = ExpenseDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'gross_value' => 468.78,
        'taxable_value' => 478.78,
        'main_classification_nature_type' => 'Intrastate Purchase Taxable',
    ])->toArray();
    $expenseDebitNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    foreach ($expenseDebitNoteInput['items'] as $key => $item) {
        $expenseDebitNoteInput['items'][$key]['gst_id'] = $gst->id;
        $expenseDebitNoteInput['items'][$key]['total'] = 78.13;
    }

    //act
    $response = $this->postJson(route('expense-debit-note-transaction-create'), $expenseDebitNoteInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'CGST is not correct.',
        ]);
});

test('validate expense debit note transaction item invoice will return sgst is not correct in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $expenseDebitNoteInput = ExpenseDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'gross_value' => 468.78,
        'taxable_value' => 478.78,
        'main_classification_nature_type' => 'Intrastate Purchase Taxable',
        'cgst' => 65.64,
    ])->toArray();
    $expenseDebitNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    foreach ($expenseDebitNoteInput['items'] as $key => $item) {
        $expenseDebitNoteInput['items'][$key]['gst_id'] = $gst->id;
        $expenseDebitNoteInput['items'][$key]['total'] = 78.13;
    }

    //act
    $response = $this->postJson(route('expense-debit-note-transaction-create'), $expenseDebitNoteInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'SGST is not correct.',
        ]);
});

test('validate expense debit note transaction item invoice will return igst is not correct in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $expenseDebitNoteInput = ExpenseDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'main_classification_nature_type' => 'Interstate Purchase Taxable',
        'gross_value' => 468.78,
        'taxable_value' => 478.78,
    ])->toArray();
    $expenseDebitNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    foreach ($expenseDebitNoteInput['items'] as $key => $item) {
        $expenseDebitNoteInput['items'][$key]['gst_id'] = $gst->id;
        $expenseDebitNoteInput['items'][$key]['total'] = 78.13;
    }

    //act
    $response = $this->postJson(route('expense-debit-note-transaction-create'), $expenseDebitNoteInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'IGST is not correct.',
        ]);
});

test('validate expense debit note transaction item invoice will return classification nature type is empty', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(0.00)->first();

    $expenseDebitNoteInput = ExpenseDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'main_classification_nature_type' => null,
        'taxable_value' => 610,
    ])->toArray();
    $expenseDebitNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    foreach ($expenseDebitNoteInput['items'] as $key => $item) {
        $expenseDebitNoteInput['items'][$key]['gst_id'] = $gst->id;
    }

    //act
    $response = $this->postJson(route('expense-debit-note-transaction-create'), $expenseDebitNoteInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Classification Nature Type is empty.',
        ]);
});

test('validate expense debit note transaction item invoice will return add less total is not correct', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $expenseDebitNoteInput = ExpenseDebitNoteTransactionFactory::new()->make([
        'is_gst_na' => true,
        'taxable_value' => 610,
    ])->toArray();
    $expenseDebitNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $expenseDebitNoteInput['add_less'][0]['al_total'] = 100;

    //act
    $response = $this->postJson(route('expense-debit-note-transaction-create'), $expenseDebitNoteInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Add Less total is not correct.',
        ]);
});

test('validate expense debit note transaction item invoice will return round off amount is not correct', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $expenseDebitNoteInput = ExpenseDebitNoteTransactionFactory::new()->make([
        'is_gst_na' => true,
        'is_round_off_not_changed' => 1,
        'taxable_value' => 610,
        'rounding_amount' => 10.50,
    ])->toArray();
    $expenseDebitNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = $this->postJson(route('expense-debit-note-transaction-create'), $expenseDebitNoteInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Round off amount is not correct.',
        ]);
});

test('validate expense debit note transaction item invoice will return grand total is not correct', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $expenseDebitNoteInput = ExpenseDebitNoteTransactionFactory::new()->make([
        'is_gst_na' => true,
        'taxable_value' => 610,
        'grand_total' => 100,
    ])->toArray();
    $expenseDebitNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = $this->postJson(route('expense-debit-note-transaction-create'), $expenseDebitNoteInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Grand total is not correct.',
        ]);
});

/*
|----------------------------------------------------------------------------------------------------------------------------------------------------
| Expense Debit Note Transaction Account Invoice Backend Validation Test Case
|----------------------------------------------------------------------------------------------------------------------------------------------------
*/

test('validate expense debit note transaction account invoice will return gst na incorrect', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false, 'app.is_validation_testing' => false]);

    $expenseDebitNoteInput = ExpenseDebitNoteTransactionFactory::new()->make(['dn_item_type' => ExpenseDebitNoteTransaction::ACCOUNTING_INVOICE])->toArray();
    $expenseDebitNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = $this->postJson(route('expense-debit-note-transaction-create'), $expenseDebitNoteInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Is GST NA is not correct.',
        ]);
});

test('validate expense debit note transaction account invoice will return cgst, sgst, and igst calculated is not correct', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false, 'app.is_validation_testing' => false]);

    $expenseDebitNoteInput = ExpenseDebitNoteTransactionFactory::new()->make([
        'dn_item_type' => ExpenseDebitNoteTransaction::ACCOUNTING_INVOICE,
        'is_gst_na' => true,
        'is_cgst_sgst_igst_calculated' => true,
    ])->toArray();
    $expenseDebitNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = $this->postJson(route('expense-debit-note-transaction-create'), $expenseDebitNoteInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Is CGST, SGST, and IGST Calculated is not correct.',
        ]);
});

test('validate expense debit note transaction account invoice will return subtotal is not correct when pass wrong total', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $expenseDebitNoteInput = ExpenseDebitNoteTransactionFactory::new()->make([
        'dn_item_type' => ExpenseDebitNoteTransaction::ACCOUNTING_INVOICE,
        'is_gst_na' => true,
    ])->toArray();
    $expenseDebitNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $expenseDebitNoteInput['ledgers'][0]['total'] = 10;

    //act
    $response = $this->postJson(route('expense-debit-note-transaction-create'), $expenseDebitNoteInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Subtotal is not correct.',
        ]);
});

test('validate expense debit note transaction account invoice will return subtotal is not correct when pass wrong total with gst na, exempt, zero in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $expenseDebitNoteInput = ExpenseDebitNoteTransactionFactory::new()->make([
        'dn_item_type' => ExpenseDebitNoteTransaction::ACCOUNTING_INVOICE,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $expenseDebitNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $expenseDebitNoteInput['ledgers'][0]['total'] = 10;

    //act
    $response = $this->postJson(route('expense-debit-note-transaction-create'), $expenseDebitNoteInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Subtotal is not correct.',
        ]);
});

test('validate expense debit note transaction account invoice will return subtotal is not correct when pass wrong total with gst percentage', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $gst = GstTax::whereTaxRate(28.00)->first();
    $expenseDebitNoteInput = ExpenseDebitNoteTransactionFactory::new()->make([
        'dn_item_type' => ExpenseDebitNoteTransaction::ACCOUNTING_INVOICE,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $expenseDebitNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $expenseDebitNoteInput['ledgers'][0]['gst_id'] = $gst->id;
    $expenseDebitNoteInput['ledgers'][0]['with_tax'] = true;
    $expenseDebitNoteInput['ledgers'][0]['gst_tax_percentage'] = $gst->tax_rate;

    //act
    $response = $this->postJson(route('expense-debit-note-transaction-create'), $expenseDebitNoteInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Subtotal is not correct.',
        ]);
});

test('validate expense debit note transaction account invoice will return gross value is not correct', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $expenseDebitNoteInput = ExpenseDebitNoteTransactionFactory::new()->make([
        'dn_item_type' => ExpenseDebitNoteTransaction::ACCOUNTING_INVOICE,
        'is_gst_na' => true,
        'gross_value' => 10,
    ])->toArray();
    $expenseDebitNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = $this->postJson(route('expense-debit-note-transaction-create'), $expenseDebitNoteInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Gross value is not correct.',
        ]);
});

test('validate expense debit note transaction account invoice will return addition charge total is not correct', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $expenseDebitNoteInput = ExpenseDebitNoteTransactionFactory::new()->make([
        'dn_item_type' => ExpenseDebitNoteTransaction::ACCOUNTING_INVOICE,
        'is_gst_na' => true,
    ])->toArray();
    $expenseDebitNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $expenseDebitNoteInput['additional_charges'][0]['ac_value'] = 20;

    //act
    $response = $this->postJson(route('expense-debit-note-transaction-create'), $expenseDebitNoteInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Additional charge total is not correct.',
        ]);
});

test('validate expense debit note transaction account invoice will return Taxable value is not correct when pass additional gst percentage in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $gst = GstTax::whereTaxRate(28.00)->first();
    $expenseDebitNoteInput = ExpenseDebitNoteTransactionFactory::new()->make([
        'dn_item_type' => ExpenseDebitNoteTransaction::ACCOUNTING_INVOICE,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $expenseDebitNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $expenseDebitNoteInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    foreach ($expenseDebitNoteInput['ledgers'] as $key => $ledger) {
        $expenseDebitNoteInput['ledgers'][$key]['gst_id'] = 2;
    }

    //act
    $response = $this->postJson(route('expense-debit-note-transaction-create'), $expenseDebitNoteInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Taxable value is not correct.',
        ]);
});

test('validate expense debit note transaction account invoice will return taxable value is not correct', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $expenseDebitNoteInput = ExpenseDebitNoteTransactionFactory::new()->make([
        'dn_item_type' => ExpenseDebitNoteTransaction::ACCOUNTING_INVOICE,
        'is_gst_na' => true,
        'taxable_value' => 10,
    ])->toArray();
    $expenseDebitNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = $this->postJson(route('expense-debit-note-transaction-create'), $expenseDebitNoteInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Taxable value is not correct.',
        ]);
});

test('validate expense debit note transaction account invoice will return cgst is not correct in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $expenseDebitNoteInput = ExpenseDebitNoteTransactionFactory::new()->make([
        'dn_item_type' => ExpenseDebitNoteTransaction::ACCOUNTING_INVOICE,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'gross_value' => 468.78,
        'taxable_value' => 478.78,
        'main_classification_nature_type' => 'Intrastate Purchase Taxable',
    ])->toArray();
    $expenseDebitNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    foreach ($expenseDebitNoteInput['ledgers'] as $key => $ledger) {
        $expenseDebitNoteInput['ledgers'][$key]['gst_id'] = $gst->id;
        $expenseDebitNoteInput['ledgers'][$key]['total'] = 78.13;
    }

    //act
    $response = $this->postJson(route('expense-debit-note-transaction-create'), $expenseDebitNoteInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'CGST is not correct.',
        ]);
});

test('validate expense debit note transaction account invoice will return sgst is not correct in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $expenseDebitNoteInput = ExpenseDebitNoteTransactionFactory::new()->make([
        'dn_item_type' => ExpenseDebitNoteTransaction::ACCOUNTING_INVOICE,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'gross_value' => 468.78,
        'taxable_value' => 478.78,
        'main_classification_nature_type' => 'Intrastate Purchase Taxable',
        'cgst' => 65.64,
    ])->toArray();
    $expenseDebitNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    foreach ($expenseDebitNoteInput['ledgers'] as $key => $ledger) {
        $expenseDebitNoteInput['ledgers'][$key]['gst_id'] = $gst->id;
        $expenseDebitNoteInput['ledgers'][$key]['total'] = 78.13;
    }

    //act
    $response = $this->postJson(route('expense-debit-note-transaction-create'), $expenseDebitNoteInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'SGST is not correct.',
        ]);
});

test('validate expense debit note transaction account invoice will return igst is not correct in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $expenseDebitNoteInput = ExpenseDebitNoteTransactionFactory::new()->make([
        'dn_item_type' => ExpenseDebitNoteTransaction::ACCOUNTING_INVOICE,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'main_classification_nature_type' => 'Interstate Purchase Taxable',
        'gross_value' => 468.78,
        'taxable_value' => 478.78,
    ])->toArray();
    $expenseDebitNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    foreach ($expenseDebitNoteInput['ledgers'] as $key => $ledger) {
        $expenseDebitNoteInput['ledgers'][$key]['gst_id'] = $gst->id;
        $expenseDebitNoteInput['ledgers'][$key]['total'] = 78.13;
    }

    //act
    $response = $this->postJson(route('expense-debit-note-transaction-create'), $expenseDebitNoteInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'IGST is not correct.',
        ]);
});

test('validate expense debit note transaction account invoice will return classification nature type is empty', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(0.00)->first();

    $expenseDebitNoteInput = ExpenseDebitNoteTransactionFactory::new()->make([
        'dn_item_type' => ExpenseDebitNoteTransaction::ACCOUNTING_INVOICE,
        'is_gst_enabled' => true,
        'main_classification_nature_type' => null,
        'taxable_value' => 610,
    ])->toArray();
    $expenseDebitNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    foreach ($expenseDebitNoteInput['ledgers'] as $key => $ledger) {
        $expenseDebitNoteInput['ledgers'][$key]['gst_id'] = $gst->id;
    }

    //act
    $response = $this->postJson(route('expense-debit-note-transaction-create'), $expenseDebitNoteInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Classification Nature Type is empty.',
        ]);
});

test('validate expense debit note transaction account invoice will return add less total is not correct', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $expenseDebitNoteInput = ExpenseDebitNoteTransactionFactory::new()->make([
        'dn_item_type' => ExpenseDebitNoteTransaction::ACCOUNTING_INVOICE,
        'is_gst_na' => true,
        'taxable_value' => 610,
    ])->toArray();
    $expenseDebitNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $expenseDebitNoteInput['add_less'][0]['al_total'] = 100;

    //act
    $response = $this->postJson(route('expense-debit-note-transaction-create'), $expenseDebitNoteInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Add Less total is not correct.',
        ]);
});

test('validate expense debit note transaction account invoice will return round off amount is not correct', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $expenseDebitNoteInput = ExpenseDebitNoteTransactionFactory::new()->make([
        'dn_item_type' => ExpenseDebitNoteTransaction::ACCOUNTING_INVOICE,
        'is_round_off_not_changed' => 1,
        'is_gst_na' => true,
        'taxable_value' => 610,
        'rounding_amount' => 10.50,
    ])->toArray();
    $expenseDebitNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = $this->postJson(route('expense-debit-note-transaction-create'), $expenseDebitNoteInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Round off amount is not correct.',
        ]);
});

test('validate expense debit note transaction account invoice will return grand total is not correct', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $expenseDebitNoteInput = ExpenseDebitNoteTransactionFactory::new()->make([
        'dn_item_type' => ExpenseDebitNoteTransaction::ACCOUNTING_INVOICE,
        'is_gst_na' => true,
        'taxable_value' => 610,
        'grand_total' => 100,
    ])->toArray();
    $expenseDebitNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = $this->postJson(route('expense-debit-note-transaction-create'), $expenseDebitNoteInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Grand total is not correct.',
        ]);
});

/*
|----------------------------------------------------------------------------------------------------------------------------------------------------
| Store Expense Debit Note Transaction Item Invoice Backend Validation with update Test Case
|----------------------------------------------------------------------------------------------------------------------------------------------------
*/

test('validation with update decimal rpu amount when decimal place is 1 for expense debit note transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 1]);
    $expenseDebitNoteInput = ExpenseDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $expenseDebitNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $expenseDebitNoteInput['items'][0]['rpu'] = 100.********;
    $expenseDebitNoteInput['items'][0]['rpu_without_gst'] = 100.********;
    $expenseDebitNoteInput['items'][0]['rpu_with_gst'] = 100.********;
    $expenseDebitNoteInput['items'][0]['gst_id'] = $gst->id;
    $expenseDebitNoteInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('expense-debit-note-transaction-create'), $expenseDebitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense Debit Note Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('expense_debit_note_transactions', [
        'id' => $response->json('data.expense_debit_note_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $expenseDebitNoteInput['voucher_number'],
        'dn_item_type' => ExpenseDebitNoteTransaction::ITEM_INVOICE,
    ]);
    $debitNoteTransaction = ExpenseDebitNoteTransaction::with('debitNoteItems')->whereId($response->json('data.expense_debit_note_transaction.id'))->first();
    $this->assertEquals(78.2, $debitNoteTransaction->debitNoteItems->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 2 for expense debit note transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 2]);
    $expenseDebitNoteInput = ExpenseDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $expenseDebitNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $expenseDebitNoteInput['items'][0]['rpu'] = 100.********;
    $expenseDebitNoteInput['items'][0]['rpu_without_gst'] = 100.********;
    $expenseDebitNoteInput['items'][0]['rpu_with_gst'] = 100.********;
    $expenseDebitNoteInput['items'][0]['gst_id'] = $gst->id;
    $expenseDebitNoteInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('expense-debit-note-transaction-create'), $expenseDebitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense Debit Note Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('expense_debit_note_transactions', [
        'id' => $response->json('data.expense_debit_note_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $expenseDebitNoteInput['voucher_number'],
        'dn_item_type' => ExpenseDebitNoteTransaction::ITEM_INVOICE,
    ]);
    $debitNoteTransaction = ExpenseDebitNoteTransaction::with('debitNoteItems')->whereId($response->json('data.expense_debit_note_transaction.id'))->first();
    $this->assertEquals(78.22, $debitNoteTransaction->debitNoteItems->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 3 for expense debit note transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 3]);
    $expenseDebitNoteInput = ExpenseDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $expenseDebitNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $expenseDebitNoteInput['items'][0]['rpu'] = 100.********;
    $expenseDebitNoteInput['items'][0]['rpu_without_gst'] = 100.********;
    $expenseDebitNoteInput['items'][0]['rpu_with_gst'] = 100.********;
    $expenseDebitNoteInput['items'][0]['gst_id'] = $gst->id;
    $expenseDebitNoteInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('expense-debit-note-transaction-create'), $expenseDebitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense Debit Note Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('expense_debit_note_transactions', [
        'id' => $response->json('data.expense_debit_note_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $expenseDebitNoteInput['voucher_number'],
        'dn_item_type' => ExpenseDebitNoteTransaction::ITEM_INVOICE,
    ]);
    $debitNoteTransaction = ExpenseDebitNoteTransaction::with('debitNoteItems')->whereId($response->json('data.expense_debit_note_transaction.id'))->first();
    $this->assertEquals(78.220, $debitNoteTransaction->debitNoteItems->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 4 for expense debit note transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 4]);
    $expenseDebitNoteInput = ExpenseDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $expenseDebitNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $expenseDebitNoteInput['items'][0]['rpu'] = 100.********;
    $expenseDebitNoteInput['items'][0]['rpu_without_gst'] = 100.********;
    $expenseDebitNoteInput['items'][0]['rpu_with_gst'] = 100.********;
    $expenseDebitNoteInput['items'][0]['gst_id'] = $gst->id;
    $expenseDebitNoteInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('expense-debit-note-transaction-create'), $expenseDebitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense Debit Note Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('expense_debit_note_transactions', [
        'id' => $response->json('data.expense_debit_note_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $expenseDebitNoteInput['voucher_number'],
        'dn_item_type' => ExpenseDebitNoteTransaction::ITEM_INVOICE,
    ]);
    $debitNoteTransaction = ExpenseDebitNoteTransaction::with('debitNoteItems')->whereId($response->json('data.expense_debit_note_transaction.id'))->first();
    $this->assertEquals(78.2197, $debitNoteTransaction->debitNoteItems->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 5 for expense debit note transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 5]);
    $expenseDebitNoteInput = ExpenseDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $expenseDebitNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $expenseDebitNoteInput['items'][0]['rpu'] = 100.********;
    $expenseDebitNoteInput['items'][0]['rpu_without_gst'] = 100.********;
    $expenseDebitNoteInput['items'][0]['rpu_with_gst'] = 100.********;
    $expenseDebitNoteInput['items'][0]['gst_id'] = $gst->id;
    $expenseDebitNoteInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('expense-debit-note-transaction-create'), $expenseDebitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense Debit Note Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('expense_debit_note_transactions', [
        'id' => $response->json('data.expense_debit_note_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $expenseDebitNoteInput['voucher_number'],
        'dn_item_type' => ExpenseDebitNoteTransaction::ITEM_INVOICE,
    ]);
    $debitNoteTransaction = ExpenseDebitNoteTransaction::with('debitNoteItems')->whereId($response->json('data.expense_debit_note_transaction.id'))->first();
    $this->assertEquals(78.21970, $debitNoteTransaction->debitNoteItems->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 1 for expense debit note transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 1]);
    $expenseDebitNoteInput = ExpenseDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $expenseDebitNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $expenseDebitNoteInput['items'][0]['rpu'] = 100.********;
    $expenseDebitNoteInput['items'][0]['rpu_without_gst'] = 100.********;
    $expenseDebitNoteInput['items'][0]['rpu_with_gst'] = 100.********;
    $expenseDebitNoteInput['items'][0]['gst_id'] = $gst->id;
    $expenseDebitNoteInput['items'][0]['gst_tax_percentage'] = 2;
    $expenseDebitNoteInput['items'][0]['with_tax'] = false;

    //act
    $response = $this->postJson(route('expense-debit-note-transaction-create'), $expenseDebitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense Debit Note Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('expense_debit_note_transactions', [
        'id' => $response->json('data.expense_debit_note_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $expenseDebitNoteInput['voucher_number'],
        'dn_item_type' => ExpenseDebitNoteTransaction::ITEM_INVOICE,
    ]);
    $debitNoteTransaction = ExpenseDebitNoteTransaction::with('debitNoteItems')->whereId($response->json('data.expense_debit_note_transaction.id'))->first();
    $this->assertEquals(128.2, $debitNoteTransaction->debitNoteItems->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 2 for expense debit note transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 2]);
    $expenseDebitNoteInput = ExpenseDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $expenseDebitNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $expenseDebitNoteInput['items'][0]['rpu'] = 100.********;
    $expenseDebitNoteInput['items'][0]['rpu_without_gst'] = 100.********;
    $expenseDebitNoteInput['items'][0]['rpu_with_gst'] = 100.********;
    $expenseDebitNoteInput['items'][0]['gst_id'] = $gst->id;
    $expenseDebitNoteInput['items'][0]['gst_tax_percentage'] = 2;
    $expenseDebitNoteInput['items'][0]['with_tax'] = false;

    //act
    $response = $this->postJson(route('expense-debit-note-transaction-create'), $expenseDebitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense Debit Note Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('expense_debit_note_transactions', [
        'id' => $response->json('data.expense_debit_note_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $expenseDebitNoteInput['voucher_number'],
        'dn_item_type' => ExpenseDebitNoteTransaction::ITEM_INVOICE,
    ]);
    $debitNoteTransaction = ExpenseDebitNoteTransaction::with('debitNoteItems')->whereId($response->json('data.expense_debit_note_transaction.id'))->first();
    $this->assertEquals(128.16, $debitNoteTransaction->debitNoteItems->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 3 for expense debit note transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 3]);
    $expenseDebitNoteInput = ExpenseDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $expenseDebitNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $expenseDebitNoteInput['items'][0]['rpu'] = 100.********;
    $expenseDebitNoteInput['items'][0]['rpu_without_gst'] = 100.********;
    $expenseDebitNoteInput['items'][0]['rpu_with_gst'] = 100.********;
    $expenseDebitNoteInput['items'][0]['gst_id'] = $gst->id;
    $expenseDebitNoteInput['items'][0]['gst_tax_percentage'] = 2;
    $expenseDebitNoteInput['items'][0]['with_tax'] = false;

    //act
    $response = $this->postJson(route('expense-debit-note-transaction-create'), $expenseDebitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense Debit Note Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('expense_debit_note_transactions', [
        'id' => $response->json('data.expense_debit_note_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $expenseDebitNoteInput['voucher_number'],
        'dn_item_type' => ExpenseDebitNoteTransaction::ITEM_INVOICE,
    ]);
    $debitNoteTransaction = ExpenseDebitNoteTransaction::with('debitNoteItems')->whereId($response->json('data.expense_debit_note_transaction.id'))->first();
    $this->assertEquals(128.155, $debitNoteTransaction->debitNoteItems->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 4 for expense debit note transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 4]);
    $expenseDebitNoteInput = ExpenseDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $expenseDebitNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $expenseDebitNoteInput['items'][0]['rpu'] = 100.********;
    $expenseDebitNoteInput['items'][0]['rpu_without_gst'] = 100.********;
    $expenseDebitNoteInput['items'][0]['rpu_with_gst'] = 100.********;
    $expenseDebitNoteInput['items'][0]['gst_id'] = $gst->id;
    $expenseDebitNoteInput['items'][0]['gst_tax_percentage'] = 2;
    $expenseDebitNoteInput['items'][0]['with_tax'] = false;

    //act
    $response = $this->postJson(route('expense-debit-note-transaction-create'), $expenseDebitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense Debit Note Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('expense_debit_note_transactions', [
        'id' => $response->json('data.expense_debit_note_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $expenseDebitNoteInput['voucher_number'],
        'dn_item_type' => ExpenseDebitNoteTransaction::ITEM_INVOICE,
    ]);
    $debitNoteTransaction = ExpenseDebitNoteTransaction::with('debitNoteItems')->whereId($response->json('data.expense_debit_note_transaction.id'))->first();
    $this->assertEquals(128.1552, $debitNoteTransaction->debitNoteItems->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 5 for expense debit note transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 5]);
    $expenseDebitNoteInput = ExpenseDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $expenseDebitNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $expenseDebitNoteInput['items'][0]['rpu'] = 100.********;
    $expenseDebitNoteInput['items'][0]['rpu_without_gst'] = 100.********;
    $expenseDebitNoteInput['items'][0]['rpu_with_gst'] = 100.********;
    $expenseDebitNoteInput['items'][0]['gst_id'] = $gst->id;
    $expenseDebitNoteInput['items'][0]['gst_tax_percentage'] = 2;
    $expenseDebitNoteInput['items'][0]['with_tax'] = false;

    //act
    $response = $this->postJson(route('expense-debit-note-transaction-create'), $expenseDebitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense Debit Note Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('expense_debit_note_transactions', [
        'id' => $response->json('data.expense_debit_note_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $expenseDebitNoteInput['voucher_number'],
        'dn_item_type' => ExpenseDebitNoteTransaction::ITEM_INVOICE,
    ]);
    $debitNoteTransaction = ExpenseDebitNoteTransaction::with('debitNoteItems')->whereId($response->json('data.expense_debit_note_transaction.id'))->first();
    $this->assertEquals(128.15515, $debitNoteTransaction->debitNoteItems->first()->rpu_with_gst);
});

test('validation with update negative and wrong rpu without or with gst for expense debit note transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $expenseDebitNoteInput = ExpenseDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $expenseDebitNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $expenseDebitNoteInput['items'][0]['rpu_without_gst'] = -10000;
    $expenseDebitNoteInput['items'][0]['rpu_with_gst'] = -10000;
    $expenseDebitNoteInput['items'][0]['gst_id'] = $gst->id;
    $expenseDebitNoteInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('expense-debit-note-transaction-create'), $expenseDebitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense Debit Note Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('expense_debit_note_transactions', [
        'id' => $response->json('data.expense_debit_note_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $expenseDebitNoteInput['voucher_number'],
        'dn_item_type' => ExpenseDebitNoteTransaction::ITEM_INVOICE,
    ]);
    $debitNoteTransaction = ExpenseDebitNoteTransaction::with('debitNoteItems')->whereId($response->json('data.expense_debit_note_transaction.id'))->first();
    $this->assertNotEquals(100, $debitNoteTransaction->debitNoteItems->first()->rpu_without_gst);
    $this->assertEquals(100.0, $debitNoteTransaction->debitNoteItems->first()->rpu_with_gst);
});

test('validation with update negative and wrong gst tax percentage for expense debit note transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $expenseDebitNoteInput = ExpenseDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $expenseDebitNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $expenseDebitNoteInput['items'][0]['total'] = -10000;
    $expenseDebitNoteInput['items'][0]['gst_id'] = $gst->id;
    $expenseDebitNoteInput['items'][0]['gst_tax_percentage'] = $gst->tax_rate;

    //act
    $response = $this->postJson(route('expense-debit-note-transaction-create'), $expenseDebitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense Debit Note Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('expense_debit_note_transactions', [
        'id' => $response->json('data.expense_debit_note_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $expenseDebitNoteInput['voucher_number'],
        'dn_item_type' => ExpenseDebitNoteTransaction::ITEM_INVOICE,
    ]);
    $debitNoteTransaction = ExpenseDebitNoteTransaction::with('debitNoteItems')->whereId($response->json('data.expense_debit_note_transaction.id'))->first();
    $this->assertEquals(28, intval($debitNoteTransaction->debitNoteItems->first()->gst_tax_percentage));
});

test('validation with update negative and wrong total discount for expense debit note transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(5.00)->first();

    $expenseDebitNoteInput = ExpenseDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $expenseDebitNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    foreach ($expenseDebitNoteInput['items'] as $key => $item) {
        $expenseDebitNoteInput['items'][$key]['gst_id'] = $gst->id;
        $expenseDebitNoteInput['items'][$key]['discount_type'] = 2;
        $expenseDebitNoteInput['items'][$key]['discount_value'] = 10;
        $expenseDebitNoteInput['items'][$key]['discount_type_2'] = 2;
        $expenseDebitNoteInput['items'][$key]['discount_value_2'] = 10;
        $expenseDebitNoteInput['items'][$key]['total_discount_amount'] = 10;
    }

    //act
    $response = $this->postJson(route('expense-debit-note-transaction-create'), $expenseDebitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense Debit Note Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('expense_debit_note_transactions', [
        'id' => $response->json('data.expense_debit_note_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $expenseDebitNoteInput['voucher_number'],
        'dn_item_type' => ExpenseDebitNoteTransaction::ITEM_INVOICE,
    ]);
    $debitNoteTransaction = ExpenseDebitNoteTransaction::with('debitNoteItems')->whereId($response->json('data.expense_debit_note_transaction.id'))->first();
    foreach ($debitNoteTransaction->debitNoteItems as $item) {
        $this->assertEquals(18.1, $item->total_discount_amount);
    }
});

test('validation with update negative and wrong sub total for expense debit note transaction item invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $expenseDebitNoteInput = ExpenseDebitNoteTransactionFactory::new()->make()->toArray();
    $expenseDebitNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $expenseDebitNoteInput['items'][0]['total'] = -10000;

    //act
    $response = $this->postJson(route('expense-debit-note-transaction-create'), $expenseDebitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense Debit Note Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('expense_debit_note_transactions', [
        'id' => $response->json('data.expense_debit_note_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $expenseDebitNoteInput['voucher_number'],
        'dn_item_type' => ExpenseDebitNoteTransaction::ITEM_INVOICE,
    ]);
    $debitNoteTransaction = ExpenseDebitNoteTransaction::with('debitNoteItems')->whereId($response->json('data.expense_debit_note_transaction.id'))->first();
    $this->assertEquals(100, intval($debitNoteTransaction->debitNoteItems->first()->total));
});

test('validation with update negative and wrong total for expense debit note transaction item invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $expenseDebitNoteInput = ExpenseDebitNoteTransactionFactory::new()->make(['total' => -620])->toArray();
    $expenseDebitNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = $this->postJson(route('expense-debit-note-transaction-create'), $expenseDebitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense Debit Note Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('expense_debit_note_transactions', [
        'id' => $response->json('data.expense_debit_note_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $expenseDebitNoteInput['voucher_number'],
        'dn_item_type' => ExpenseDebitNoteTransaction::ITEM_INVOICE,
        'total' => 620.00,
    ]);
});

test('validation with update negative and wrong gross value for expense debit note transaction item invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $expenseDebitNoteInput = ExpenseDebitNoteTransactionFactory::new()->make(['gross_value' => -100])->toArray();
    $expenseDebitNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = $this->postJson(route('expense-debit-note-transaction-create'), $expenseDebitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense Debit Note Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('expense_debit_note_transactions', [
        'id' => $response->json('data.expense_debit_note_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $expenseDebitNoteInput['voucher_number'],
        'dn_item_type' => ExpenseDebitNoteTransaction::ITEM_INVOICE,
        'gross_value' => 600.00,
    ]);
});

test('validation with update negative and wrong additional charges gst tax percentage for expense debit note transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $expenseDebitNoteInput = ExpenseDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $expenseDebitNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $expenseDebitNoteInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    $expenseDebitNoteInput['additional_charges'][0]['ac_total'] = -2000;

    //act
    $response = $this->postJson(route('expense-debit-note-transaction-create'), $expenseDebitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense Debit Note Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('expense_debit_note_transactions', [
        'id' => $response->json('data.expense_debit_note_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $expenseDebitNoteInput['voucher_number'],
        'dn_item_type' => ExpenseDebitNoteTransaction::ITEM_INVOICE,
    ]);
    $debitNoteTransaction = ExpenseDebitNoteTransaction::with('additionalCharges')->whereId($response->json('data.expense_debit_note_transaction.id'))->first();
    $this->assertNotEquals(10.00, $debitNoteTransaction->additionalCharges->first()->total);
});

test('validation with update negative and wrong additional charges total for expense debit note transaction item invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $expenseDebitNoteInput = ExpenseDebitNoteTransactionFactory::new()->make()->toArray();
    $expenseDebitNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $expenseDebitNoteInput['additional_charges'][0]['ac_total'] = -200;

    //act
    $response = $this->postJson(route('expense-debit-note-transaction-create'), $expenseDebitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense Debit Note Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('expense_debit_note_transactions', [
        'id' => $response->json('data.expense_debit_note_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $expenseDebitNoteInput['voucher_number'],
        'dn_item_type' => ExpenseDebitNoteTransaction::ITEM_INVOICE,
        'gross_value' => 600.00,
    ]);
    $debitNoteTransaction = ExpenseDebitNoteTransaction::with('additionalCharges')->whereId($response->json('data.expense_debit_note_transaction.id'))->first();
    $this->assertEquals(10, intval($debitNoteTransaction->additionalCharges->first()->total));
});

test('validation with update negative and wrong cgst and sgst when intrastate purchase taxable classification for expense debit note transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $expenseDebitNoteInput = ExpenseDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'main_classification_nature_type' => 'Intrastate Purchase Taxable',
    ])->toArray();
    $expenseDebitNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $expenseDebitNoteInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    foreach ($expenseDebitNoteInput['items'] as $key => $item) {
        $expenseDebitNoteInput['items'][$key]['gst_id'] = $gst->id;
    }

    //act
    $response = $this->postJson(route('expense-debit-note-transaction-create'), $expenseDebitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense Debit Note Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('expense_debit_note_transactions', [
        'id' => $response->json('data.expense_debit_note_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $expenseDebitNoteInput['voucher_number'],
        'dn_item_type' => ExpenseDebitNoteTransaction::ITEM_INVOICE,
    ]);
    $debitNoteTransaction = ExpenseDebitNoteTransaction::whereId($response->json('data.expense_debit_note_transaction.id'))->first();
    $this->assertEquals(67.04, $debitNoteTransaction->cgst);
    $this->assertEquals(67.04, $debitNoteTransaction->sgst);
});

test('validation with update negative and wrong igst when purchase inter state taxable classification for expense debit note transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $expenseDebitNoteInput = ExpenseDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'main_classification_nature_type' => 'Interstate Purchase Taxable',
    ])->toArray();
    $expenseDebitNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $expenseDebitNoteInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    foreach ($expenseDebitNoteInput['items'] as $key => $item) {
        $expenseDebitNoteInput['items'][$key]['gst_id'] = $gst->id;
    }

    //act
    $response = $this->postJson(route('expense-debit-note-transaction-create'), $expenseDebitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense Debit Note Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('expense_debit_note_transactions', [
        'id' => $response->json('data.expense_debit_note_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $expenseDebitNoteInput['voucher_number'],
        'dn_item_type' => ExpenseDebitNoteTransaction::ITEM_INVOICE,
    ]);
    $debitNoteTransaction = ExpenseDebitNoteTransaction::whereId($response->json('data.expense_debit_note_transaction.id'))->first();
    $this->assertEquals(134.08, $debitNoteTransaction->igst);
});

test('validation with update negative and wrong taxable value for expense debit note transaction item invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $expenseDebitNoteInput = ExpenseDebitNoteTransactionFactory::new()->make(['taxable_value' => -100])->toArray();
    $expenseDebitNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = $this->postJson(route('expense-debit-note-transaction-create'), $expenseDebitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense Debit Note Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('expense_debit_note_transactions', [
        'id' => $response->json('data.expense_debit_note_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $expenseDebitNoteInput['voucher_number'],
        'dn_item_type' => ExpenseDebitNoteTransaction::ITEM_INVOICE,
        'taxable_value' => 610.00,
    ]);
});

test('validation with update negative and wrong addless total for expense debit note transaction item invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $expenseDebitNoteInput = ExpenseDebitNoteTransactionFactory::new()->make()->toArray();
    $expenseDebitNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $expenseDebitNoteInput['add_less'][0]['al_total'] = -100;

    //act
    $response = $this->postJson(route('expense-debit-note-transaction-create'), $expenseDebitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense Debit Note Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('expense_debit_note_transactions', [
        'id' => $response->json('data.expense_debit_note_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $expenseDebitNoteInput['voucher_number'],
        'dn_item_type' => ExpenseDebitNoteTransaction::ITEM_INVOICE,
        'gross_value' => 600.00,
    ]);
    $debitNoteTransaction = ExpenseDebitNoteTransaction::with('addLess')->whereId($response->json('data.expense_debit_note_transaction.id'))->first();
    $this->assertEquals(10, intval($debitNoteTransaction->addLess->first()->total));
});

test('validation with update negative and wrong grand total for expense debit note transaction item invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $expenseDebitNoteInput = ExpenseDebitNoteTransactionFactory::new()->make(['grand_total' => -100])->toArray();
    $expenseDebitNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = $this->postJson(route('expense-debit-note-transaction-create'), $expenseDebitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense Debit Note Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('expense_debit_note_transactions', [
        'id' => $response->json('data.expense_debit_note_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $expenseDebitNoteInput['voucher_number'],
        'dn_item_type' => ExpenseDebitNoteTransaction::ITEM_INVOICE,
        'grand_total' => 620.00,
    ]);
});

/*
|----------------------------------------------------------------------------------------------------------------------------------------------------
| Store Expense Debit Note Transaction Account Invoice Backend Validation with update Test Case
|----------------------------------------------------------------------------------------------------------------------------------------------------
*/

test('validation with update decimal rpu amount when decimal place is 1 for expense debit note transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $expenseDebitNoteInput = ExpenseDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'dn_item_type' => ExpenseDebitNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $expenseDebitNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $expenseDebitNoteInput['ledgers'][0]['decimal'] = 1;
    $expenseDebitNoteInput['ledgers'][0]['rpu'] = 100.********;
    $expenseDebitNoteInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $expenseDebitNoteInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $expenseDebitNoteInput['ledgers'][0]['gst_id'] = $gst->id;
    $expenseDebitNoteInput['ledgers'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('expense-debit-note-transaction-create'), $expenseDebitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense Debit Note Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('expense_debit_note_transactions', [
        'id' => $response->json('data.expense_debit_note_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $expenseDebitNoteInput['voucher_number'],
        'dn_item_type' => ExpenseDebitNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $debitNoteTransaction = ExpenseDebitNoteTransaction::with('debitNoteLedgers')->whereId($response->json('data.expense_debit_note_transaction.id'))->first();
    $this->assertEquals(78.2, $debitNoteTransaction->debitNoteLedgers->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 2 for expense debit note transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $expenseDebitNoteInput = ExpenseDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'dn_item_type' => ExpenseDebitNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $expenseDebitNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $expenseDebitNoteInput['ledgers'][0]['decimal'] = 2;
    $expenseDebitNoteInput['ledgers'][0]['rpu'] = 100.********;
    $expenseDebitNoteInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $expenseDebitNoteInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $expenseDebitNoteInput['ledgers'][0]['gst_id'] = $gst->id;
    $expenseDebitNoteInput['ledgers'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('expense-debit-note-transaction-create'), $expenseDebitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense Debit Note Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('expense_debit_note_transactions', [
        'id' => $response->json('data.expense_debit_note_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $expenseDebitNoteInput['voucher_number'],
        'dn_item_type' => ExpenseDebitNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $debitNoteTransaction = ExpenseDebitNoteTransaction::with('debitNoteLedgers')->whereId($response->json('data.expense_debit_note_transaction.id'))->first();
    $this->assertEquals(78.22, $debitNoteTransaction->debitNoteLedgers->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 3 for expense debit note transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $expenseDebitNoteInput = ExpenseDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'dn_item_type' => ExpenseDebitNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $expenseDebitNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $expenseDebitNoteInput['ledgers'][0]['decimal'] = 3;
    $expenseDebitNoteInput['ledgers'][0]['rpu'] = 100.********;
    $expenseDebitNoteInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $expenseDebitNoteInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $expenseDebitNoteInput['ledgers'][0]['gst_id'] = $gst->id;
    $expenseDebitNoteInput['ledgers'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('expense-debit-note-transaction-create'), $expenseDebitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense Debit Note Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('expense_debit_note_transactions', [
        'id' => $response->json('data.expense_debit_note_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $expenseDebitNoteInput['voucher_number'],
        'dn_item_type' => ExpenseDebitNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $debitNoteTransaction = ExpenseDebitNoteTransaction::with('debitNoteLedgers')->whereId($response->json('data.expense_debit_note_transaction.id'))->first();
    $this->assertEquals(78.220, $debitNoteTransaction->debitNoteLedgers->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 4 for expense debit note transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $expenseDebitNoteInput = ExpenseDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'dn_item_type' => ExpenseDebitNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $expenseDebitNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $expenseDebitNoteInput['ledgers'][0]['decimal'] = 4;
    $expenseDebitNoteInput['ledgers'][0]['rpu'] = 100.********;
    $expenseDebitNoteInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $expenseDebitNoteInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $expenseDebitNoteInput['ledgers'][0]['gst_id'] = $gst->id;
    $expenseDebitNoteInput['ledgers'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('expense-debit-note-transaction-create'), $expenseDebitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense Debit Note Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('expense_debit_note_transactions', [
        'id' => $response->json('data.expense_debit_note_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $expenseDebitNoteInput['voucher_number'],
        'dn_item_type' => ExpenseDebitNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $debitNoteTransaction = ExpenseDebitNoteTransaction::with('debitNoteLedgers')->whereId($response->json('data.expense_debit_note_transaction.id'))->first();
    $this->assertEquals(78.2197, $debitNoteTransaction->debitNoteLedgers->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 5 for expense debit note transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $expenseDebitNoteInput = ExpenseDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'dn_item_type' => ExpenseDebitNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $expenseDebitNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $expenseDebitNoteInput['ledgers'][0]['decimal'] = 5;
    $expenseDebitNoteInput['ledgers'][0]['rpu'] = 100.********;
    $expenseDebitNoteInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $expenseDebitNoteInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $expenseDebitNoteInput['ledgers'][0]['gst_id'] = $gst->id;
    $expenseDebitNoteInput['ledgers'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('expense-debit-note-transaction-create'), $expenseDebitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense Debit Note Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('expense_debit_note_transactions', [
        'id' => $response->json('data.expense_debit_note_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $expenseDebitNoteInput['voucher_number'],
        'dn_item_type' => ExpenseDebitNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $debitNoteTransaction = ExpenseDebitNoteTransaction::with('debitNoteLedgers')->whereId($response->json('data.expense_debit_note_transaction.id'))->first();
    $this->assertEquals(78.21970, $debitNoteTransaction->debitNoteLedgers->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 1 for expense debit note transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $expenseDebitNoteInput = ExpenseDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'dn_item_type' => ExpenseDebitNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $expenseDebitNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $expenseDebitNoteInput['ledgers'][0]['decimal'] = 1;
    $expenseDebitNoteInput['ledgers'][0]['rpu'] = 100.********;
    $expenseDebitNoteInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $expenseDebitNoteInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $expenseDebitNoteInput['ledgers'][0]['gst_id'] = $gst->id;
    $expenseDebitNoteInput['ledgers'][0]['gst_tax_percentage'] = 2;
    $expenseDebitNoteInput['ledgers'][0]['with_tax'] = false;

    //act
    $response = $this->postJson(route('expense-debit-note-transaction-create'), $expenseDebitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense Debit Note Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('expense_debit_note_transactions', [
        'id' => $response->json('data.expense_debit_note_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $expenseDebitNoteInput['voucher_number'],
        'dn_item_type' => ExpenseDebitNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $debitNoteTransaction = ExpenseDebitNoteTransaction::with('debitNoteLedgers')->whereId($response->json('data.expense_debit_note_transaction.id'))->first();
    $this->assertEquals(128.2, $debitNoteTransaction->debitNoteLedgers->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 2 for expense debit note transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $expenseDebitNoteInput = ExpenseDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'dn_item_type' => ExpenseDebitNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $expenseDebitNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $expenseDebitNoteInput['ledgers'][0]['decimal'] = 2;
    $expenseDebitNoteInput['ledgers'][0]['rpu'] = 100.********;
    $expenseDebitNoteInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $expenseDebitNoteInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $expenseDebitNoteInput['ledgers'][0]['gst_id'] = $gst->id;
    $expenseDebitNoteInput['ledgers'][0]['gst_tax_percentage'] = 2;
    $expenseDebitNoteInput['ledgers'][0]['with_tax'] = false;

    //act
    $response = $this->postJson(route('expense-debit-note-transaction-create'), $expenseDebitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense Debit Note Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('expense_debit_note_transactions', [
        'id' => $response->json('data.expense_debit_note_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $expenseDebitNoteInput['voucher_number'],
        'dn_item_type' => ExpenseDebitNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $debitNoteTransaction = ExpenseDebitNoteTransaction::with('debitNoteLedgers')->whereId($response->json('data.expense_debit_note_transaction.id'))->first();
    $this->assertEquals(128.16, $debitNoteTransaction->debitNoteLedgers->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 3 for expense debit note transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $expenseDebitNoteInput = ExpenseDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'dn_item_type' => ExpenseDebitNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $expenseDebitNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $expenseDebitNoteInput['ledgers'][0]['decimal'] = 3;
    $expenseDebitNoteInput['ledgers'][0]['rpu'] = 100.********;
    $expenseDebitNoteInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $expenseDebitNoteInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $expenseDebitNoteInput['ledgers'][0]['gst_id'] = $gst->id;
    $expenseDebitNoteInput['ledgers'][0]['gst_tax_percentage'] = 2;
    $expenseDebitNoteInput['ledgers'][0]['with_tax'] = false;

    //act
    $response = $this->postJson(route('expense-debit-note-transaction-create'), $expenseDebitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense Debit Note Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('expense_debit_note_transactions', [
        'id' => $response->json('data.expense_debit_note_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $expenseDebitNoteInput['voucher_number'],
        'dn_item_type' => ExpenseDebitNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $debitNoteTransaction = ExpenseDebitNoteTransaction::with('debitNoteLedgers')->whereId($response->json('data.expense_debit_note_transaction.id'))->first();
    $this->assertEquals(128.155, $debitNoteTransaction->debitNoteLedgers->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 4 for expense debit note transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $expenseDebitNoteInput = ExpenseDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'dn_item_type' => ExpenseDebitNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $expenseDebitNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $expenseDebitNoteInput['ledgers'][0]['decimal'] = 4;
    $expenseDebitNoteInput['ledgers'][0]['rpu'] = 100.********;
    $expenseDebitNoteInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $expenseDebitNoteInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $expenseDebitNoteInput['ledgers'][0]['gst_id'] = $gst->id;
    $expenseDebitNoteInput['ledgers'][0]['gst_tax_percentage'] = 2;
    $expenseDebitNoteInput['ledgers'][0]['with_tax'] = false;

    //act
    $response = $this->postJson(route('expense-debit-note-transaction-create'), $expenseDebitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense Debit Note Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('expense_debit_note_transactions', [
        'id' => $response->json('data.expense_debit_note_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $expenseDebitNoteInput['voucher_number'],
        'dn_item_type' => ExpenseDebitNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $debitNoteTransaction = ExpenseDebitNoteTransaction::with('debitNoteLedgers')->whereId($response->json('data.expense_debit_note_transaction.id'))->first();
    $this->assertEquals(128.1552, $debitNoteTransaction->debitNoteLedgers->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 5 for expense debit note transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $expenseDebitNoteInput = ExpenseDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'dn_item_type' => ExpenseDebitNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $expenseDebitNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $expenseDebitNoteInput['ledgers'][0]['decimal'] = 5;
    $expenseDebitNoteInput['ledgers'][0]['rpu'] = 100.********;
    $expenseDebitNoteInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $expenseDebitNoteInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $expenseDebitNoteInput['ledgers'][0]['gst_id'] = $gst->id;
    $expenseDebitNoteInput['ledgers'][0]['gst_tax_percentage'] = 2;
    $expenseDebitNoteInput['ledgers'][0]['with_tax'] = false;

    //act
    $response = $this->postJson(route('expense-debit-note-transaction-create'), $expenseDebitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense Debit Note Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('expense_debit_note_transactions', [
        'id' => $response->json('data.expense_debit_note_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $expenseDebitNoteInput['voucher_number'],
        'dn_item_type' => ExpenseDebitNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $debitNoteTransaction = ExpenseDebitNoteTransaction::with('debitNoteLedgers')->whereId($response->json('data.expense_debit_note_transaction.id'))->first();
    $this->assertEquals(128.15515, $debitNoteTransaction->debitNoteLedgers->first()->rpu_with_gst);
});

test('validation with update negative and wrong rpu without or with gst for expense debit note transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $expenseDebitNoteInput = ExpenseDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'dn_item_type' => ExpenseDebitNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $expenseDebitNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $expenseDebitNoteInput['ledgers'][0]['rpu_without_gst'] = -10000;
    $expenseDebitNoteInput['ledgers'][0]['rpu_with_gst'] = -10000;
    $expenseDebitNoteInput['ledgers'][0]['gst_id'] = $gst->id;
    $expenseDebitNoteInput['ledgers'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('expense-debit-note-transaction-create'), $expenseDebitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense Debit Note Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('expense_debit_note_transactions', [
        'id' => $response->json('data.expense_debit_note_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $expenseDebitNoteInput['voucher_number'],
        'dn_item_type' => ExpenseDebitNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $debitNoteTransaction = ExpenseDebitNoteTransaction::with('debitNoteLedgers')->whereId($response->json('data.expense_debit_note_transaction.id'))->first();
    $this->assertNotEquals(100, $debitNoteTransaction->debitNoteLedgers->first()->rpu_without_gst);
    $this->assertEquals(100.0, $debitNoteTransaction->debitNoteLedgers->first()->rpu_with_gst);
});

test('validation with update negative and wrong gst tax percentage for expense debit note transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $expenseDebitNoteInput = ExpenseDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'dn_item_type' => ExpenseDebitNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $expenseDebitNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $expenseDebitNoteInput['ledgers'][0]['total'] = -10000;
    $expenseDebitNoteInput['ledgers'][0]['gst_id'] = $gst->id;
    $expenseDebitNoteInput['ledgers'][0]['gst_tax_percentage'] = $gst->tax_rate;

    //act
    $response = $this->postJson(route('expense-debit-note-transaction-create'), $expenseDebitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense Debit Note Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('expense_debit_note_transactions', [
        'id' => $response->json('data.expense_debit_note_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $expenseDebitNoteInput['voucher_number'],
        'dn_item_type' => ExpenseDebitNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $debitNoteTransaction = ExpenseDebitNoteTransaction::with('debitNoteLedgers')->whereId($response->json('data.expense_debit_note_transaction.id'))->first();
    $this->assertEquals(28, intval($debitNoteTransaction->debitNoteLedgers->first()->gst_tax_percentage));
});

test('validation with update negative and wrong total discount for expense debit note transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(5.00)->first();

    $expenseDebitNoteInput = ExpenseDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'dn_item_type' => ExpenseDebitNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $expenseDebitNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    foreach ($expenseDebitNoteInput['ledgers'] as $key => $ledger) {
        $expenseDebitNoteInput['ledgers'][$key]['gst_id'] = $gst->id;
        $expenseDebitNoteInput['ledgers'][$key]['discount_type'] = 2;
        $expenseDebitNoteInput['ledgers'][$key]['discount_value'] = 10;
        $expenseDebitNoteInput['ledgers'][$key]['discount_type_2'] = 2;
        $expenseDebitNoteInput['ledgers'][$key]['discount_value_2'] = 10;
        $expenseDebitNoteInput['ledgers'][$key]['total_discount_amount'] = 10;
    }

    //act
    $response = $this->postJson(route('expense-debit-note-transaction-create'), $expenseDebitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense Debit Note Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('expense_debit_note_transactions', [
        'id' => $response->json('data.expense_debit_note_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $expenseDebitNoteInput['voucher_number'],
        'dn_item_type' => ExpenseDebitNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $debitNoteTransaction = ExpenseDebitNoteTransaction::with('debitNoteLedgers')->whereId($response->json('data.expense_debit_note_transaction.id'))->first();
    foreach ($debitNoteTransaction->debitNoteLedgers as $ledger) {
        $this->assertEquals(18.1, $ledger->total_discount_amount);
    }
});

test('validation with update negative and wrong sub total for expense debit note transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $expenseDebitNoteInput = ExpenseDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'dn_item_type' => ExpenseDebitNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $expenseDebitNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $expenseDebitNoteInput['ledgers'][0]['total'] = -10000;
    $expenseDebitNoteInput['ledgers'][0]['gst_id'] = $gst->id;
    $expenseDebitNoteInput['ledgers'][0]['gst_tax_percentage'] = $gst->tax_rate;

    //act
    $response = $this->postJson(route('expense-debit-note-transaction-create'), $expenseDebitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense Debit Note Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('expense_debit_note_transactions', [
        'id' => $response->json('data.expense_debit_note_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $expenseDebitNoteInput['voucher_number'],
        'dn_item_type' => ExpenseDebitNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $debitNoteTransaction = ExpenseDebitNoteTransaction::with('debitNoteLedgers')->whereId($response->json('data.expense_debit_note_transaction.id'))->first();
    $this->assertEquals(78.13, $debitNoteTransaction->debitNoteLedgers->first()->total);
});

test('validation with update negative and wrong sub total for expense debit note transaction account invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $expenseDebitNoteInput = ExpenseDebitNoteTransactionFactory::new()->make(['dn_item_type' => ExpenseDebitNoteTransaction::ACCOUNTING_INVOICE])->toArray();
    $expenseDebitNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $expenseDebitNoteInput['ledgers'][0]['total'] = -10000;

    //act
    $response = $this->postJson(route('expense-debit-note-transaction-create'), $expenseDebitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense Debit Note Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('expense_debit_note_transactions', [
        'id' => $response->json('data.expense_debit_note_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $expenseDebitNoteInput['voucher_number'],
        'dn_item_type' => ExpenseDebitNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $debitNoteTransaction = ExpenseDebitNoteTransaction::with('debitNoteLedgers')->whereId($response->json('data.expense_debit_note_transaction.id'))->first();
    $this->assertEquals(100, intval($debitNoteTransaction->debitNoteLedgers->first()->total));
});

test('validation with update negative and wrong total for expense debit note transaction account invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $debitNoteTransaction = ExpenseDebitNoteTransactionFactory::new()->make([
        'total' => -620,
        'dn_item_type' => ExpenseDebitNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $debitNoteTransaction['voucher_date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = $this->postJson(route('expense-debit-note-transaction-create'), $debitNoteTransaction);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense Debit Note Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('expense_debit_note_transactions', [
        'id' => $response->json('data.expense_debit_note_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $debitNoteTransaction['voucher_number'],
        'dn_item_type' => ExpenseDebitNoteTransaction::ACCOUNTING_INVOICE,
        'total' => 620.00,
    ]);
});

test('validation with update negative and wrong gross value for expense debit note transaction account invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $expenseDebitNoteInput = ExpenseDebitNoteTransactionFactory::new()->make([
        'gross_value' => -100,
        'dn_item_type' => ExpenseDebitNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $expenseDebitNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = $this->postJson(route('expense-debit-note-transaction-create'), $expenseDebitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense Debit Note Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('expense_debit_note_transactions', [
        'id' => $response->json('data.expense_debit_note_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $expenseDebitNoteInput['voucher_number'],
        'dn_item_type' => ExpenseDebitNoteTransaction::ACCOUNTING_INVOICE,
        'gross_value' => 600.00,
    ]);
});

test('validation with update negative and wrong additional charges gst tax percentage for expense debit note transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $expenseDebitNoteInput = ExpenseDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'dn_item_type' => ExpenseDebitNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $expenseDebitNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $expenseDebitNoteInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    $expenseDebitNoteInput['additional_charges'][0]['ac_total'] = -2000;

    //act
    $response = $this->postJson(route('expense-debit-note-transaction-create'), $expenseDebitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense Debit Note Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('expense_debit_note_transactions', [
        'id' => $response->json('data.expense_debit_note_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $expenseDebitNoteInput['voucher_number'],
        'dn_item_type' => ExpenseDebitNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $debitNoteTransaction = ExpenseDebitNoteTransaction::with('additionalCharges')->whereId($response->json('data.expense_debit_note_transaction.id'))->first();
    $this->assertNotEquals(10.00, $debitNoteTransaction->additionalCharges->first()->total);
});

test('validation with update negative and wrong additional charges total for expense debit note transaction account invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $expenseDebitNoteInput = ExpenseDebitNoteTransactionFactory::new()->make(['dn_item_type' => ExpenseDebitNoteTransaction::ACCOUNTING_INVOICE])->toArray();
    $expenseDebitNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $expenseDebitNoteInput['additional_charges'][0]['ac_total'] = -200;

    //act
    $response = $this->postJson(route('expense-debit-note-transaction-create'), $expenseDebitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense Debit Note Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('expense_debit_note_transactions', [
        'id' => $response->json('data.expense_debit_note_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $expenseDebitNoteInput['voucher_number'],
        'dn_item_type' => ExpenseDebitNoteTransaction::ACCOUNTING_INVOICE,
        'gross_value' => 600.00,
    ]);
    $debitNoteTransaction = ExpenseDebitNoteTransaction::with('additionalCharges')->whereId($response->json('data.expense_debit_note_transaction.id'))->first();
    $this->assertEquals(10, intval($debitNoteTransaction->additionalCharges->first()->total));
});

test('validation with update negative and wrong cgst and sgst when purchase intra state taxable classification for expense debit note transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $expenseDebitNoteInput = ExpenseDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'main_classification_nature_type' => 'Intrastate Purchase Taxable',
        'dn_item_type' => ExpenseDebitNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $expenseDebitNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $expenseDebitNoteInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    foreach ($expenseDebitNoteInput['ledgers'] as $key => $ledger) {
        $expenseDebitNoteInput['ledgers'][$key]['gst_id'] = $gst->id;
    }

    //act
    $response = $this->postJson(route('expense-debit-note-transaction-create'), $expenseDebitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense Debit Note Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('expense_debit_note_transactions', [
        'id' => $response->json('data.expense_debit_note_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $expenseDebitNoteInput['voucher_number'],
        'dn_item_type' => ExpenseDebitNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $debitNoteTransaction = ExpenseDebitNoteTransaction::whereId($response->json('data.expense_debit_note_transaction.id'))->first();
    $this->assertEquals(67.04, $debitNoteTransaction->cgst);
    $this->assertEquals(67.04, $debitNoteTransaction->sgst);
});

test('validation with update negative and wrong igst when purchase inter state taxable classification for expense debit note transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $expenseDebitNoteInput = ExpenseDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'main_classification_nature_type' => 'Interstate Purchase Taxable',
        'dn_item_type' => ExpenseDebitNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $expenseDebitNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $expenseDebitNoteInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    foreach ($expenseDebitNoteInput['ledgers'] as $key => $ledger) {
        $expenseDebitNoteInput['ledgers'][$key]['gst_id'] = $gst->id;
    }

    //act
    $response = $this->postJson(route('expense-debit-note-transaction-create'), $expenseDebitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense Debit Note Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('expense_debit_note_transactions', [
        'id' => $response->json('data.expense_debit_note_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $expenseDebitNoteInput['voucher_number'],
        'dn_item_type' => ExpenseDebitNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $debitNoteTransaction = ExpenseDebitNoteTransaction::whereId($response->json('data.expense_debit_note_transaction.id'))->first();
    $this->assertEquals(134.08, $debitNoteTransaction->igst);
});

test('validation with update negative and wrong taxable value for expense debit note transaction account invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $expenseDebitNoteInput = ExpenseDebitNoteTransactionFactory::new()->make([
        'taxable_value' => -100,
        'dn_item_type' => ExpenseDebitNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $expenseDebitNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = $this->postJson(route('expense-debit-note-transaction-create'), $expenseDebitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense Debit Note Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('expense_debit_note_transactions', [
        'id' => $response->json('data.expense_debit_note_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $expenseDebitNoteInput['voucher_number'],
        'dn_item_type' => ExpenseDebitNoteTransaction::ACCOUNTING_INVOICE,
        'taxable_value' => 610.00,
    ]);
});

test('validation with update negative and wrong addless total for expense debit note transaction account invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $expenseDebitNoteInput = ExpenseDebitNoteTransactionFactory::new()->make([
        'dn_item_type' => ExpenseDebitNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $expenseDebitNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $expenseDebitNoteInput['add_less'][0]['al_total'] = -100;

    //act
    $response = $this->postJson(route('expense-debit-note-transaction-create'), $expenseDebitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense Debit Note Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('expense_debit_note_transactions', [
        'id' => $response->json('data.expense_debit_note_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $expenseDebitNoteInput['voucher_number'],
        'dn_item_type' => ExpenseDebitNoteTransaction::ACCOUNTING_INVOICE,
        'gross_value' => 600.00,
    ]);
    $debitNoteTransaction = ExpenseDebitNoteTransaction::with('addLess')->whereId($response->json('data.expense_debit_note_transaction.id'))->first();
    $this->assertEquals(10, intval($debitNoteTransaction->addLess->first()->total));
});

test('validation with update negative and wrong grand total for expense debit note transaction account invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $expenseDebitNoteInput = ExpenseDebitNoteTransactionFactory::new()->make([
        'grand_total' => -100,
        'dn_item_type' => ExpenseDebitNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $expenseDebitNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = $this->postJson(route('expense-debit-note-transaction-create'), $expenseDebitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense Debit Note Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('expense_debit_note_transactions', [
        'id' => $response->json('data.expense_debit_note_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $expenseDebitNoteInput['voucher_number'],
        'dn_item_type' => ExpenseDebitNoteTransaction::ACCOUNTING_INVOICE,
        'grand_total' => 620.00,
    ]);
});

test('validation with update negative and wrong round off amount and method for expense debit note transaction', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);
    $expenseDebitNoteInput = ExpenseDebitNoteTransactionFactory::new()->make()->toArray();
    $expenseDebitNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $expenseDebitNoteInput['additional_charges'][0]['ac_value'] = 10.54;

    $roundOffMethods = [
        ['method' => 1, 'correct_amount' => 0],
        ['method' => 2, 'correct_amount' => -0.54],
        ['method' => 3, 'correct_amount' => 0.46],
        ['method' => 4, 'correct_amount' => 0.46],
    ];
    foreach ($roundOffMethods as $roundOffMethod) {
        $expenseDebitNoteInput['voucher_number'] = 'V-'.$roundOffMethod['method'];
        $expenseDebitNoteInput['round_off_method'] = 0;
        ExpenseDebitNoteTransactionMaster::whereCompanyId(1)->update(['round_off_method' => $roundOffMethod['method']]);
        $expenseDebitNoteInput['rounding_amount'] = -10.50;

        //act
        $response = $this->postJson(route('expense-debit-note-transaction-create'), $expenseDebitNoteInput);

        //assert
        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Expense Debit Note Transaction Created Successfully.',
            ]);
        $this->assertDatabaseHas('expense_debit_note_transactions', [
            'id' => $response->json('data.expense_debit_note_transaction.id'),
            'company_id' => 1,
            'voucher_number' => $expenseDebitNoteInput['voucher_number'],
            'round_off_method' => $roundOffMethod,
            'rounding_amount' => $roundOffMethod['correct_amount'],
        ]);
    }
});

/*
|----------------------------------------------------------------------------------------------------------------------------------------------------
| Update Expense Debit Note Transaction Item Invoice Backend Validation with update Test Case
|----------------------------------------------------------------------------------------------------------------------------------------------------
*/

test('validation with update decimal rpu amount when decimal place is 1 for update expense debit note transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 1]);
    $expenseDebitNoteInput = ExpenseDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $expenseDebitNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $debitNoteData = StoreExpenseDebitNoteTransactionAction::run($expenseDebitNoteInput);
    $expenseDebitNoteInput['items'][0]['rpu'] = 100.********;
    $expenseDebitNoteInput['items'][0]['rpu_without_gst'] = 100.********;
    $expenseDebitNoteInput['items'][0]['rpu_with_gst'] = 100.********;
    $expenseDebitNoteInput['items'][0]['gst_id'] = $gst->id;
    $expenseDebitNoteInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('expense-debit-note-transaction-update', $debitNoteData['transaction_id']), $expenseDebitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense Debit Note Transaction Update Successfully.',
        ]);
    $this->assertDatabaseHas('expense_debit_note_transactions', [
        'id' => $debitNoteData['transaction_id'],
        'company_id' => 1,
        'voucher_number' => $expenseDebitNoteInput['voucher_number'],
        'dn_item_type' => ExpenseDebitNoteTransaction::ITEM_INVOICE,
    ]);
    $debitNoteTransaction = ExpenseDebitNoteTransaction::with('debitNoteItems')->whereId($debitNoteData['transaction_id'])->first();
    $this->assertEquals(78.2, $debitNoteTransaction->debitNoteItems->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 2 for update expense debit note transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 2]);
    $expenseDebitNoteInput = ExpenseDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $expenseDebitNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $debitNoteData = StoreExpenseDebitNoteTransactionAction::run($expenseDebitNoteInput);
    $expenseDebitNoteInput['items'][0]['rpu'] = 100.********;
    $expenseDebitNoteInput['items'][0]['rpu_without_gst'] = 100.********;
    $expenseDebitNoteInput['items'][0]['rpu_with_gst'] = 100.********;
    $expenseDebitNoteInput['items'][0]['gst_id'] = $gst->id;
    $expenseDebitNoteInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('expense-debit-note-transaction-update', $debitNoteData['transaction_id']), $expenseDebitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense Debit Note Transaction Update Successfully.',
        ]);
    $this->assertDatabaseHas('expense_debit_note_transactions', [
        'id' => $debitNoteData['transaction_id'],
        'company_id' => 1,
        'voucher_number' => $expenseDebitNoteInput['voucher_number'],
        'dn_item_type' => ExpenseDebitNoteTransaction::ITEM_INVOICE,
    ]);
    $debitNoteTransaction = ExpenseDebitNoteTransaction::with('debitNoteItems')->whereId($debitNoteData['transaction_id'])->first();
    $this->assertEquals(78.22, $debitNoteTransaction->debitNoteItems->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 3 for update expense debit note transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 3]);
    $expenseDebitNoteInput = ExpenseDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $expenseDebitNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $debitNoteData = StoreExpenseDebitNoteTransactionAction::run($expenseDebitNoteInput);
    $expenseDebitNoteInput['items'][0]['rpu'] = 100.********;
    $expenseDebitNoteInput['items'][0]['rpu_without_gst'] = 100.********;
    $expenseDebitNoteInput['items'][0]['rpu_with_gst'] = 100.********;
    $expenseDebitNoteInput['items'][0]['gst_id'] = $gst->id;
    $expenseDebitNoteInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('expense-debit-note-transaction-update', $debitNoteData['transaction_id']), $expenseDebitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense Debit Note Transaction Update Successfully.',
        ]);
    $this->assertDatabaseHas('expense_debit_note_transactions', [
        'id' => $debitNoteData['transaction_id'],
        'company_id' => 1,
        'voucher_number' => $expenseDebitNoteInput['voucher_number'],
        'dn_item_type' => ExpenseDebitNoteTransaction::ITEM_INVOICE,
    ]);
    $debitNoteTransaction = ExpenseDebitNoteTransaction::with('debitNoteItems')->whereId($debitNoteData['transaction_id'])->first();
    $this->assertEquals(78.220, $debitNoteTransaction->debitNoteItems->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 4 for update expense debit note transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 4]);
    $expenseDebitNoteInput = ExpenseDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $expenseDebitNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $debitNoteData = StoreExpenseDebitNoteTransactionAction::run($expenseDebitNoteInput);
    $expenseDebitNoteInput['items'][0]['rpu'] = 100.********;
    $expenseDebitNoteInput['items'][0]['rpu_without_gst'] = 100.********;
    $expenseDebitNoteInput['items'][0]['rpu_with_gst'] = 100.********;
    $expenseDebitNoteInput['items'][0]['gst_id'] = $gst->id;
    $expenseDebitNoteInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('expense-debit-note-transaction-update', $debitNoteData['transaction_id']), $expenseDebitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense Debit Note Transaction Update Successfully.',
        ]);
    $this->assertDatabaseHas('expense_debit_note_transactions', [
        'id' => $debitNoteData['transaction_id'],
        'company_id' => 1,
        'voucher_number' => $expenseDebitNoteInput['voucher_number'],
        'dn_item_type' => ExpenseDebitNoteTransaction::ITEM_INVOICE,
    ]);
    $debitNoteTransaction = ExpenseDebitNoteTransaction::with('debitNoteItems')->whereId($debitNoteData['transaction_id'])->first();
    $this->assertEquals(78.2197, $debitNoteTransaction->debitNoteItems->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 5 for update expense debit note transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 5]);
    $expenseDebitNoteInput = ExpenseDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $expenseDebitNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $debitNoteData = StoreExpenseDebitNoteTransactionAction::run($expenseDebitNoteInput);
    $expenseDebitNoteInput['items'][0]['rpu'] = 100.********;
    $expenseDebitNoteInput['items'][0]['rpu_without_gst'] = 100.********;
    $expenseDebitNoteInput['items'][0]['rpu_with_gst'] = 100.********;
    $expenseDebitNoteInput['items'][0]['gst_id'] = $gst->id;
    $expenseDebitNoteInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('expense-debit-note-transaction-update', $debitNoteData['transaction_id']), $expenseDebitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense Debit Note Transaction Update Successfully.',
        ]);
    $this->assertDatabaseHas('expense_debit_note_transactions', [
        'id' => $debitNoteData['transaction_id'],
        'company_id' => 1,
        'voucher_number' => $expenseDebitNoteInput['voucher_number'],
        'dn_item_type' => ExpenseDebitNoteTransaction::ITEM_INVOICE,
    ]);
    $debitNoteTransaction = ExpenseDebitNoteTransaction::with('debitNoteItems')->whereId($debitNoteData['transaction_id'])->first();
    $this->assertEquals(78.21970, $debitNoteTransaction->debitNoteItems->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 1 for update expense debit note transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 1]);
    $expenseDebitNoteInput = ExpenseDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $expenseDebitNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $debitNoteData = StoreExpenseDebitNoteTransactionAction::run($expenseDebitNoteInput);
    $expenseDebitNoteInput['items'][0]['rpu'] = 100.********;
    $expenseDebitNoteInput['items'][0]['rpu_without_gst'] = 100.********;
    $expenseDebitNoteInput['items'][0]['rpu_with_gst'] = 100.********;
    $expenseDebitNoteInput['items'][0]['gst_id'] = $gst->id;
    $expenseDebitNoteInput['items'][0]['gst_tax_percentage'] = 2;
    $expenseDebitNoteInput['items'][0]['with_tax'] = false;

    //act
    $response = $this->postJson(route('expense-debit-note-transaction-update', $debitNoteData['transaction_id']), $expenseDebitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense Debit Note Transaction Update Successfully.',
        ]);
    $this->assertDatabaseHas('expense_debit_note_transactions', [
        'id' => $debitNoteData['transaction_id'],
        'company_id' => 1,
        'voucher_number' => $expenseDebitNoteInput['voucher_number'],
        'dn_item_type' => ExpenseDebitNoteTransaction::ITEM_INVOICE,
    ]);
    $debitNoteTransaction = ExpenseDebitNoteTransaction::with('debitNoteItems')->whereId($debitNoteData['transaction_id'])->first();
    $this->assertEquals(128.2, $debitNoteTransaction->debitNoteItems->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 2 for update expense debit note transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 2]);
    $expenseDebitNoteInput = ExpenseDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $expenseDebitNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $debitNoteData = StoreExpenseDebitNoteTransactionAction::run($expenseDebitNoteInput);
    $expenseDebitNoteInput['items'][0]['rpu'] = 100.********;
    $expenseDebitNoteInput['items'][0]['rpu_without_gst'] = 100.********;
    $expenseDebitNoteInput['items'][0]['rpu_with_gst'] = 100.********;
    $expenseDebitNoteInput['items'][0]['gst_id'] = $gst->id;
    $expenseDebitNoteInput['items'][0]['gst_tax_percentage'] = 2;
    $expenseDebitNoteInput['items'][0]['with_tax'] = false;

    //act
    $response = $this->postJson(route('expense-debit-note-transaction-update', $debitNoteData['transaction_id']), $expenseDebitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense Debit Note Transaction Update Successfully.',
        ]);
    $this->assertDatabaseHas('expense_debit_note_transactions', [
        'id' => $debitNoteData['transaction_id'],
        'company_id' => 1,
        'voucher_number' => $expenseDebitNoteInput['voucher_number'],
        'dn_item_type' => ExpenseDebitNoteTransaction::ITEM_INVOICE,
    ]);
    $debitNoteTransaction = ExpenseDebitNoteTransaction::with('debitNoteItems')->whereId($debitNoteData['transaction_id'])->first();
    $this->assertEquals(128.16, $debitNoteTransaction->debitNoteItems->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 3 for update expense debit note transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 3]);
    $expenseDebitNoteInput = ExpenseDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $expenseDebitNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $debitNoteData = StoreExpenseDebitNoteTransactionAction::run($expenseDebitNoteInput);
    $expenseDebitNoteInput['items'][0]['rpu'] = 100.********;
    $expenseDebitNoteInput['items'][0]['rpu_without_gst'] = 100.********;
    $expenseDebitNoteInput['items'][0]['rpu_with_gst'] = 100.********;
    $expenseDebitNoteInput['items'][0]['gst_id'] = $gst->id;
    $expenseDebitNoteInput['items'][0]['gst_tax_percentage'] = 2;
    $expenseDebitNoteInput['items'][0]['with_tax'] = false;

    //act
    $response = $this->postJson(route('expense-debit-note-transaction-update', $debitNoteData['transaction_id']), $expenseDebitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense Debit Note Transaction Update Successfully.',
        ]);
    $this->assertDatabaseHas('expense_debit_note_transactions', [
        'id' => $debitNoteData['transaction_id'],
        'company_id' => 1,
        'voucher_number' => $expenseDebitNoteInput['voucher_number'],
        'dn_item_type' => ExpenseDebitNoteTransaction::ITEM_INVOICE,
    ]);
    $debitNoteTransaction = ExpenseDebitNoteTransaction::with('debitNoteItems')->whereId($debitNoteData['transaction_id'])->first();
    $this->assertEquals(128.155, $debitNoteTransaction->debitNoteItems->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 4 for update expense debit note transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 4]);
    $expenseDebitNoteInput = ExpenseDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $expenseDebitNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $debitNoteData = StoreExpenseDebitNoteTransactionAction::run($expenseDebitNoteInput);
    $expenseDebitNoteInput['items'][0]['rpu'] = 100.********;
    $expenseDebitNoteInput['items'][0]['rpu_without_gst'] = 100.********;
    $expenseDebitNoteInput['items'][0]['rpu_with_gst'] = 100.********;
    $expenseDebitNoteInput['items'][0]['gst_id'] = $gst->id;
    $expenseDebitNoteInput['items'][0]['gst_tax_percentage'] = 2;
    $expenseDebitNoteInput['items'][0]['with_tax'] = false;

    //act
    $response = $this->postJson(route('expense-debit-note-transaction-update', $debitNoteData['transaction_id']), $expenseDebitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense Debit Note Transaction Update Successfully.',
        ]);
    $this->assertDatabaseHas('expense_debit_note_transactions', [
        'id' => $debitNoteData['transaction_id'],
        'company_id' => 1,
        'voucher_number' => $expenseDebitNoteInput['voucher_number'],
        'dn_item_type' => ExpenseDebitNoteTransaction::ITEM_INVOICE,
    ]);
    $debitNoteTransaction = ExpenseDebitNoteTransaction::with('debitNoteItems')->whereId($debitNoteData['transaction_id'])->first();
    $this->assertEquals(128.1552, $debitNoteTransaction->debitNoteItems->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 5 for update expense debit note transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 5]);
    $expenseDebitNoteInput = ExpenseDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $expenseDebitNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $debitNoteData = StoreExpenseDebitNoteTransactionAction::run($expenseDebitNoteInput);
    $expenseDebitNoteInput['items'][0]['rpu'] = 100.********;
    $expenseDebitNoteInput['items'][0]['rpu_without_gst'] = 100.********;
    $expenseDebitNoteInput['items'][0]['rpu_with_gst'] = 100.********;
    $expenseDebitNoteInput['items'][0]['gst_id'] = $gst->id;
    $expenseDebitNoteInput['items'][0]['gst_tax_percentage'] = 2;
    $expenseDebitNoteInput['items'][0]['with_tax'] = false;

    //act
    $response = $this->postJson(route('expense-debit-note-transaction-update', $debitNoteData['transaction_id']), $expenseDebitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense Debit Note Transaction Update Successfully.',
        ]);
    $this->assertDatabaseHas('expense_debit_note_transactions', [
        'id' => $debitNoteData['transaction_id'],
        'company_id' => 1,
        'voucher_number' => $expenseDebitNoteInput['voucher_number'],
        'dn_item_type' => ExpenseDebitNoteTransaction::ITEM_INVOICE,
    ]);
    $debitNoteTransaction = ExpenseDebitNoteTransaction::with('debitNoteItems')->whereId($debitNoteData['transaction_id'])->first();
    $this->assertEquals(128.15515, $debitNoteTransaction->debitNoteItems->first()->rpu_with_gst);
});

test('validation with update negative and wrong rpu without or with gst for update expense debit note transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $expenseDebitNoteInput = ExpenseDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $expenseDebitNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $debitNoteData = StoreExpenseDebitNoteTransactionAction::run($expenseDebitNoteInput);
    $expenseDebitNoteInput['items'][0]['rpu_without_gst'] = -10000;
    $expenseDebitNoteInput['items'][0]['rpu_with_gst'] = -10000;
    $expenseDebitNoteInput['items'][0]['gst_id'] = $gst->id;
    $expenseDebitNoteInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('expense-debit-note-transaction-update', $debitNoteData['transaction_id']), $expenseDebitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense Debit Note Transaction Update Successfully.',
        ]);
    $this->assertDatabaseHas('expense_debit_note_transactions', [
        'id' => $debitNoteData['transaction_id'],
        'company_id' => 1,
        'voucher_number' => $expenseDebitNoteInput['voucher_number'],
        'dn_item_type' => ExpenseDebitNoteTransaction::ITEM_INVOICE,
    ]);
    $debitNoteTransaction = ExpenseDebitNoteTransaction::with('debitNoteItems')->whereId($debitNoteData['transaction_id'])->first();
    $this->assertNotEquals(100, $debitNoteTransaction->debitNoteItems->first()->rpu_without_gst);
    $this->assertEquals(100.0, $debitNoteTransaction->debitNoteItems->first()->rpu_with_gst);
});

test('validation with update negative and wrong gst tax percentage for update expense debit note transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $expenseDebitNoteInput = ExpenseDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $expenseDebitNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $debitNoteData = StoreExpenseDebitNoteTransactionAction::run($expenseDebitNoteInput);
    $expenseDebitNoteInput['items'][0]['total'] = -10000;
    $expenseDebitNoteInput['items'][0]['gst_id'] = $gst->id;
    $expenseDebitNoteInput['items'][0]['gst_tax_percentage'] = $gst->tax_rate;

    //act
    $response = $this->postJson(route('expense-debit-note-transaction-update', $debitNoteData['transaction_id']), $expenseDebitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense Debit Note Transaction Update Successfully.',
        ]);
    $this->assertDatabaseHas('expense_debit_note_transactions', [
        'id' => $debitNoteData['transaction_id'],
        'company_id' => 1,
        'voucher_number' => $expenseDebitNoteInput['voucher_number'],
        'dn_item_type' => ExpenseDebitNoteTransaction::ITEM_INVOICE,
    ]);
    $debitNoteTransaction = ExpenseDebitNoteTransaction::with('debitNoteItems')->whereId($debitNoteData['transaction_id'])->first();
    $this->assertEquals(28, intval($debitNoteTransaction->debitNoteItems->first()->gst_tax_percentage));
});

test('validation with update negative and wrong total discount for update expense debit note transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(5.00)->first();

    $expenseDebitNoteInput = ExpenseDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $expenseDebitNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $debitNoteData = StoreExpenseDebitNoteTransactionAction::run($expenseDebitNoteInput);
    foreach ($expenseDebitNoteInput['items'] as $key => $item) {
        $expenseDebitNoteInput['items'][$key]['gst_id'] = $gst->id;
        $expenseDebitNoteInput['items'][$key]['discount_type'] = 2;
        $expenseDebitNoteInput['items'][$key]['discount_value'] = 10;
        $expenseDebitNoteInput['items'][$key]['discount_type_2'] = 2;
        $expenseDebitNoteInput['items'][$key]['discount_value_2'] = 10;
        $expenseDebitNoteInput['items'][$key]['total_discount_amount'] = 10;
    }

    //act
    $response = $this->postJson(route('expense-debit-note-transaction-update', $debitNoteData['transaction_id']), $expenseDebitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense Debit Note Transaction Update Successfully.',
        ]);
    $this->assertDatabaseHas('expense_debit_note_transactions', [
        'id' => $debitNoteData['transaction_id'],
        'company_id' => 1,
        'voucher_number' => $expenseDebitNoteInput['voucher_number'],
        'dn_item_type' => ExpenseDebitNoteTransaction::ITEM_INVOICE,
    ]);
    $debitNoteTransaction = ExpenseDebitNoteTransaction::with('debitNoteItems')->whereId($debitNoteData['transaction_id'])->first();
    foreach ($debitNoteTransaction->debitNoteItems as $item) {
        $this->assertEquals(18.1, $item->total_discount_amount);
    }
});

test('validation with update negative and wrong sub total for update expense debit note transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $expenseDebitNoteInput = ExpenseDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $expenseDebitNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $debitNoteData = StoreExpenseDebitNoteTransactionAction::run($expenseDebitNoteInput);
    $expenseDebitNoteInput['items'][0]['total'] = -10000;
    $expenseDebitNoteInput['items'][0]['gst_id'] = $gst->id;
    $expenseDebitNoteInput['items'][0]['gst_tax_percentage'] = $gst->tax_rate;

    //act
    $response = $this->postJson(route('expense-debit-note-transaction-update', $debitNoteData['transaction_id']), $expenseDebitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense Debit Note Transaction Update Successfully.',
        ]);
    $this->assertDatabaseHas('expense_debit_note_transactions', [
        'id' => $debitNoteData['transaction_id'],
        'company_id' => 1,
        'voucher_number' => $expenseDebitNoteInput['voucher_number'],
        'dn_item_type' => ExpenseDebitNoteTransaction::ITEM_INVOICE,
    ]);
    $debitNoteTransaction = ExpenseDebitNoteTransaction::with('debitNoteItems')->whereId($debitNoteData['transaction_id'])->first();
    $this->assertEquals(78.13, $debitNoteTransaction->debitNoteItems->first()->total);
});

test('validation with update negative and wrong sub total for update expense debit note transaction item invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $expenseDebitNoteInput = ExpenseDebitNoteTransactionFactory::new()->make()->toArray();
    $expenseDebitNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $debitNoteData = StoreExpenseDebitNoteTransactionAction::run($expenseDebitNoteInput);
    $expenseDebitNoteInput['items'][0]['total'] = -10000;

    //act
    $response = $this->postJson(route('expense-debit-note-transaction-update', $debitNoteData['transaction_id']), $expenseDebitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense Debit Note Transaction Update Successfully.',
        ]);
    $this->assertDatabaseHas('expense_debit_note_transactions', [
        'id' => $debitNoteData['transaction_id'],
        'company_id' => 1,
        'voucher_number' => $expenseDebitNoteInput['voucher_number'],
        'dn_item_type' => ExpenseDebitNoteTransaction::ITEM_INVOICE,
    ]);
    $debitNoteTransaction = ExpenseDebitNoteTransaction::with('debitNoteItems')->whereId($debitNoteData['transaction_id'])->first();
    $this->assertEquals(100, intval($debitNoteTransaction->debitNoteItems->first()->total));
});

test('validation with update negative and wrong total for update expense debit note transaction item invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $expenseDebitNoteInput = ExpenseDebitNoteTransactionFactory::new()->make()->toArray();
    $expenseDebitNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $debitNoteData = StoreExpenseDebitNoteTransactionAction::run($expenseDebitNoteInput);
    $expenseDebitNoteInput['total'] = -620;

    //act
    $response = $this->postJson(route('expense-debit-note-transaction-update', $debitNoteData['transaction_id']), $expenseDebitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense Debit Note Transaction Update Successfully.',
        ]);
    $this->assertDatabaseHas('expense_debit_note_transactions', [
        'id' => $debitNoteData['transaction_id'],
        'company_id' => 1,
        'voucher_number' => $expenseDebitNoteInput['voucher_number'],
        'dn_item_type' => ExpenseDebitNoteTransaction::ITEM_INVOICE,
        'total' => 620.00,
    ]);
});

test('validation with update negative and wrong gross value for update expense debit note transaction item invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $expenseDebitNoteInput = ExpenseDebitNoteTransactionFactory::new()->make()->toArray();
    $expenseDebitNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $debitNoteData = StoreExpenseDebitNoteTransactionAction::run($expenseDebitNoteInput);
    $expenseDebitNoteInput['gross_value'] = -100;

    //act
    $response = $this->postJson(route('expense-debit-note-transaction-update', $debitNoteData['transaction_id']), $expenseDebitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense Debit Note Transaction Update Successfully.',
        ]);
    $this->assertDatabaseHas('expense_debit_note_transactions', [
        'id' => $debitNoteData['transaction_id'],
        'company_id' => 1,
        'voucher_number' => $expenseDebitNoteInput['voucher_number'],
        'dn_item_type' => ExpenseDebitNoteTransaction::ITEM_INVOICE,
        'gross_value' => 600.00,
    ]);
});

test('validation with update negative and wrong additional charges gst tax percentage for update expense debit note transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $expenseDebitNoteInput = ExpenseDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $expenseDebitNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $debitNoteData = StoreExpenseDebitNoteTransactionAction::run($expenseDebitNoteInput);
    $expenseDebitNoteInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    $expenseDebitNoteInput['additional_charges'][0]['ac_total'] = -2000;

    //act
    $response = $this->postJson(route('expense-debit-note-transaction-update', $debitNoteData['transaction_id']), $expenseDebitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense Debit Note Transaction Update Successfully.',
        ]);
    $this->assertDatabaseHas('expense_debit_note_transactions', [
        'id' => $debitNoteData['transaction_id'],
        'company_id' => 1,
        'voucher_number' => $expenseDebitNoteInput['voucher_number'],
        'dn_item_type' => ExpenseDebitNoteTransaction::ITEM_INVOICE,
    ]);
    $debitNoteTransaction = ExpenseDebitNoteTransaction::with('additionalCharges')->whereId($debitNoteData['transaction_id'])->first();
    $this->assertNotEquals(10.00, $debitNoteTransaction->additionalCharges->first()->total);
});

test('validation with update negative and wrong additional charges total for update expense debit note transaction item invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $expenseDebitNoteInput = ExpenseDebitNoteTransactionFactory::new()->make()->toArray();
    $expenseDebitNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $debitNoteData = StoreExpenseDebitNoteTransactionAction::run($expenseDebitNoteInput);
    $expenseDebitNoteInput['additional_charges'][0]['ac_total'] = -200;

    //act
    $response = $this->postJson(route('expense-debit-note-transaction-update', $debitNoteData['transaction_id']), $expenseDebitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense Debit Note Transaction Update Successfully.',
        ]);
    $this->assertDatabaseHas('expense_debit_note_transactions', [
        'id' => $debitNoteData['transaction_id'],
        'company_id' => 1,
        'voucher_number' => $expenseDebitNoteInput['voucher_number'],
        'dn_item_type' => ExpenseDebitNoteTransaction::ITEM_INVOICE,
        'gross_value' => 600.00,
    ]);
    $debitNoteTransaction = ExpenseDebitNoteTransaction::with('additionalCharges')->whereId($debitNoteData['transaction_id'])->first();
    $this->assertEquals(10, intval($debitNoteTransaction->additionalCharges->first()->total));
});

test('validation with update negative and wrong cgst and sgst when purchase intra state taxable classification for update expense debit note transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $expenseDebitNoteInput = ExpenseDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'main_classification_nature_type' => 'Intrastate Purchase Taxable',
    ])->toArray();
    $expenseDebitNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $debitNoteData = StoreExpenseDebitNoteTransactionAction::run($expenseDebitNoteInput);
    $expenseDebitNoteInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    foreach ($expenseDebitNoteInput['items'] as $key => $item) {
        $expenseDebitNoteInput['items'][$key]['gst_id'] = $gst->id;
    }

    //act
    $response = $this->postJson(route('expense-debit-note-transaction-update', $debitNoteData['transaction_id']), $expenseDebitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense Debit Note Transaction Update Successfully.',
        ]);
    $this->assertDatabaseHas('expense_debit_note_transactions', [
        'id' => $debitNoteData['transaction_id'],
        'company_id' => 1,
        'voucher_number' => $expenseDebitNoteInput['voucher_number'],
        'dn_item_type' => ExpenseDebitNoteTransaction::ITEM_INVOICE,
    ]);
    $debitNoteTransaction = ExpenseDebitNoteTransaction::whereId($debitNoteData['transaction_id'])->first();
    $this->assertEquals(67.04, $debitNoteTransaction->cgst);
    $this->assertEquals(67.04, $debitNoteTransaction->sgst);
});

test('validation with update negative and wrong igst when purchase inter state taxable classification for update expense debit note transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $expenseDebitNoteInput = ExpenseDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'main_classification_nature_type' => 'Interstate Purchase Taxable',
    ])->toArray();
    $expenseDebitNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $debitNoteData = StoreExpenseDebitNoteTransactionAction::run($expenseDebitNoteInput);
    $expenseDebitNoteInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    foreach ($expenseDebitNoteInput['items'] as $key => $item) {
        $expenseDebitNoteInput['items'][$key]['gst_id'] = $gst->id;
    }

    //act
    $response = $this->postJson(route('expense-debit-note-transaction-update', $debitNoteData['transaction_id']), $expenseDebitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense Debit Note Transaction Update Successfully.',
        ]);
    $this->assertDatabaseHas('expense_debit_note_transactions', [
        'id' => $debitNoteData['transaction_id'],
        'company_id' => 1,
        'voucher_number' => $expenseDebitNoteInput['voucher_number'],
        'dn_item_type' => ExpenseDebitNoteTransaction::ITEM_INVOICE,
    ]);
    $debitNoteTransaction = ExpenseDebitNoteTransaction::whereId($debitNoteData['transaction_id'])->first();
    $this->assertEquals(134.08, $debitNoteTransaction->igst);
});

test('validation with update negative and wrong taxable value for update expense debit note transaction item invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $expenseDebitNoteInput = ExpenseDebitNoteTransactionFactory::new()->make()->toArray();
    $expenseDebitNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $debitNoteData = StoreExpenseDebitNoteTransactionAction::run($expenseDebitNoteInput);
    $expenseDebitNoteInput['taxable_value'] = -100;

    //act
    $response = $this->postJson(route('expense-debit-note-transaction-update', $debitNoteData['transaction_id']), $expenseDebitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense Debit Note Transaction Update Successfully.',
        ]);
    $this->assertDatabaseHas('expense_debit_note_transactions', [
        'id' => $debitNoteData['transaction_id'],
        'company_id' => 1,
        'voucher_number' => $expenseDebitNoteInput['voucher_number'],
        'dn_item_type' => ExpenseDebitNoteTransaction::ITEM_INVOICE,
        'taxable_value' => 610.00,
    ]);
});

test('validation with update negative and wrong addless total for update expense debit note transaction item invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $expenseDebitNoteInput = ExpenseDebitNoteTransactionFactory::new()->make()->toArray();
    $expenseDebitNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $debitNoteData = StoreExpenseDebitNoteTransactionAction::run($expenseDebitNoteInput);
    $expenseDebitNoteInput['add_less'][0]['al_total'] = -100;

    //act
    $response = $this->postJson(route('expense-debit-note-transaction-update', $debitNoteData['transaction_id']), $expenseDebitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense Debit Note Transaction Update Successfully.',
        ]);
    $this->assertDatabaseHas('expense_debit_note_transactions', [
        'id' => $debitNoteData['transaction_id'],
        'company_id' => 1,
        'voucher_number' => $expenseDebitNoteInput['voucher_number'],
        'dn_item_type' => ExpenseDebitNoteTransaction::ITEM_INVOICE,
        'gross_value' => 600.00,
    ]);
    $debitNoteTransaction = ExpenseDebitNoteTransaction::with('addLess')->whereId($debitNoteData['transaction_id'])->first();
    $this->assertEquals(10, intval($debitNoteTransaction->addLess->first()->total));
});

test('validation with update negative and wrong grand total for update expense debit note transaction item invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $expenseDebitNoteInput = ExpenseDebitNoteTransactionFactory::new()->make()->toArray();
    $expenseDebitNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $debitNoteData = StoreExpenseDebitNoteTransactionAction::run($expenseDebitNoteInput);
    $expenseDebitNoteInput['grand_total'] = -100;

    //act
    $response = $this->postJson(route('expense-debit-note-transaction-update', $debitNoteData['transaction_id']), $expenseDebitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense Debit Note Transaction Update Successfully.',
        ]);
    $this->assertDatabaseHas('expense_debit_note_transactions', [
        'id' => $debitNoteData['transaction_id'],
        'company_id' => 1,
        'voucher_number' => $expenseDebitNoteInput['voucher_number'],
        'dn_item_type' => ExpenseDebitNoteTransaction::ITEM_INVOICE,
        'grand_total' => 620.00,
    ]);
});

/*
|----------------------------------------------------------------------------------------------------------------------------------------------------
| Update Expense Debit Note Transaction Account Invoice Backend Validation with update Test Case
|----------------------------------------------------------------------------------------------------------------------------------------------------
*/

test('validation with update decimal rpu amount when decimal place is 1 for update expense debit note transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $expenseDebitNoteInput = ExpenseDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'dn_item_type' => ExpenseDebitNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $expenseDebitNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $debitNoteData = StoreExpenseDebitNoteTransactionAction::run($expenseDebitNoteInput);
    $expenseDebitNoteInput['ledgers'][0]['decimal'] = 1;
    $expenseDebitNoteInput['ledgers'][0]['rpu'] = 100.********;
    $expenseDebitNoteInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $expenseDebitNoteInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $expenseDebitNoteInput['ledgers'][0]['gst_id'] = $gst->id;
    $expenseDebitNoteInput['ledgers'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('expense-debit-note-transaction-update', $debitNoteData['transaction_id']), $expenseDebitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense Debit Note Transaction Update Successfully.',
        ]);
    $this->assertDatabaseHas('expense_debit_note_transactions', [
        'id' => $debitNoteData['transaction_id'],
        'company_id' => 1,
        'voucher_number' => $expenseDebitNoteInput['voucher_number'],
        'dn_item_type' => ExpenseDebitNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $debitNoteTransaction = ExpenseDebitNoteTransaction::with('debitNoteLedgers')->whereId($debitNoteData['transaction_id'])->first();
    $this->assertEquals(78.2, $debitNoteTransaction->debitNoteLedgers->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 2 for update expense debit note transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $expenseDebitNoteInput = ExpenseDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'dn_item_type' => ExpenseDebitNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $expenseDebitNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $debitNoteData = StoreExpenseDebitNoteTransactionAction::run($expenseDebitNoteInput);
    $expenseDebitNoteInput['ledgers'][0]['decimal'] = 2;
    $expenseDebitNoteInput['ledgers'][0]['rpu'] = 100.********;
    $expenseDebitNoteInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $expenseDebitNoteInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $expenseDebitNoteInput['ledgers'][0]['gst_id'] = $gst->id;
    $expenseDebitNoteInput['ledgers'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('expense-debit-note-transaction-update', $debitNoteData['transaction_id']), $expenseDebitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense Debit Note Transaction Update Successfully.',
        ]);
    $this->assertDatabaseHas('expense_debit_note_transactions', [
        'id' => $debitNoteData['transaction_id'],
        'company_id' => 1,
        'voucher_number' => $expenseDebitNoteInput['voucher_number'],
        'dn_item_type' => ExpenseDebitNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $debitNoteTransaction = ExpenseDebitNoteTransaction::with('debitNoteLedgers')->whereId($debitNoteData['transaction_id'])->first();
    $this->assertEquals(78.22, $debitNoteTransaction->debitNoteLedgers->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 3 for update expense debit note transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $expenseDebitNoteInput = ExpenseDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'dn_item_type' => ExpenseDebitNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $expenseDebitNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $debitNoteData = StoreExpenseDebitNoteTransactionAction::run($expenseDebitNoteInput);
    $expenseDebitNoteInput['ledgers'][0]['decimal'] = 3;
    $expenseDebitNoteInput['ledgers'][0]['rpu'] = 100.********;
    $expenseDebitNoteInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $expenseDebitNoteInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $expenseDebitNoteInput['ledgers'][0]['gst_id'] = $gst->id;
    $expenseDebitNoteInput['ledgers'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('expense-debit-note-transaction-update', $debitNoteData['transaction_id']), $expenseDebitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense Debit Note Transaction Update Successfully.',
        ]);
    $this->assertDatabaseHas('expense_debit_note_transactions', [
        'id' => $debitNoteData['transaction_id'],
        'company_id' => 1,
        'voucher_number' => $expenseDebitNoteInput['voucher_number'],
        'dn_item_type' => ExpenseDebitNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $debitNoteTransaction = ExpenseDebitNoteTransaction::with('debitNoteLedgers')->whereId($debitNoteData['transaction_id'])->first();
    $this->assertEquals(78.220, $debitNoteTransaction->debitNoteLedgers->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 4 for update expense debit note transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $expenseDebitNoteInput = ExpenseDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'dn_item_type' => ExpenseDebitNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $expenseDebitNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $debitNoteData = StoreExpenseDebitNoteTransactionAction::run($expenseDebitNoteInput);
    $expenseDebitNoteInput['ledgers'][0]['decimal'] = 4;
    $expenseDebitNoteInput['ledgers'][0]['rpu'] = 100.********;
    $expenseDebitNoteInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $expenseDebitNoteInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $expenseDebitNoteInput['ledgers'][0]['gst_id'] = $gst->id;
    $expenseDebitNoteInput['ledgers'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('expense-debit-note-transaction-update', $debitNoteData['transaction_id']), $expenseDebitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense Debit Note Transaction Update Successfully.',
        ]);
    $this->assertDatabaseHas('expense_debit_note_transactions', [
        'id' => $debitNoteData['transaction_id'],
        'company_id' => 1,
        'voucher_number' => $expenseDebitNoteInput['voucher_number'],
        'dn_item_type' => ExpenseDebitNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $debitNoteTransaction = ExpenseDebitNoteTransaction::with('debitNoteLedgers')->whereId($debitNoteData['transaction_id'])->first();
    $this->assertEquals(78.2197, $debitNoteTransaction->debitNoteLedgers->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 5 for update expense debit note transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $expenseDebitNoteInput = ExpenseDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'dn_item_type' => ExpenseDebitNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $expenseDebitNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $debitNoteData = StoreExpenseDebitNoteTransactionAction::run($expenseDebitNoteInput);
    $expenseDebitNoteInput['ledgers'][0]['decimal'] = 5;
    $expenseDebitNoteInput['ledgers'][0]['rpu'] = 100.********;
    $expenseDebitNoteInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $expenseDebitNoteInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $expenseDebitNoteInput['ledgers'][0]['gst_id'] = $gst->id;
    $expenseDebitNoteInput['ledgers'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('expense-debit-note-transaction-update', $debitNoteData['transaction_id']), $expenseDebitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense Debit Note Transaction Update Successfully.',
        ]);
    $this->assertDatabaseHas('expense_debit_note_transactions', [
        'id' => $debitNoteData['transaction_id'],
        'company_id' => 1,
        'voucher_number' => $expenseDebitNoteInput['voucher_number'],
        'dn_item_type' => ExpenseDebitNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $debitNoteTransaction = ExpenseDebitNoteTransaction::with('debitNoteLedgers')->whereId($debitNoteData['transaction_id'])->first();
    $this->assertEquals(78.21970, $debitNoteTransaction->debitNoteLedgers->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 1 for update expense debit note transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $expenseDebitNoteInput = ExpenseDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'dn_item_type' => ExpenseDebitNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $expenseDebitNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $debitNoteData = StoreExpenseDebitNoteTransactionAction::run($expenseDebitNoteInput);
    $expenseDebitNoteInput['ledgers'][0]['decimal'] = 1;
    $expenseDebitNoteInput['ledgers'][0]['rpu'] = 100.********;
    $expenseDebitNoteInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $expenseDebitNoteInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $expenseDebitNoteInput['ledgers'][0]['gst_id'] = $gst->id;
    $expenseDebitNoteInput['ledgers'][0]['gst_tax_percentage'] = 2;
    $expenseDebitNoteInput['ledgers'][0]['with_tax'] = false;

    //act
    $response = $this->postJson(route('expense-debit-note-transaction-update', $debitNoteData['transaction_id']), $expenseDebitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense Debit Note Transaction Update Successfully.',
        ]);
    $this->assertDatabaseHas('expense_debit_note_transactions', [
        'id' => $debitNoteData['transaction_id'],
        'company_id' => 1,
        'voucher_number' => $expenseDebitNoteInput['voucher_number'],
        'dn_item_type' => ExpenseDebitNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $debitNoteTransaction = ExpenseDebitNoteTransaction::with('debitNoteLedgers')->whereId($debitNoteData['transaction_id'])->first();
    $this->assertEquals(128.2, $debitNoteTransaction->debitNoteLedgers->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 2 for update expense debit note transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $expenseDebitNoteInput = ExpenseDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'dn_item_type' => ExpenseDebitNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $expenseDebitNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $debitNoteData = StoreExpenseDebitNoteTransactionAction::run($expenseDebitNoteInput);
    $expenseDebitNoteInput['ledgers'][0]['decimal'] = 2;
    $expenseDebitNoteInput['ledgers'][0]['rpu'] = 100.********;
    $expenseDebitNoteInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $expenseDebitNoteInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $expenseDebitNoteInput['ledgers'][0]['gst_id'] = $gst->id;
    $expenseDebitNoteInput['ledgers'][0]['gst_tax_percentage'] = 2;
    $expenseDebitNoteInput['ledgers'][0]['with_tax'] = false;

    //act
    $response = $this->postJson(route('expense-debit-note-transaction-update', $debitNoteData['transaction_id']), $expenseDebitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense Debit Note Transaction Update Successfully.',
        ]);
    $this->assertDatabaseHas('expense_debit_note_transactions', [
        'id' => $debitNoteData['transaction_id'],
        'company_id' => 1,
        'voucher_number' => $expenseDebitNoteInput['voucher_number'],
        'dn_item_type' => ExpenseDebitNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $debitNoteTransaction = ExpenseDebitNoteTransaction::with('debitNoteLedgers')->whereId($debitNoteData['transaction_id'])->first();
    $this->assertEquals(128.16, $debitNoteTransaction->debitNoteLedgers->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 3 for update expense debit note transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $expenseDebitNoteInput = ExpenseDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'dn_item_type' => ExpenseDebitNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $expenseDebitNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $debitNoteData = StoreExpenseDebitNoteTransactionAction::run($expenseDebitNoteInput);
    $expenseDebitNoteInput['ledgers'][0]['decimal'] = 3;
    $expenseDebitNoteInput['ledgers'][0]['rpu'] = 100.********;
    $expenseDebitNoteInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $expenseDebitNoteInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $expenseDebitNoteInput['ledgers'][0]['gst_id'] = $gst->id;
    $expenseDebitNoteInput['ledgers'][0]['gst_tax_percentage'] = 2;
    $expenseDebitNoteInput['ledgers'][0]['with_tax'] = false;

    //act
    $response = $this->postJson(route('expense-debit-note-transaction-update', $debitNoteData['transaction_id']), $expenseDebitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense Debit Note Transaction Update Successfully.',
        ]);
    $this->assertDatabaseHas('expense_debit_note_transactions', [
        'id' => $debitNoteData['transaction_id'],
        'company_id' => 1,
        'voucher_number' => $expenseDebitNoteInput['voucher_number'],
        'dn_item_type' => ExpenseDebitNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $debitNoteTransaction = ExpenseDebitNoteTransaction::with('debitNoteLedgers')->whereId($debitNoteData['transaction_id'])->first();
    $this->assertEquals(128.155, $debitNoteTransaction->debitNoteLedgers->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 4 for update expense debit note transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $expenseDebitNoteInput = ExpenseDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'dn_item_type' => ExpenseDebitNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $expenseDebitNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $debitNoteData = StoreExpenseDebitNoteTransactionAction::run($expenseDebitNoteInput);
    $expenseDebitNoteInput['ledgers'][0]['decimal'] = 4;
    $expenseDebitNoteInput['ledgers'][0]['rpu'] = 100.********;
    $expenseDebitNoteInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $expenseDebitNoteInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $expenseDebitNoteInput['ledgers'][0]['gst_id'] = $gst->id;
    $expenseDebitNoteInput['ledgers'][0]['gst_tax_percentage'] = 2;
    $expenseDebitNoteInput['ledgers'][0]['with_tax'] = false;

    //act
    $response = $this->postJson(route('expense-debit-note-transaction-update', $debitNoteData['transaction_id']), $expenseDebitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense Debit Note Transaction Update Successfully.',
        ]);
    $this->assertDatabaseHas('expense_debit_note_transactions', [
        'id' => $debitNoteData['transaction_id'],
        'company_id' => 1,
        'voucher_number' => $expenseDebitNoteInput['voucher_number'],
        'dn_item_type' => ExpenseDebitNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $debitNoteTransaction = ExpenseDebitNoteTransaction::with('debitNoteLedgers')->whereId($debitNoteData['transaction_id'])->first();
    $this->assertEquals(128.1552, $debitNoteTransaction->debitNoteLedgers->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 5 for update expense debit note transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $expenseDebitNoteInput = ExpenseDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'dn_item_type' => ExpenseDebitNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $expenseDebitNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $debitNoteData = StoreExpenseDebitNoteTransactionAction::run($expenseDebitNoteInput);
    $expenseDebitNoteInput['ledgers'][0]['decimal'] = 5;
    $expenseDebitNoteInput['ledgers'][0]['rpu'] = 100.********;
    $expenseDebitNoteInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $expenseDebitNoteInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $expenseDebitNoteInput['ledgers'][0]['gst_id'] = $gst->id;
    $expenseDebitNoteInput['ledgers'][0]['gst_tax_percentage'] = 2;
    $expenseDebitNoteInput['ledgers'][0]['with_tax'] = false;

    //act
    $response = $this->postJson(route('expense-debit-note-transaction-update', $debitNoteData['transaction_id']), $expenseDebitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense Debit Note Transaction Update Successfully.',
        ]);
    $this->assertDatabaseHas('expense_debit_note_transactions', [
        'id' => $debitNoteData['transaction_id'],
        'company_id' => 1,
        'voucher_number' => $expenseDebitNoteInput['voucher_number'],
        'dn_item_type' => ExpenseDebitNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $debitNoteTransaction = ExpenseDebitNoteTransaction::with('debitNoteLedgers')->whereId($debitNoteData['transaction_id'])->first();
    $this->assertEquals(128.15515, $debitNoteTransaction->debitNoteLedgers->first()->rpu_with_gst);
});

test('validation with update negative and wrong rpu without or with gst for update expense debit note transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $expenseDebitNoteInput = ExpenseDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'dn_item_type' => ExpenseDebitNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $expenseDebitNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $debitNoteData = StoreExpenseDebitNoteTransactionAction::run($expenseDebitNoteInput);
    $expenseDebitNoteInput['ledgers'][0]['rpu_without_gst'] = -10000;
    $expenseDebitNoteInput['ledgers'][0]['rpu_with_gst'] = -10000;
    $expenseDebitNoteInput['ledgers'][0]['gst_id'] = $gst->id;
    $expenseDebitNoteInput['ledgers'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('expense-debit-note-transaction-update', $debitNoteData['transaction_id']), $expenseDebitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense Debit Note Transaction Update Successfully.',
        ]);
    $this->assertDatabaseHas('expense_debit_note_transactions', [
        'id' => $debitNoteData['transaction_id'],
        'company_id' => 1,
        'voucher_number' => $expenseDebitNoteInput['voucher_number'],
        'dn_item_type' => ExpenseDebitNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $debitNoteTransaction = ExpenseDebitNoteTransaction::with('debitNoteLedgers')->whereId($debitNoteData['transaction_id'])->first();
    $this->assertNotEquals(100, $debitNoteTransaction->debitNoteLedgers->first()->rpu_without_gst);
    $this->assertEquals(100.0, $debitNoteTransaction->debitNoteLedgers->first()->rpu_with_gst);
});

test('validation with update negative and wrong gst tax percentage for update expense debit note transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $expenseDebitNoteInput = ExpenseDebitNoteTransactionFactory::new()->make([
        'is_cgst_sgst_igst_calculated' => 1,
        'is_gst_enabled' => true,
        'dn_item_type' => ExpenseDebitNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $expenseDebitNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $debitNoteData = StoreExpenseDebitNoteTransactionAction::run($expenseDebitNoteInput);
    $expenseDebitNoteInput['ledgers'][0]['total'] = -10000;
    $expenseDebitNoteInput['ledgers'][0]['gst_id'] = $gst->id;
    $expenseDebitNoteInput['ledgers'][0]['gst_tax_percentage'] = $gst->tax_rate;

    //act
    $response = $this->postJson(route('expense-debit-note-transaction-update', $debitNoteData['transaction_id']), $expenseDebitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense Debit Note Transaction Update Successfully.',
        ]);
    $this->assertDatabaseHas('expense_debit_note_transactions', [
        'id' => $debitNoteData['transaction_id'],
        'company_id' => 1,
        'voucher_number' => $expenseDebitNoteInput['voucher_number'],
        'dn_item_type' => ExpenseDebitNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $debitNoteTransaction = ExpenseDebitNoteTransaction::with('debitNoteLedgers')->whereId($debitNoteData['transaction_id'])->first();
    $this->assertEquals(28, intval($debitNoteTransaction->debitNoteLedgers->first()->gst_tax_percentage));
});

test('validation with update negative and wrong total discount for update expense debit note transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(5.00)->first();

    $expenseDebitNoteInput = ExpenseDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'dn_item_type' => ExpenseDebitNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $expenseDebitNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $debitNoteData = StoreExpenseDebitNoteTransactionAction::run($expenseDebitNoteInput);
    foreach ($expenseDebitNoteInput['ledgers'] as $key => $ledger) {
        $expenseDebitNoteInput['ledgers'][$key]['gst_id'] = $gst->id;
        $expenseDebitNoteInput['ledgers'][$key]['discount_type'] = 2;
        $expenseDebitNoteInput['ledgers'][$key]['discount_value'] = 10;
        $expenseDebitNoteInput['ledgers'][$key]['discount_type_2'] = 2;
        $expenseDebitNoteInput['ledgers'][$key]['discount_value_2'] = 10;
        $expenseDebitNoteInput['ledgers'][$key]['total_discount_amount'] = 10;
    }

    //act
    $response = $this->postJson(route('expense-debit-note-transaction-update', $debitNoteData['transaction_id']), $expenseDebitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense Debit Note Transaction Update Successfully.',
        ]);
    $this->assertDatabaseHas('expense_debit_note_transactions', [
        'id' => $debitNoteData['transaction_id'],
        'company_id' => 1,
        'voucher_number' => $expenseDebitNoteInput['voucher_number'],
        'dn_item_type' => ExpenseDebitNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $debitNoteTransaction = ExpenseDebitNoteTransaction::with('debitNoteLedgers')->whereId($debitNoteData['transaction_id'])->first();
    foreach ($debitNoteTransaction->debitNoteLedgers as $ledger) {
        $this->assertEquals(18.1, $ledger->total_discount_amount);
    }
});

test('validation with update negative and wrong sub total for update expense debit note transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $expenseDebitNoteInput = ExpenseDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'dn_item_type' => ExpenseDebitNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $expenseDebitNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $debitNoteData = StoreExpenseDebitNoteTransactionAction::run($expenseDebitNoteInput);
    $expenseDebitNoteInput['ledgers'][0]['total'] = -10000;
    $expenseDebitNoteInput['ledgers'][0]['gst_id'] = $gst->id;
    $expenseDebitNoteInput['ledgers'][0]['gst_tax_percentage'] = $gst->tax_rate;

    //act
    $response = $this->postJson(route('expense-debit-note-transaction-update', $debitNoteData['transaction_id']), $expenseDebitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense Debit Note Transaction Update Successfully.',
        ]);
    $this->assertDatabaseHas('expense_debit_note_transactions', [
        'id' => $debitNoteData['transaction_id'],
        'company_id' => 1,
        'voucher_number' => $expenseDebitNoteInput['voucher_number'],
        'dn_item_type' => ExpenseDebitNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $debitNoteTransaction = ExpenseDebitNoteTransaction::with('debitNoteLedgers')->whereId($debitNoteData['transaction_id'])->first();
    $this->assertEquals(78.13, $debitNoteTransaction->debitNoteLedgers->first()->total);
});

test('validation with update negative and wrong sub total for update expense debit note transaction account invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $expenseDebitNoteInput = ExpenseDebitNoteTransactionFactory::new()->make(['dn_item_type' => ExpenseDebitNoteTransaction::ACCOUNTING_INVOICE])->toArray();
    $expenseDebitNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $debitNoteData = StoreExpenseDebitNoteTransactionAction::run($expenseDebitNoteInput);
    $expenseDebitNoteInput['ledgers'][0]['total'] = -10000;

    //act
    $response = $this->postJson(route('expense-debit-note-transaction-update', $debitNoteData['transaction_id']), $expenseDebitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense Debit Note Transaction Update Successfully.',
        ]);
    $this->assertDatabaseHas('expense_debit_note_transactions', [
        'id' => $debitNoteData['transaction_id'],
        'company_id' => 1,
        'voucher_number' => $expenseDebitNoteInput['voucher_number'],
        'dn_item_type' => ExpenseDebitNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $debitNoteTransaction = ExpenseDebitNoteTransaction::with('debitNoteLedgers')->whereId($debitNoteData['transaction_id'])->first();
    $this->assertEquals(100, intval($debitNoteTransaction->debitNoteLedgers->first()->total));
});

test('validation with update negative and wrong total for update expense debit note transaction account invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $expenseDebitNoteInput = ExpenseDebitNoteTransactionFactory::new()->make([
        'dn_item_type' => ExpenseDebitNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $expenseDebitNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $debitNoteData = StoreExpenseDebitNoteTransactionAction::run($expenseDebitNoteInput);
    $expenseDebitNoteInput['total'] = 620;

    //act
    $response = $this->postJson(route('expense-debit-note-transaction-update', $debitNoteData['transaction_id']), $expenseDebitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense Debit Note Transaction Update Successfully.',
        ]);
    $this->assertDatabaseHas('expense_debit_note_transactions', [
        'id' => $debitNoteData['transaction_id'],
        'company_id' => 1,
        'voucher_number' => $expenseDebitNoteInput['voucher_number'],
        'dn_item_type' => ExpenseDebitNoteTransaction::ACCOUNTING_INVOICE,
        'total' => 620.00,
    ]);
});

test('validation with update negative and wrong gross value for update expense debit note transaction account invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $expenseDebitNoteInput = ExpenseDebitNoteTransactionFactory::new()->make([
        'dn_item_type' => ExpenseDebitNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $expenseDebitNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $debitNoteData = StoreExpenseDebitNoteTransactionAction::run($expenseDebitNoteInput);
    $expenseDebitNoteInput['gross_value'] = -100;

    //act
    $response = $this->postJson(route('expense-debit-note-transaction-update', $debitNoteData['transaction_id']), $expenseDebitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense Debit Note Transaction Update Successfully.',
        ]);
    $this->assertDatabaseHas('expense_debit_note_transactions', [
        'id' => $debitNoteData['transaction_id'],
        'company_id' => 1,
        'voucher_number' => $expenseDebitNoteInput['voucher_number'],
        'dn_item_type' => ExpenseDebitNoteTransaction::ACCOUNTING_INVOICE,
        'gross_value' => 600.00,
    ]);
});

test('validation with update negative and wrong additional charges gst tax percentage for update expense debit note transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $expenseDebitNoteInput = ExpenseDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'dn_item_type' => ExpenseDebitNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $expenseDebitNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $debitNoteData = StoreExpenseDebitNoteTransactionAction::run($expenseDebitNoteInput);
    $expenseDebitNoteInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    $expenseDebitNoteInput['additional_charges'][0]['ac_total'] = -2000;

    //act
    $response = $this->postJson(route('expense-debit-note-transaction-update', $debitNoteData['transaction_id']), $expenseDebitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense Debit Note Transaction Update Successfully.',
        ]);
    $this->assertDatabaseHas('expense_debit_note_transactions', [
        'id' => $debitNoteData['transaction_id'],
        'company_id' => 1,
        'voucher_number' => $expenseDebitNoteInput['voucher_number'],
        'dn_item_type' => ExpenseDebitNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $debitNoteTransaction = ExpenseDebitNoteTransaction::with('additionalCharges')->whereId($debitNoteData['transaction_id'])->first();
    $this->assertNotEquals(10.00, $debitNoteTransaction->additionalCharges->first()->total);
});

test('validation with update negative and wrong additional charges total for update expense debit note transaction account invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $expenseDebitNoteInput = ExpenseDebitNoteTransactionFactory::new()->make(['dn_item_type' => ExpenseDebitNoteTransaction::ACCOUNTING_INVOICE])->toArray();
    $expenseDebitNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $debitNoteData = StoreExpenseDebitNoteTransactionAction::run($expenseDebitNoteInput);
    $expenseDebitNoteInput['additional_charges'][0]['ac_total'] = -200;

    //act
    $response = $this->postJson(route('expense-debit-note-transaction-update', $debitNoteData['transaction_id']), $expenseDebitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense Debit Note Transaction Update Successfully.',
        ]);
    $this->assertDatabaseHas('expense_debit_note_transactions', [
        'id' => $debitNoteData['transaction_id'],
        'company_id' => 1,
        'voucher_number' => $expenseDebitNoteInput['voucher_number'],
        'dn_item_type' => ExpenseDebitNoteTransaction::ACCOUNTING_INVOICE,
        'gross_value' => 600.00,
    ]);
    $debitNoteTransaction = ExpenseDebitNoteTransaction::with('additionalCharges')->whereId($debitNoteData['transaction_id'])->first();
    $this->assertEquals(10, intval($debitNoteTransaction->additionalCharges->first()->total));
});

test('validation with update negative and wrong cgst and sgst when purchase intra state taxable classification for update expense debit note transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $expenseDebitNoteInput = ExpenseDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'main_classification_nature_type' => 'Intrastate Purchase Taxable',
        'dn_item_type' => ExpenseDebitNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $expenseDebitNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $debitNoteData = StoreExpenseDebitNoteTransactionAction::run($expenseDebitNoteInput);
    $expenseDebitNoteInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    foreach ($expenseDebitNoteInput['ledgers'] as $key => $ledger) {
        $expenseDebitNoteInput['ledgers'][$key]['gst_id'] = $gst->id;
    }

    //act
    $response = $this->postJson(route('expense-debit-note-transaction-update', $debitNoteData['transaction_id']), $expenseDebitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense Debit Note Transaction Update Successfully.',
        ]);
    $this->assertDatabaseHas('expense_debit_note_transactions', [
        'id' => $debitNoteData['transaction_id'],
        'company_id' => 1,
        'voucher_number' => $expenseDebitNoteInput['voucher_number'],
        'dn_item_type' => ExpenseDebitNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $debitNoteTransaction = ExpenseDebitNoteTransaction::whereId($debitNoteData['transaction_id'])->first();
    $this->assertEquals(67.04, $debitNoteTransaction->cgst);
    $this->assertEquals(67.04, $debitNoteTransaction->sgst);
});

test('validation with update negative and wrong igst when purchase inter state taxable classification for update expense debit note transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $expenseDebitNoteInput = ExpenseDebitNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'main_classification_nature_type' => 'Interstate Purchase Taxable',
        'dn_item_type' => ExpenseDebitNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $expenseDebitNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $debitNoteData = StoreExpenseDebitNoteTransactionAction::run($expenseDebitNoteInput);
    $expenseDebitNoteInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    foreach ($expenseDebitNoteInput['ledgers'] as $key => $ledger) {
        $expenseDebitNoteInput['ledgers'][$key]['gst_id'] = $gst->id;
    }

    //act
    $response = $this->postJson(route('expense-debit-note-transaction-update', $debitNoteData['transaction_id']), $expenseDebitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense Debit Note Transaction Update Successfully.',
        ]);
    $this->assertDatabaseHas('expense_debit_note_transactions', [
        'id' => $debitNoteData['transaction_id'],
        'company_id' => 1,
        'voucher_number' => $expenseDebitNoteInput['voucher_number'],
        'dn_item_type' => ExpenseDebitNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $debitNoteTransaction = ExpenseDebitNoteTransaction::whereId($debitNoteData['transaction_id'])->first();
    $this->assertEquals(134.08, $debitNoteTransaction->igst);
});

test('validation with update negative and wrong taxable value for update expense debit note transaction account invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $expenseDebitNoteInput = ExpenseDebitNoteTransactionFactory::new()->make([
        'dn_item_type' => ExpenseDebitNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $expenseDebitNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $debitNoteData = StoreExpenseDebitNoteTransactionAction::run($expenseDebitNoteInput);
    $expenseDebitNoteInput['taxable_value'] = -100;

    //act
    $response = $this->postJson(route('expense-debit-note-transaction-update', $debitNoteData['transaction_id']), $expenseDebitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense Debit Note Transaction Update Successfully.',
        ]);
    $this->assertDatabaseHas('expense_debit_note_transactions', [
        'id' => $debitNoteData['transaction_id'],
        'company_id' => 1,
        'voucher_number' => $expenseDebitNoteInput['voucher_number'],
        'dn_item_type' => ExpenseDebitNoteTransaction::ACCOUNTING_INVOICE,
        'taxable_value' => 610.00,
    ]);
});

test('validation with update negative and wrong addless total for update expense debit note transaction account invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $expenseDebitNoteInput = ExpenseDebitNoteTransactionFactory::new()->make([
        'dn_item_type' => ExpenseDebitNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $expenseDebitNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $debitNoteData = StoreExpenseDebitNoteTransactionAction::run($expenseDebitNoteInput);
    $expenseDebitNoteInput['add_less'][0]['al_total'] = -100;

    //act
    $response = $this->postJson(route('expense-debit-note-transaction-update', $debitNoteData['transaction_id']), $expenseDebitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense Debit Note Transaction Update Successfully.',
        ]);
    $this->assertDatabaseHas('expense_debit_note_transactions', [
        'id' => $debitNoteData['transaction_id'],
        'company_id' => 1,
        'voucher_number' => $expenseDebitNoteInput['voucher_number'],
        'dn_item_type' => ExpenseDebitNoteTransaction::ACCOUNTING_INVOICE,
        'gross_value' => 600.00,
    ]);
    $debitNoteTransaction = ExpenseDebitNoteTransaction::with('addLess')->whereId($debitNoteData['transaction_id'])->first();
    $this->assertEquals(10, intval($debitNoteTransaction->addLess->first()->total));
});

test('validation with update negative and wrong grand total for update expense debit note transaction account invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $expenseDebitNoteInput = ExpenseDebitNoteTransactionFactory::new()->make([
        'dn_item_type' => ExpenseDebitNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $expenseDebitNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $debitNoteData = StoreExpenseDebitNoteTransactionAction::run($expenseDebitNoteInput);
    $expenseDebitNoteInput['grand_total'] = -100;

    //act
    $response = $this->postJson(route('expense-debit-note-transaction-update', $debitNoteData['transaction_id']), $expenseDebitNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense Debit Note Transaction Update Successfully.',
        ]);
    $this->assertDatabaseHas('expense_debit_note_transactions', [
        'id' => $debitNoteData['transaction_id'],
        'company_id' => 1,
        'voucher_number' => $expenseDebitNoteInput['voucher_number'],
        'dn_item_type' => ExpenseDebitNoteTransaction::ACCOUNTING_INVOICE,
        'grand_total' => 620.00,
    ]);
});

test('validation with update negative and wrong round off amount and method for update expense debit note transaction', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);
    $expenseDebitNoteInput = ExpenseDebitNoteTransactionFactory::new()->make()->toArray();
    $expenseDebitNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $expenseDebitNoteInput['additional_charges'][0]['ac_value'] = 10.54;

    $roundOffMethods = [
        ['method' => 1, 'correct_amount' => 0],
        ['method' => 2, 'correct_amount' => -0.54],
        ['method' => 3, 'correct_amount' => 0.46],
        ['method' => 4, 'correct_amount' => 0.46],
    ];
    foreach ($roundOffMethods as $roundOffMethod) {
        $expenseDebitNoteInput['voucher_number'] = 'V-'.$roundOffMethod['method'];
        $debitNoteData = StoreExpenseDebitNoteTransactionAction::run($expenseDebitNoteInput);
        $expenseDebitNoteInput['round_off_method'] = 0;
        ExpenseDebitNoteTransactionMaster::whereCompanyId(1)->update(['round_off_method' => $roundOffMethod['method']]);
        $expenseDebitNoteInput['rounding_amount'] = -10.50;

        //act
        $response = $this->postJson(route('expense-debit-note-transaction-update', $debitNoteData['transaction_id']), $expenseDebitNoteInput);

        //assert
        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Expense Debit Note Transaction Update Successfully.',
            ]);
        $this->assertDatabaseHas('expense_debit_note_transactions', [
            'id' => $debitNoteData['transaction_id'],
            'company_id' => 1,
            'voucher_number' => $expenseDebitNoteInput['voucher_number'],
            'round_off_method' => $roundOffMethod,
            'rounding_amount' => $roundOffMethod['correct_amount'],
        ]);
    }
});
