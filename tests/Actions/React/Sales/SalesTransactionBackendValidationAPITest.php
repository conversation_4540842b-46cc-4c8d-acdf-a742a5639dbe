<?php

use App\Actions\v1\Transactions\Sale\StoreSaleTransactionAction;
use App\Models\GstTax;
use App\Models\Master\IncomeTransactionMaster;
use App\Models\Master\ItemMasterGoods;
use App\Models\SaleTransaction;
use Carbon\Carbon;
use Database\Factories\Api\SaleTransactionFactory;

/*
|----------------------------------------------------------------------------------------------------------------------------------------------------
| Sale Transaction Item Invoice Backend Validation Test Case
|----------------------------------------------------------------------------------------------------------------------------------------------------
*/

test('validate sale transaction item invoice will return gst na incorrect', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false, 'app.is_validation_testing' => false]);

    $saleInput = SaleTransactionFactory::new()->make()->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = $this->postJson(route('sale-transaction'), $saleInput);

    //asert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Is GST NA is not correct.',
        ]);
});

test('validate sale transaction item invoice will return cgst, sgst, and igst calculated is not correct', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false, 'app.is_validation_testing' => false]);

    $saleInput = SaleTransactionFactory::new()->make(['is_gst_na' => true, 'is_cgst_sgst_igst_calculated' => true])->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = $this->postJson(route('sale-transaction'), $saleInput);

    //asert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Is CGST, SGST, and IGST Calculated is not correct.',
        ]);
});

test('validate sale transaction item invoice will return subtotal is not correct when pass wrong total', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $saleInput = SaleTransactionFactory::new()->make([
        'is_gst_na' => true,
    ])->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');
    $saleInput['items'][0]['total'] = 10;

    //act
    $response = $this->postJson(route('sale-transaction'), $saleInput);

    //asert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Subtotal is not correct.',
        ]);
});

test('validate sale transaction item invoice will return subtotal is not correct when pass wrong total with gst na, exempt, zero in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $saleInput = SaleTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');
    $saleInput['items'][0]['total'] = 10;

    //act
    $response = $this->postJson(route('sale-transaction'), $saleInput);

    //asert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Subtotal is not correct.',
        ]);
});

test('validate sale transaction item invoice will return subtotal is not correct when pass wrong total with gst percentage', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $gst = GstTax::whereTaxRate(28.00)->first();
    $saleInput = SaleTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');
    $saleInput['items'][0]['gst_id'] = $gst->id;
    $saleInput['items'][0]['gst_tax_percentage'] = $gst->tax_rate;

    //act
    $response = $this->postJson(route('sale-transaction'), $saleInput);

    //asert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Subtotal is not correct.',
        ]);
});

test('validate sale transaction item invoice will return gross value is not correct', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $saleInput = SaleTransactionFactory::new()->make([
        'is_gst_na' => true,
        'gross_value' => 10,
    ])->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = $this->postJson(route('sale-transaction'), $saleInput);

    //asert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Gross value is not correct.',
        ]);
});

test('validate sale transaction item invoice will return addition charge total is not correct', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $saleInput = SaleTransactionFactory::new()->make([
        'is_gst_na' => true,
    ])->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');
    $saleInput['additional_charges'][0]['ac_value'] = 20;

    //act
    $response = $this->postJson(route('sale-transaction'), $saleInput);

    //asert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Additional charge total is not correct.',
        ]);
});

test('validate sale transaction item invoice will return Taxable value is not correct when pass additional gst percentage in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $gst = GstTax::whereTaxRate(28.00)->first();
    $saleInput = SaleTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');
    $saleInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    foreach ($saleInput['items'] as $key => $item) {
        $saleInput['items'][$key]['gst_id'] = 2;
    }

    //act
    $response = $this->postJson(route('sale-transaction'), $saleInput);

    //asert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Taxable value is not correct.',
        ]);
});

test('validate sale transaction item invoice will return taxable value is not correct', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $saleInput = SaleTransactionFactory::new()->make([
        'is_gst_na' => true,
        'taxable_value' => 10,
    ])->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = $this->postJson(route('sale-transaction'), $saleInput);

    //asert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Taxable value is not correct.',
        ]);
});

test('validate sale transaction item invoice will return cgst is not correct in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $saleInput = SaleTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'gross_value' => 468.78,
        'taxable_value' => 478.78,
        'main_classification_nature_type' => 'Sale Intra State Taxable',
    ])->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');
    foreach ($saleInput['items'] as $key => $item) {
        $saleInput['items'][$key]['gst_id'] = $gst->id;
        $saleInput['items'][$key]['total'] = 78.13;
    }

    //act
    $response = $this->postJson(route('sale-transaction'), $saleInput);

    //asert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'CGST is not correct.',
        ]);
});

test('validate sale transaction item invoice will return sgst is not correct in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $saleInput = SaleTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'gross_value' => 468.78,
        'taxable_value' => 478.78,
        'main_classification_nature_type' => 'Sale Intra State Taxable',
        'cgst' => 65.64,
    ])->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');
    foreach ($saleInput['items'] as $key => $item) {
        $saleInput['items'][$key]['gst_id'] = $gst->id;
        $saleInput['items'][$key]['total'] = 78.13;
    }

    //act
    $response = $this->postJson(route('sale-transaction'), $saleInput);

    //asert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'SGST is not correct.',
        ]);
});

test('validate sale transaction item invoice will return igst is not correct in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $saleInput = SaleTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'gross_value' => 468.78,
        'taxable_value' => 478.78,
    ])->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');
    foreach ($saleInput['items'] as $key => $item) {
        $saleInput['items'][$key]['gst_id'] = $gst->id;
        $saleInput['items'][$key]['total'] = 78.13;
    }

    //act
    $response = $this->postJson(route('sale-transaction'), $saleInput);

    //asert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'IGST is not correct.',
        ]);
});

test('validate sale transaction item invoice will return classification nature type is empty', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(0.00)->first();

    $saleInput = SaleTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'main_classification_nature_type' => null,
        'taxable_value' => 610,
    ])->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');
    foreach ($saleInput['items'] as $key => $item) {
        $saleInput['items'][$key]['gst_id'] = $gst->id;
    }

    //act
    $response = $this->postJson(route('sale-transaction'), $saleInput);

    //asert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Classification Nature Type is empty.',
        ]);
});

test('validate sale transaction item invoice will return add less total is not correct', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $saleInput = SaleTransactionFactory::new()->make([
        'is_gst_na' => true,
        'taxable_value' => 610,
    ])->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');
    $saleInput['add_less'][0]['al_total'] = 100;

    //act
    $response = $this->postJson(route('sale-transaction'), $saleInput);

    //asert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Add Less total is not correct.',
        ]);
});

test('validate sale transaction item invoice will return round off amount is not correct', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $saleInput = SaleTransactionFactory::new()->make([
        'is_gst_na' => true,
        'taxable_value' => 610,
        'rounding_amount' => 10.50,
    ])->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = $this->postJson(route('sale-transaction'), $saleInput);

    //asert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Round off amount is not correct.',
        ]);
});

test('validate sale transaction item invoice will return grand total is not correct', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $saleInput = SaleTransactionFactory::new()->make([
        'is_gst_na' => true,
        'taxable_value' => 610,
        'grand_total' => 100,
    ])->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = $this->postJson(route('sale-transaction'), $saleInput);

    //asert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Grand total is not correct.',
        ]);
});

/*
|----------------------------------------------------------------------------------------------------------------------------------------------------
| Sale Transaction Account Invoice Backend Validation Test Case
|----------------------------------------------------------------------------------------------------------------------------------------------------
*/

test('validate sale transaction account invoice will return gst na incorrect', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false, 'app.is_validation_testing' => false]);

    $saleInput = SaleTransactionFactory::new()->make(['sales_item_type' => SaleTransaction::ACCOUNTING_INVOICE])->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = $this->postJson(route('sale-transaction'), $saleInput);

    //asert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Is GST NA is not correct.',
        ]);
});

test('validate sale transaction account invoice will return cgst, sgst, and igst calculated is not correct', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false, 'app.is_validation_testing' => false]);

    $saleInput = SaleTransactionFactory::new()->make([
        'sales_item_type' => SaleTransaction::ACCOUNTING_INVOICE,
        'is_gst_na' => true,
        'is_cgst_sgst_igst_calculated' => true,
    ])->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = $this->postJson(route('sale-transaction'), $saleInput);

    //asert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Is CGST, SGST, and IGST Calculated is not correct.',
        ]);
});

test('validate sale transaction account invoice will return subtotal is not correct when pass wrong total', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $saleInput = SaleTransactionFactory::new()->make([
        'sales_item_type' => SaleTransaction::ACCOUNTING_INVOICE,
        'is_gst_na' => true,
    ])->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');
    $saleInput['ledgers'][0]['total'] = 10;

    //act
    $response = $this->postJson(route('sale-transaction'), $saleInput);

    //asert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Subtotal is not correct.',
        ]);
});

test('validate sale transaction account invoice will return subtotal is not correct when pass wrong total with gst na, exempt, zero in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $saleInput = SaleTransactionFactory::new()->make([
        'sales_item_type' => SaleTransaction::ACCOUNTING_INVOICE,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');
    $saleInput['ledgers'][0]['total'] = 10;

    //act
    $response = $this->postJson(route('sale-transaction'), $saleInput);

    //asert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Subtotal is not correct.',
        ]);
});

test('validate sale transaction account invoice will return subtotal is not correct when pass wrong total with gst percentage', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $gst = GstTax::whereTaxRate(28.00)->first();
    $saleInput = SaleTransactionFactory::new()->make([
        'sales_item_type' => SaleTransaction::ACCOUNTING_INVOICE,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');
    $saleInput['ledgers'][0]['gst_id'] = $gst->id;
    $saleInput['ledgers'][0]['with_tax'] = true;
    $saleInput['ledgers'][0]['gst_tax_percentage'] = $gst->tax_rate;

    //act
    $response = $this->postJson(route('sale-transaction'), $saleInput);

    //asert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Subtotal is not correct.',
        ]);
});

test('validate sale transaction account invoice will return gross value is not correct', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $saleInput = SaleTransactionFactory::new()->make([
        'sales_item_type' => SaleTransaction::ACCOUNTING_INVOICE,
        'is_gst_na' => true,
        'gross_value' => 10,
    ])->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = $this->postJson(route('sale-transaction'), $saleInput);

    //asert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Gross value is not correct.',
        ]);
});

test('validate sale transaction account invoice will return addition charge total is not correct', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $saleInput = SaleTransactionFactory::new()->make([
        'sales_item_type' => SaleTransaction::ACCOUNTING_INVOICE,
        'is_gst_na' => true,
    ])->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');
    $saleInput['additional_charges'][0]['ac_value'] = 20;

    //act
    $response = $this->postJson(route('sale-transaction'), $saleInput);

    //asert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Additional charge total is not correct.',
        ]);
});

test('validate sale transaction account invoice will return Taxable value is not correct when pass additional gst percentage in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $gst = GstTax::whereTaxRate(28.00)->first();
    $saleInput = SaleTransactionFactory::new()->make([
        'sales_item_type' => SaleTransaction::ACCOUNTING_INVOICE,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');
    $saleInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    foreach ($saleInput['ledgers'] as $key => $item) {
        $saleInput['ledgers'][$key]['gst_id'] = 2;
    }

    //act
    $response = $this->postJson(route('sale-transaction'), $saleInput);

    //asert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Taxable value is not correct.',
        ]);
});

test('validate sale transaction account invoice will return taxable value is not correct', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $saleInput = SaleTransactionFactory::new()->make([
        'sales_item_type' => SaleTransaction::ACCOUNTING_INVOICE,
        'is_gst_na' => true,
        'taxable_value' => 10,
    ])->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = $this->postJson(route('sale-transaction'), $saleInput);

    //asert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Taxable value is not correct.',
        ]);
});

test('validate sale transaction account invoice will return cgst is not correct in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $saleInput = SaleTransactionFactory::new()->make([
        'sales_item_type' => SaleTransaction::ACCOUNTING_INVOICE,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'gross_value' => 468.78,
        'taxable_value' => 478.78,
        'main_classification_nature_type' => 'Sale Intra State Taxable',
    ])->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');
    foreach ($saleInput['ledgers'] as $key => $item) {
        $saleInput['ledgers'][$key]['gst_id'] = $gst->id;
        $saleInput['ledgers'][$key]['total'] = 78.13;
    }

    //act
    $response = $this->postJson(route('sale-transaction'), $saleInput);

    //asert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'CGST is not correct.',
        ]);
});

test('validate sale transaction account invoice will return sgst is not correct in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $saleInput = SaleTransactionFactory::new()->make([
        'sales_item_type' => SaleTransaction::ACCOUNTING_INVOICE,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'gross_value' => 468.78,
        'taxable_value' => 478.78,
        'main_classification_nature_type' => 'Sale Intra State Taxable',
        'cgst' => 65.64,
    ])->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');
    foreach ($saleInput['ledgers'] as $key => $item) {
        $saleInput['ledgers'][$key]['gst_id'] = $gst->id;
        $saleInput['ledgers'][$key]['total'] = 78.13;
    }

    //act
    $response = $this->postJson(route('sale-transaction'), $saleInput);

    //asert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'SGST is not correct.',
        ]);
});

test('validate sale transaction account invoice will return igst is not correct in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $saleInput = SaleTransactionFactory::new()->make([
        'sales_item_type' => SaleTransaction::ACCOUNTING_INVOICE,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'gross_value' => 468.78,
        'taxable_value' => 478.78,
    ])->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');
    foreach ($saleInput['ledgers'] as $key => $item) {
        $saleInput['ledgers'][$key]['gst_id'] = $gst->id;
        $saleInput['ledgers'][$key]['total'] = 78.13;
    }

    //act
    $response = $this->postJson(route('sale-transaction'), $saleInput);

    //asert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'IGST is not correct.',
        ]);
});

test('validate sale transaction account invoice will return classification nature type is empty', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(0.00)->first();

    $saleInput = SaleTransactionFactory::new()->make([
        'sales_item_type' => SaleTransaction::ACCOUNTING_INVOICE,
        'is_gst_enabled' => true,
        'main_classification_nature_type' => null,
        'taxable_value' => 610,
    ])->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');
    foreach ($saleInput['ledgers'] as $key => $item) {
        $saleInput['ledgers'][$key]['gst_id'] = $gst->id;
    }

    //act
    $response = $this->postJson(route('sale-transaction'), $saleInput);

    //asert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Classification Nature Type is empty.',
        ]);
});

test('validate sale transaction account invoice will return add less total is not correct', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $saleInput = SaleTransactionFactory::new()->make([
        'sales_item_type' => SaleTransaction::ACCOUNTING_INVOICE,
        'is_gst_na' => true,
        'taxable_value' => 610,
    ])->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');
    $saleInput['add_less'][0]['al_total'] = 100;

    //act
    $response = $this->postJson(route('sale-transaction'), $saleInput);

    //asert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Add Less total is not correct.',
        ]);
});

test('validate sale transaction account invoice will return round off amount is not correct', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $saleInput = SaleTransactionFactory::new()->make([
        'sales_item_type' => SaleTransaction::ACCOUNTING_INVOICE,
        'is_gst_na' => true,
        'taxable_value' => 610,
        'rounding_amount' => 10.50,
    ])->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = $this->postJson(route('sale-transaction'), $saleInput);

    //asert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Round off amount is not correct.',
        ]);
});

test('validate sale transaction account invoice will return grand total is not correct', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $saleInput = SaleTransactionFactory::new()->make([
        'sales_item_type' => SaleTransaction::ACCOUNTING_INVOICE,
        'is_gst_na' => true,
        'taxable_value' => 610,
        'grand_total' => 100,
    ])->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = $this->postJson(route('sale-transaction'), $saleInput);

    //asert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Grand total is not correct.',
        ]);
});

/*
|----------------------------------------------------------------------------------------------------------------------------------------------------
| Store Sale Transaction Item Invoice Backend Validation with update Test Case
|----------------------------------------------------------------------------------------------------------------------------------------------------
*/

test('validation with update decimal rpu amount when decimal place is 1 for sale transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 1]);
    $saleInput = SaleTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');
    $saleInput['items'][0]['rpu'] = 100.********;
    $saleInput['items'][0]['rpu_without_gst'] = 100.********;
    $saleInput['items'][0]['rpu_with_gst'] = 100.********;
    $saleInput['items'][0]['gst_id'] = $gst->id;
    $saleInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('sale-transaction'), $saleInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('sales_transactions', [
        'id' => $response->json('data.sale_transaction.id'),
        'company_id' => 1,
        'invoice_number' => $response->json('data.sale_transaction.invoice_number'),
        'sales_item_type' => SaleTransaction::ITEM_INVOICE,
    ]);
    $saleTransaction = SaleTransaction::with('saleItems')->whereId($response->json('data.sale_transaction.id'))->first();
    $this->assertEquals(78.2, $saleTransaction->saleItems->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 2 for sale transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 2]);
    $saleInput = SaleTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');
    $saleInput['items'][0]['rpu'] = 100.********;
    $saleInput['items'][0]['rpu_without_gst'] = 100.********;
    $saleInput['items'][0]['rpu_with_gst'] = 100.********;
    $saleInput['items'][0]['gst_id'] = $gst->id;
    $saleInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('sale-transaction'), $saleInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('sales_transactions', [
        'id' => $response->json('data.sale_transaction.id'),
        'company_id' => 1,
        'invoice_number' => $response->json('data.sale_transaction.invoice_number'),
        'sales_item_type' => SaleTransaction::ITEM_INVOICE,
    ]);
    $saleTransaction = SaleTransaction::with('saleItems')->whereId($response->json('data.sale_transaction.id'))->first();
    $this->assertEquals(78.22, $saleTransaction->saleItems->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 3 for sale transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 3]);
    $saleInput = SaleTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');
    $saleInput['items'][0]['rpu'] = 100.********;
    $saleInput['items'][0]['rpu_without_gst'] = 100.********;
    $saleInput['items'][0]['rpu_with_gst'] = 100.********;
    $saleInput['items'][0]['gst_id'] = $gst->id;
    $saleInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('sale-transaction'), $saleInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('sales_transactions', [
        'id' => $response->json('data.sale_transaction.id'),
        'company_id' => 1,
        'invoice_number' => $response->json('data.sale_transaction.invoice_number'),
        'sales_item_type' => SaleTransaction::ITEM_INVOICE,
    ]);
    $saleTransaction = SaleTransaction::with('saleItems')->whereId($response->json('data.sale_transaction.id'))->first();
    $this->assertEquals(78.220, $saleTransaction->saleItems->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 4 for sale transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 4]);
    $saleInput = SaleTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');
    $saleInput['items'][0]['rpu'] = 100.********;
    $saleInput['items'][0]['rpu_without_gst'] = 100.********;
    $saleInput['items'][0]['rpu_with_gst'] = 100.********;
    $saleInput['items'][0]['gst_id'] = $gst->id;
    $saleInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('sale-transaction'), $saleInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('sales_transactions', [
        'id' => $response->json('data.sale_transaction.id'),
        'company_id' => 1,
        'invoice_number' => $response->json('data.sale_transaction.invoice_number'),
        'sales_item_type' => SaleTransaction::ITEM_INVOICE,
    ]);
    $saleTransaction = SaleTransaction::with('saleItems')->whereId($response->json('data.sale_transaction.id'))->first();
    $this->assertEquals(78.2197, $saleTransaction->saleItems->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 5 for sale transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 5]);
    $saleInput = SaleTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');
    $saleInput['items'][0]['rpu'] = 100.********;
    $saleInput['items'][0]['rpu_without_gst'] = 100.********;
    $saleInput['items'][0]['rpu_with_gst'] = 100.********;
    $saleInput['items'][0]['gst_id'] = $gst->id;
    $saleInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('sale-transaction'), $saleInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('sales_transactions', [
        'id' => $response->json('data.sale_transaction.id'),
        'company_id' => 1,
        'invoice_number' => $response->json('data.sale_transaction.invoice_number'),
        'sales_item_type' => SaleTransaction::ITEM_INVOICE,
    ]);
    $saleTransaction = SaleTransaction::with('saleItems')->whereId($response->json('data.sale_transaction.id'))->first();
    $this->assertEquals(78.21970, $saleTransaction->saleItems->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 1 for sale transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 1]);
    $saleInput = SaleTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');
    $saleInput['items'][0]['rpu'] = 100.********;
    $saleInput['items'][0]['rpu_without_gst'] = 100.********;
    $saleInput['items'][0]['rpu_with_gst'] = 100.********;
    $saleInput['items'][0]['gst_id'] = $gst->id;
    $saleInput['items'][0]['gst_tax_percentage'] = 2;
    $saleInput['items'][0]['with_tax'] = false;

    //act
    $response = $this->postJson(route('sale-transaction'), $saleInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('sales_transactions', [
        'id' => $response->json('data.sale_transaction.id'),
        'company_id' => 1,
        'invoice_number' => $response->json('data.sale_transaction.invoice_number'),
        'sales_item_type' => SaleTransaction::ITEM_INVOICE,
    ]);
    $saleTransaction = SaleTransaction::with('saleItems')->whereId($response->json('data.sale_transaction.id'))->first();
    $this->assertEquals(128.2, $saleTransaction->saleItems->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 2 for sale transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 2]);
    $saleInput = SaleTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');
    $saleInput['items'][0]['rpu'] = 100.********;
    $saleInput['items'][0]['rpu_without_gst'] = 100.********;
    $saleInput['items'][0]['rpu_with_gst'] = 100.********;
    $saleInput['items'][0]['gst_id'] = $gst->id;
    $saleInput['items'][0]['gst_tax_percentage'] = 2;
    $saleInput['items'][0]['with_tax'] = false;

    //act
    $response = $this->postJson(route('sale-transaction'), $saleInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('sales_transactions', [
        'id' => $response->json('data.sale_transaction.id'),
        'company_id' => 1,
        'invoice_number' => $response->json('data.sale_transaction.invoice_number'),
        'sales_item_type' => SaleTransaction::ITEM_INVOICE,
    ]);
    $saleTransaction = SaleTransaction::with('saleItems')->whereId($response->json('data.sale_transaction.id'))->first();
    $this->assertEquals(128.16, $saleTransaction->saleItems->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 3 for sale transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 3]);
    $saleInput = SaleTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');
    $saleInput['items'][0]['rpu'] = 100.********;
    $saleInput['items'][0]['rpu_without_gst'] = 100.********;
    $saleInput['items'][0]['rpu_with_gst'] = 100.********;
    $saleInput['items'][0]['gst_id'] = $gst->id;
    $saleInput['items'][0]['gst_tax_percentage'] = 2;
    $saleInput['items'][0]['with_tax'] = false;

    //act
    $response = $this->postJson(route('sale-transaction'), $saleInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('sales_transactions', [
        'id' => $response->json('data.sale_transaction.id'),
        'company_id' => 1,
        'invoice_number' => $response->json('data.sale_transaction.invoice_number'),
        'sales_item_type' => SaleTransaction::ITEM_INVOICE,
    ]);
    $saleTransaction = SaleTransaction::with('saleItems')->whereId($response->json('data.sale_transaction.id'))->first();
    $this->assertEquals(128.155, $saleTransaction->saleItems->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 4 for sale transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 4]);
    $saleInput = SaleTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');
    $saleInput['items'][0]['rpu'] = 100.********;
    $saleInput['items'][0]['rpu_without_gst'] = 100.********;
    $saleInput['items'][0]['rpu_with_gst'] = 100.********;
    $saleInput['items'][0]['gst_id'] = $gst->id;
    $saleInput['items'][0]['gst_tax_percentage'] = 2;
    $saleInput['items'][0]['with_tax'] = false;

    //act
    $response = $this->postJson(route('sale-transaction'), $saleInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('sales_transactions', [
        'id' => $response->json('data.sale_transaction.id'),
        'company_id' => 1,
        'invoice_number' => $response->json('data.sale_transaction.invoice_number'),
        'sales_item_type' => SaleTransaction::ITEM_INVOICE,
    ]);
    $saleTransaction = SaleTransaction::with('saleItems')->whereId($response->json('data.sale_transaction.id'))->first();
    $this->assertEquals(128.1552, $saleTransaction->saleItems->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 5 for sale transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 5]);
    $saleInput = SaleTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');
    $saleInput['items'][0]['rpu'] = 100.********;
    $saleInput['items'][0]['rpu_without_gst'] = 100.********;
    $saleInput['items'][0]['rpu_with_gst'] = 100.********;
    $saleInput['items'][0]['gst_id'] = $gst->id;
    $saleInput['items'][0]['gst_tax_percentage'] = 2;
    $saleInput['items'][0]['with_tax'] = false;

    //act
    $response = $this->postJson(route('sale-transaction'), $saleInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('sales_transactions', [
        'id' => $response->json('data.sale_transaction.id'),
        'company_id' => 1,
        'invoice_number' => $response->json('data.sale_transaction.invoice_number'),
        'sales_item_type' => SaleTransaction::ITEM_INVOICE,
    ]);
    $saleTransaction = SaleTransaction::with('saleItems')->whereId($response->json('data.sale_transaction.id'))->first();
    $this->assertEquals(128.15515, $saleTransaction->saleItems->first()->rpu_with_gst);
});

test('validation with update negative and wrong rpu without or with gst for sale transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $saleInput = SaleTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');
    $saleInput['items'][0]['rpu_without_gst'] = -10000;
    $saleInput['items'][0]['rpu_with_gst'] = -10000;
    $saleInput['items'][0]['gst_id'] = $gst->id;
    $saleInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('sale-transaction'), $saleInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('sales_transactions', [
        'id' => $response->json('data.sale_transaction.id'),
        'company_id' => 1,
        'invoice_number' => $response->json('data.sale_transaction.invoice_number'),
        'sales_item_type' => SaleTransaction::ITEM_INVOICE,
    ]);
    $saleTransaction = SaleTransaction::with('saleItems')->whereId($response->json('data.sale_transaction.id'))->first();
    $this->assertNotEquals(100, $saleTransaction->saleItems->first()->rpu_without_gst);
    $this->assertEquals(100.0, $saleTransaction->saleItems->first()->rpu_with_gst);
});

test('validation with update negative and wrong gst tax percentage for sale transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $saleInput = SaleTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');
    $saleInput['items'][0]['total'] = -10000;
    $saleInput['items'][0]['gst_id'] = $gst->id;
    $saleInput['items'][0]['gst_tax_percentage'] = $gst->tax_rate;

    //act
    $response = $this->postJson(route('sale-transaction'), $saleInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('sales_transactions', [
        'id' => $response->json('data.sale_transaction.id'),
        'company_id' => 1,
        'invoice_number' => $response->json('data.sale_transaction.invoice_number'),
        'sales_item_type' => SaleTransaction::ITEM_INVOICE,
    ]);
    $saleTransaction = SaleTransaction::with('saleItems')->whereId($response->json('data.sale_transaction.id'))->first();
    $this->assertEquals(28, intval($saleTransaction->saleItems->first()->gst_tax_percentage));
});

test('validation with update negative and wrong total discount for sale transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(5.00)->first();

    $saleInput = SaleTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');
    foreach ($saleInput['items'] as $key => $item) {
        $saleInput['items'][$key]['gst_id'] = $gst->id;
        $saleInput['items'][$key]['discount_type'] = 2;
        $saleInput['items'][$key]['discount_value'] = 10;
        $saleInput['items'][$key]['discount_type_2'] = 2;
        $saleInput['items'][$key]['discount_value_2'] = 10;
        $saleInput['items'][$key]['total_discount_amount'] = 10;
    }

    //act
    $response = $this->postJson(route('sale-transaction'), $saleInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('sales_transactions', [
        'id' => $response->json('data.sale_transaction.id'),
        'company_id' => 1,
        'invoice_number' => $response->json('data.sale_transaction.invoice_number'),
        'sales_item_type' => SaleTransaction::ITEM_INVOICE,
    ]);
    $saleTransaction = SaleTransaction::with('saleItems')->whereId($response->json('data.sale_transaction.id'))->first();
    foreach ($saleTransaction->saleItems as $item) {
        $this->assertEquals(18.1, $item->total_discount_amount);
    }
});

test('validation with update negative and wrong sub total for sale transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $saleInput = SaleTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');
    $saleInput['items'][0]['total'] = -10000;
    $saleInput['items'][0]['gst_id'] = $gst->id;
    $saleInput['items'][0]['gst_tax_percentage'] = $gst->tax_rate;

    //act
    $response = $this->postJson(route('sale-transaction'), $saleInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('sales_transactions', [
        'id' => $response->json('data.sale_transaction.id'),
        'company_id' => 1,
        'invoice_number' => $response->json('data.sale_transaction.invoice_number'),
        'sales_item_type' => SaleTransaction::ITEM_INVOICE,
    ]);
    $saleTransaction = SaleTransaction::with('saleItems')->whereId($response->json('data.sale_transaction.id'))->first();
    $this->assertEquals(78.13, $saleTransaction->saleItems->first()->total);
});

test('validation with update negative and wrong sub total for sale transaction item invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $saleInput = SaleTransactionFactory::new()->make()->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');
    $saleInput['items'][0]['total'] = -10000;

    //act
    $response = $this->postJson(route('sale-transaction'), $saleInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('sales_transactions', [
        'id' => $response->json('data.sale_transaction.id'),
        'company_id' => 1,
        'invoice_number' => $response->json('data.sale_transaction.invoice_number'),
        'sales_item_type' => SaleTransaction::ITEM_INVOICE,
    ]);
    $saleTransaction = SaleTransaction::with('saleItems')->whereId($response->json('data.sale_transaction.id'))->first();
    $this->assertEquals(100, intval($saleTransaction->saleItems->first()->total));
});

test('validation with update negative and wrong total for sale transaction item invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $saleInput = SaleTransactionFactory::new()->make(['total' => -620])->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = $this->postJson(route('sale-transaction'), $saleInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('sales_transactions', [
        'id' => $response->json('data.sale_transaction.id'),
        'company_id' => 1,
        'invoice_number' => $response->json('data.sale_transaction.invoice_number'),
        'sales_item_type' => SaleTransaction::ITEM_INVOICE,
        'total' => 620.00,
    ]);
});

test('validation with update negative and wrong gross value for sale transaction item invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $saleInput = SaleTransactionFactory::new()->make(['gross_value' => -100])->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = $this->postJson(route('sale-transaction'), $saleInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('sales_transactions', [
        'id' => $response->json('data.sale_transaction.id'),
        'company_id' => 1,
        'invoice_number' => $response->json('data.sale_transaction.invoice_number'),
        'sales_item_type' => SaleTransaction::ITEM_INVOICE,
        'gross_value' => 600.00,
    ]);
});

test('validation with update negative and wrong additional charges gst tax percentage for sale transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $saleInput = SaleTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');
    $saleInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    $saleInput['additional_charges'][0]['ac_total'] = -2000;

    //act
    $response = $this->postJson(route('sale-transaction'), $saleInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('sales_transactions', [
        'id' => $response->json('data.sale_transaction.id'),
        'company_id' => 1,
        'invoice_number' => $response->json('data.sale_transaction.invoice_number'),
        'sales_item_type' => SaleTransaction::ITEM_INVOICE,
    ]);
    $saleTransaction = SaleTransaction::with('additionalCharges')->whereId($response->json('data.sale_transaction.id'))->first();
    $this->assertNotEquals(10.00, $saleTransaction->additionalCharges->first()->total);
});

test('validation with update negative and wrong additional charges total for sale transaction item invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $saleInput = SaleTransactionFactory::new()->make()->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');
    $saleInput['additional_charges'][0]['ac_total'] = -200;

    //act
    $response = $this->postJson(route('sale-transaction'), $saleInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('sales_transactions', [
        'id' => $response->json('data.sale_transaction.id'),
        'company_id' => 1,
        'invoice_number' => $response->json('data.sale_transaction.invoice_number'),
        'sales_item_type' => SaleTransaction::ITEM_INVOICE,
        'gross_value' => 600.00,
    ]);
    $saleTransaction = SaleTransaction::with('additionalCharges')->whereId($response->json('data.sale_transaction.id'))->first();
    $this->assertEquals(10, intval($saleTransaction->additionalCharges->first()->total));
});

test('validation with update negative and wrong cgst and sgst when sale intra state taxable classification for sale transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $saleInput = SaleTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'main_classification_nature_type' => 'Sale Intra State Taxable',
    ])->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');
    $saleInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    foreach ($saleInput['items'] as $key => $item) {
        $saleInput['items'][$key]['gst_id'] = $gst->id;
    }

    //act
    $response = $this->postJson(route('sale-transaction'), $saleInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('sales_transactions', [
        'id' => $response->json('data.sale_transaction.id'),
        'company_id' => 1,
        'invoice_number' => $response->json('data.sale_transaction.invoice_number'),
        'sales_item_type' => SaleTransaction::ITEM_INVOICE,
    ]);
    $saleTransaction = SaleTransaction::whereId($response->json('data.sale_transaction.id'))->first();
    $this->assertEquals(67.04, $saleTransaction->cgst);
    $this->assertEquals(67.04, $saleTransaction->sgst);
});

test('validation with update negative and wrong igst when sale inter state taxable classification for sale transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $saleInput = SaleTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'main_classification_nature_type' => 'Sale Inter State Taxable',
    ])->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');
    $saleInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    foreach ($saleInput['items'] as $key => $item) {
        $saleInput['items'][$key]['gst_id'] = $gst->id;
    }

    //act
    $response = $this->postJson(route('sale-transaction'), $saleInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('sales_transactions', [
        'id' => $response->json('data.sale_transaction.id'),
        'company_id' => 1,
        'invoice_number' => $response->json('data.sale_transaction.invoice_number'),
        'sales_item_type' => SaleTransaction::ITEM_INVOICE,
    ]);
    $saleTransaction = SaleTransaction::whereId($response->json('data.sale_transaction.id'))->first();
    $this->assertEquals(134.08, $saleTransaction->igst);
});

test('validation with update negative and wrong cgst when deemed export - intrastate classification for sale transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $saleInput = SaleTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'main_classification_nature_type' => 'Deemed Export - Intrastate',
    ])->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');
    $saleInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    foreach ($saleInput['items'] as $key => $item) {
        $saleInput['items'][$key]['gst_id'] = $gst->id;
    }

    //act
    $response = $this->postJson(route('sale-transaction'), $saleInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('sales_transactions', [
        'id' => $response->json('data.sale_transaction.id'),
        'company_id' => 1,
        'invoice_number' => $response->json('data.sale_transaction.invoice_number'),
        'sales_item_type' => SaleTransaction::ITEM_INVOICE,
    ]);
    $saleTransaction = SaleTransaction::whereId($response->json('data.sale_transaction.id'))->first();
    $this->assertEquals(67.04, $saleTransaction->cgst);
});

test('validation with update negative and wrong igst when deemed export - interstate classification for sale transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $saleInput = SaleTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'main_classification_nature_type' => 'Deemed Export - Interstate',
    ])->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');
    $saleInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    foreach ($saleInput['items'] as $key => $item) {
        $saleInput['items'][$key]['gst_id'] = $gst->id;
    }

    //act
    $response = $this->postJson(route('sale-transaction'), $saleInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('sales_transactions', [
        'id' => $response->json('data.sale_transaction.id'),
        'company_id' => 1,
        'invoice_number' => $response->json('data.sale_transaction.invoice_number'),
        'sales_item_type' => SaleTransaction::ITEM_INVOICE,
    ]);
    $saleTransaction = SaleTransaction::whereId($response->json('data.sale_transaction.id'))->first();
    $this->assertEquals(134.08, $saleTransaction->igst);
});

test('validation with update negative and wrong taxable value for sale transaction item invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $saleInput = SaleTransactionFactory::new()->make(['taxable_value' => -100])->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = $this->postJson(route('sale-transaction'), $saleInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('sales_transactions', [
        'id' => $response->json('data.sale_transaction.id'),
        'company_id' => 1,
        'invoice_number' => $response->json('data.sale_transaction.invoice_number'),
        'sales_item_type' => SaleTransaction::ITEM_INVOICE,
        'taxable_value' => 610.00,
    ]);
});

test('validation with update negative and wrong addless total for sale transaction item invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $saleInput = SaleTransactionFactory::new()->make()->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');
    $saleInput['add_less'][0]['al_total'] = -100;

    //act
    $response = $this->postJson(route('sale-transaction'), $saleInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('sales_transactions', [
        'id' => $response->json('data.sale_transaction.id'),
        'company_id' => 1,
        'invoice_number' => $response->json('data.sale_transaction.invoice_number'),
        'sales_item_type' => SaleTransaction::ITEM_INVOICE,
        'gross_value' => 600.00,
    ]);
    $saleTransaction = SaleTransaction::with('additionalCharges')->whereId($response->json('data.sale_transaction.id'))->first();
    $this->assertEquals(10, intval($saleTransaction->addLess->first()->total));
});

test('validation with update negative and wrong grand total for sale transaction item invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $saleInput = SaleTransactionFactory::new()->make(['grand_total' => -100])->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = $this->postJson(route('sale-transaction'), $saleInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('sales_transactions', [
        'id' => $response->json('data.sale_transaction.id'),
        'company_id' => 1,
        'invoice_number' => $response->json('data.sale_transaction.invoice_number'),
        'sales_item_type' => SaleTransaction::ITEM_INVOICE,
        'grand_total' => 620.00,
    ]);
});

/*
|----------------------------------------------------------------------------------------------------------------------------------------------------
| Sale Transaction Account Invoice Backend Validation with update Test Case
|----------------------------------------------------------------------------------------------------------------------------------------------------
*/

test('validation with update decimal rpu amount when decimal place is 1 for sale transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $saleInput = SaleTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'sales_item_type' => SaleTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');
    $saleInput['ledgers'][0]['decimal'] = 1;
    $saleInput['ledgers'][0]['rpu'] = 100.********;
    $saleInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $saleInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $saleInput['ledgers'][0]['gst_id'] = $gst->id;
    $saleInput['ledgers'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('sale-transaction'), $saleInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('sales_transactions', [
        'id' => $response->json('data.sale_transaction.id'),
        'company_id' => 1,
        'invoice_number' => $response->json('data.sale_transaction.invoice_number'),
        'sales_item_type' => SaleTransaction::ACCOUNTING_INVOICE,
    ]);
    $saleTransaction = SaleTransaction::with('saleLedgers')->whereId($response->json('data.sale_transaction.id'))->first();
    $this->assertEquals(78.2, $saleTransaction->saleLedgers->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 2 for sale transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $saleInput = SaleTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'sales_item_type' => SaleTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');
    $saleInput['ledgers'][0]['decimal'] = 2;
    $saleInput['ledgers'][0]['rpu'] = 100.********;
    $saleInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $saleInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $saleInput['ledgers'][0]['gst_id'] = $gst->id;
    $saleInput['ledgers'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('sale-transaction'), $saleInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('sales_transactions', [
        'id' => $response->json('data.sale_transaction.id'),
        'company_id' => 1,
        'invoice_number' => $response->json('data.sale_transaction.invoice_number'),
        'sales_item_type' => SaleTransaction::ACCOUNTING_INVOICE,
    ]);
    $saleTransaction = SaleTransaction::with('saleLedgers')->whereId($response->json('data.sale_transaction.id'))->first();
    $this->assertEquals(78.22, $saleTransaction->saleLedgers->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 3 for sale transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $saleInput = SaleTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'sales_item_type' => SaleTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');
    $saleInput['ledgers'][0]['decimal'] = 3;
    $saleInput['ledgers'][0]['rpu'] = 100.********;
    $saleInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $saleInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $saleInput['ledgers'][0]['gst_id'] = $gst->id;
    $saleInput['ledgers'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('sale-transaction'), $saleInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('sales_transactions', [
        'id' => $response->json('data.sale_transaction.id'),
        'company_id' => 1,
        'invoice_number' => $response->json('data.sale_transaction.invoice_number'),
        'sales_item_type' => SaleTransaction::ACCOUNTING_INVOICE,
    ]);
    $saleTransaction = SaleTransaction::with('saleLedgers')->whereId($response->json('data.sale_transaction.id'))->first();
    $this->assertEquals(78.220, $saleTransaction->saleLedgers->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 4 for sale transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $saleInput = SaleTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'sales_item_type' => SaleTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');
    $saleInput['ledgers'][0]['decimal'] = 4;
    $saleInput['ledgers'][0]['rpu'] = 100.********;
    $saleInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $saleInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $saleInput['ledgers'][0]['gst_id'] = $gst->id;
    $saleInput['ledgers'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('sale-transaction'), $saleInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('sales_transactions', [
        'id' => $response->json('data.sale_transaction.id'),
        'company_id' => 1,
        'invoice_number' => $response->json('data.sale_transaction.invoice_number'),
        'sales_item_type' => SaleTransaction::ACCOUNTING_INVOICE,
    ]);
    $saleTransaction = SaleTransaction::with('saleLedgers')->whereId($response->json('data.sale_transaction.id'))->first();
    $this->assertEquals(78.2197, $saleTransaction->saleLedgers->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 5 for sale transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $saleInput = SaleTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'sales_item_type' => SaleTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');
    $saleInput['ledgers'][0]['decimal'] = 5;
    $saleInput['ledgers'][0]['rpu'] = 100.********;
    $saleInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $saleInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $saleInput['ledgers'][0]['gst_id'] = $gst->id;
    $saleInput['ledgers'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('sale-transaction'), $saleInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('sales_transactions', [
        'id' => $response->json('data.sale_transaction.id'),
        'company_id' => 1,
        'invoice_number' => $response->json('data.sale_transaction.invoice_number'),
        'sales_item_type' => SaleTransaction::ACCOUNTING_INVOICE,
    ]);
    $saleTransaction = SaleTransaction::with('saleLedgers')->whereId($response->json('data.sale_transaction.id'))->first();
    $this->assertEquals(78.21970, $saleTransaction->saleLedgers->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 1 for sale transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 1]);
    $saleInput = SaleTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'sales_item_type' => SaleTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');
    $saleInput['ledgers'][0]['decimal'] = 1;
    $saleInput['ledgers'][0]['rpu'] = 100.********;
    $saleInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $saleInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $saleInput['ledgers'][0]['gst_id'] = $gst->id;
    $saleInput['ledgers'][0]['gst_tax_percentage'] = 2;
    $saleInput['ledgers'][0]['with_tax'] = false;

    //act
    $response = $this->postJson(route('sale-transaction'), $saleInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('sales_transactions', [
        'id' => $response->json('data.sale_transaction.id'),
        'company_id' => 1,
        'invoice_number' => $response->json('data.sale_transaction.invoice_number'),
        'sales_item_type' => SaleTransaction::ACCOUNTING_INVOICE,
    ]);
    $saleTransaction = SaleTransaction::with('saleLedgers')->whereId($response->json('data.sale_transaction.id'))->first();
    $this->assertEquals(128.2, $saleTransaction->saleLedgers->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 2 for sale transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $saleInput = SaleTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'sales_item_type' => SaleTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');
    $saleInput['ledgers'][0]['decimal'] = 2;
    $saleInput['ledgers'][0]['rpu'] = 100.********;
    $saleInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $saleInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $saleInput['ledgers'][0]['gst_id'] = $gst->id;
    $saleInput['ledgers'][0]['gst_tax_percentage'] = 2;
    $saleInput['ledgers'][0]['with_tax'] = false;

    //act
    $response = $this->postJson(route('sale-transaction'), $saleInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('sales_transactions', [
        'id' => $response->json('data.sale_transaction.id'),
        'company_id' => 1,
        'invoice_number' => $response->json('data.sale_transaction.invoice_number'),
        'sales_item_type' => SaleTransaction::ACCOUNTING_INVOICE,
    ]);
    $saleTransaction = SaleTransaction::with('saleLedgers')->whereId($response->json('data.sale_transaction.id'))->first();
    $this->assertEquals(128.16, $saleTransaction->saleLedgers->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 3 for sale transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $saleInput = SaleTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'sales_item_type' => SaleTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');
    $saleInput['ledgers'][0]['decimal'] = 3;
    $saleInput['ledgers'][0]['rpu'] = 100.********;
    $saleInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $saleInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $saleInput['ledgers'][0]['gst_id'] = $gst->id;
    $saleInput['ledgers'][0]['gst_tax_percentage'] = 2;
    $saleInput['ledgers'][0]['with_tax'] = false;

    //act
    $response = $this->postJson(route('sale-transaction'), $saleInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('sales_transactions', [
        'id' => $response->json('data.sale_transaction.id'),
        'company_id' => 1,
        'invoice_number' => $response->json('data.sale_transaction.invoice_number'),
        'sales_item_type' => SaleTransaction::ACCOUNTING_INVOICE,
    ]);
    $saleTransaction = SaleTransaction::with('saleLedgers')->whereId($response->json('data.sale_transaction.id'))->first();
    $this->assertEquals(128.155, $saleTransaction->saleLedgers->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 4 for sale transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $saleInput = SaleTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'sales_item_type' => SaleTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');
    $saleInput['ledgers'][0]['decimal'] = 4;
    $saleInput['ledgers'][0]['rpu'] = 100.********;
    $saleInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $saleInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $saleInput['ledgers'][0]['gst_id'] = $gst->id;
    $saleInput['ledgers'][0]['gst_tax_percentage'] = 2;
    $saleInput['ledgers'][0]['with_tax'] = false;

    //act
    $response = $this->postJson(route('sale-transaction'), $saleInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('sales_transactions', [
        'id' => $response->json('data.sale_transaction.id'),
        'company_id' => 1,
        'invoice_number' => $response->json('data.sale_transaction.invoice_number'),
        'sales_item_type' => SaleTransaction::ACCOUNTING_INVOICE,
    ]);
    $saleTransaction = SaleTransaction::with('saleLedgers')->whereId($response->json('data.sale_transaction.id'))->first();
    $this->assertEquals(128.1552, $saleTransaction->saleLedgers->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 5 for sale transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $saleInput = SaleTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'sales_item_type' => SaleTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');
    $saleInput['ledgers'][0]['decimal'] = 5;
    $saleInput['ledgers'][0]['rpu'] = 100.********;
    $saleInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $saleInput['ledgers'][0]['gst_id'] = $gst->id;
    $saleInput['ledgers'][0]['gst_tax_percentage'] = 2;
    $saleInput['ledgers'][0]['with_tax'] = false;

    //act
    $response = $this->postJson(route('sale-transaction'), $saleInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('sales_transactions', [
        'id' => $response->json('data.sale_transaction.id'),
        'company_id' => 1,
        'invoice_number' => $response->json('data.sale_transaction.invoice_number'),
        'sales_item_type' => SaleTransaction::ACCOUNTING_INVOICE,
    ]);
    $saleTransaction = SaleTransaction::with('saleLedgers')->whereId($response->json('data.sale_transaction.id'))->first();
    $this->assertEquals(128.15515, $saleTransaction->saleLedgers->first()->rpu_with_gst);
});

test('validation with update negative and wrong rpu without or with gst for sale transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $saleInput = SaleTransactionFactory::new()->make([
        'sales_item_type' => SaleTransaction::ACCOUNTING_INVOICE,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');
    $saleInput['ledgers'][0]['rpu_without_gst'] = -10000;
    $saleInput['ledgers'][0]['rpu_with_gst'] = -10000;
    $saleInput['ledgers'][0]['gst_id'] = $gst->id;
    $saleInput['ledgers'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('sale-transaction'), $saleInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('sales_transactions', [
        'id' => $response->json('data.sale_transaction.id'),
        'company_id' => 1,
        'invoice_number' => $response->json('data.sale_transaction.invoice_number'),
        'sales_item_type' => SaleTransaction::ACCOUNTING_INVOICE,
    ]);
    $saleTransaction = SaleTransaction::with('saleLedgers')->whereId($response->json('data.sale_transaction.id'))->first();
    $this->assertNotEquals(100, $saleTransaction->saleLedgers->first()->rpu_without_gst);
    $this->assertEquals(100.0, $saleTransaction->saleLedgers->first()->rpu_with_gst);
});

test('validation with update negative and wrong gst tax percentage for sale transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $saleInput = SaleTransactionFactory::new()->make([
        'sales_item_type' => SaleTransaction::ACCOUNTING_INVOICE,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');
    $saleInput['ledgers'][0]['total'] = -10000;
    $saleInput['ledgers'][0]['gst_id'] = $gst->id;
    $saleInput['ledgers'][0]['gst_tax_percentage'] = $gst->tax_rate;

    //act
    $response = $this->postJson(route('sale-transaction'), $saleInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('sales_transactions', [
        'id' => $response->json('data.sale_transaction.id'),
        'company_id' => 1,
        'invoice_number' => $response->json('data.sale_transaction.invoice_number'),
        'sales_item_type' => SaleTransaction::ACCOUNTING_INVOICE,
    ]);
    $saleTransaction = SaleTransaction::with('saleLedgers')->whereId($response->json('data.sale_transaction.id'))->first();
    $this->assertEquals(28, intval($saleTransaction->saleLedgers->first()->gst_tax_percentage));
});

test('validation with update negative and wrong total discount for sale transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(5.00)->first();

    $saleInput = SaleTransactionFactory::new()->make([
        'sales_item_type' => SaleTransaction::ACCOUNTING_INVOICE,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');
    foreach ($saleInput['ledgers'] as $key => $ledger) {
        $saleInput['ledgers'][$key]['gst_id'] = $gst->id;
        $saleInput['ledgers'][$key]['discount_type'] = 2;
        $saleInput['ledgers'][$key]['discount_value'] = 10;
        $saleInput['ledgers'][$key]['discount_type_2'] = 2;
        $saleInput['ledgers'][$key]['discount_value_2'] = 10;
        $saleInput['ledgers'][$key]['total_discount_amount'] = 10;
    }

    //act
    $response = $this->postJson(route('sale-transaction'), $saleInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('sales_transactions', [
        'id' => $response->json('data.sale_transaction.id'),
        'company_id' => 1,
        'invoice_number' => $response->json('data.sale_transaction.invoice_number'),
        'sales_item_type' => SaleTransaction::ACCOUNTING_INVOICE,
    ]);
    $saleTransaction = SaleTransaction::with('saleLedgers')->whereId($response->json('data.sale_transaction.id'))->first();
    foreach ($saleTransaction->saleLedgers as $ledger) {
        $this->assertEquals(18.1, $ledger->total_discount_amount);
    }
});

test('validation with update negative and wrong sub total for sale transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $saleInput = SaleTransactionFactory::new()->make([
        'sales_item_type' => SaleTransaction::ACCOUNTING_INVOICE,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');
    $saleInput['ledgers'][0]['total'] = -10000;
    $saleInput['ledgers'][0]['gst_id'] = $gst->id;
    $saleInput['ledgers'][0]['gst_tax_percentage'] = $gst->tax_rate;

    //act
    $response = $this->postJson(route('sale-transaction'), $saleInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('sales_transactions', [
        'id' => $response->json('data.sale_transaction.id'),
        'company_id' => 1,
        'invoice_number' => $response->json('data.sale_transaction.invoice_number'),
        'sales_item_type' => SaleTransaction::ACCOUNTING_INVOICE,
    ]);
    $saleTransaction = SaleTransaction::with('saleLedgers')->whereId($response->json('data.sale_transaction.id'))->first();
    $this->assertEquals(78.13, $saleTransaction->saleLedgers->first()->total);
});

test('validation with update negative and wrong sub total for sale transaction account invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $saleInput = SaleTransactionFactory::new()->make(['sales_item_type' => SaleTransaction::ACCOUNTING_INVOICE])->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');
    $saleInput['ledgers'][0]['total'] = -10000;

    //act
    $response = $this->postJson(route('sale-transaction'), $saleInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('sales_transactions', [
        'id' => $response->json('data.sale_transaction.id'),
        'company_id' => 1,
        'invoice_number' => $response->json('data.sale_transaction.invoice_number'),
        'sales_item_type' => SaleTransaction::ACCOUNTING_INVOICE,
    ]);
    $saleTransaction = SaleTransaction::with('saleLedgers')->whereId($response->json('data.sale_transaction.id'))->first();
    $this->assertEquals(100, intval($saleTransaction->saleLedgers->first()->total));
});

test('validation with update negative and wrong total for sale transaction account invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $saleInput = SaleTransactionFactory::new()->make(['sales_item_type' => SaleTransaction::ACCOUNTING_INVOICE, 'total' => -620])->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = $this->postJson(route('sale-transaction'), $saleInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('sales_transactions', [
        'id' => $response->json('data.sale_transaction.id'),
        'company_id' => 1,
        'invoice_number' => $response->json('data.sale_transaction.invoice_number'),
        'sales_item_type' => SaleTransaction::ACCOUNTING_INVOICE,
        'total' => 620.00,
    ]);
});

test('validation with update negative and wrong gross value for sale transaction account invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $saleInput = SaleTransactionFactory::new()->make(['sales_item_type' => SaleTransaction::ACCOUNTING_INVOICE, 'gross_value' => -100])->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = $this->postJson(route('sale-transaction'), $saleInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('sales_transactions', [
        'id' => $response->json('data.sale_transaction.id'),
        'company_id' => 1,
        'invoice_number' => $response->json('data.sale_transaction.invoice_number'),
        'sales_item_type' => SaleTransaction::ACCOUNTING_INVOICE,
        'gross_value' => 600.00,
    ]);
});

test('validation with update negative and wrong additional charges gst tax percentage for sale transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $saleInput = SaleTransactionFactory::new()->make([
        'sales_item_type' => SaleTransaction::ACCOUNTING_INVOICE,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');
    $saleInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    $saleInput['additional_charges'][0]['ac_total'] = -2000;

    //act
    $response = $this->postJson(route('sale-transaction'), $saleInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('sales_transactions', [
        'id' => $response->json('data.sale_transaction.id'),
        'company_id' => 1,
        'invoice_number' => $response->json('data.sale_transaction.invoice_number'),
        'sales_item_type' => SaleTransaction::ACCOUNTING_INVOICE,
    ]);
    $saleTransaction = SaleTransaction::with('additionalCharges')->whereId($response->json('data.sale_transaction.id'))->first();
    $this->assertNotEquals(10.00, $saleTransaction->additionalCharges->first()->total);
});

test('validation with update negative and wrong additional chanrges total for sale transaction account invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $saleInput = SaleTransactionFactory::new()->make(['sales_item_type' => SaleTransaction::ACCOUNTING_INVOICE])->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');
    $saleInput['additional_charges'][0]['ac_total'] = -200;

    //act
    $response = $this->postJson(route('sale-transaction'), $saleInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('sales_transactions', [
        'id' => $response->json('data.sale_transaction.id'),
        'company_id' => 1,
        'invoice_number' => $response->json('data.sale_transaction.invoice_number'),
        'sales_item_type' => SaleTransaction::ACCOUNTING_INVOICE,
        'gross_value' => 600.00,
    ]);
    $saleTransaction = SaleTransaction::with('additionalCharges')->whereId($response->json('data.sale_transaction.id'))->first();
    $this->assertEquals(10, intval($saleTransaction->additionalCharges->first()->total));
});

test('validation with update negative and wrong cgst and sgst when sale intra state taxable classification for sale transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $saleInput = SaleTransactionFactory::new()->make([
        'sales_item_type' => SaleTransaction::ACCOUNTING_INVOICE,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'main_classification_nature_type' => 'Sale Intra State Taxable',
    ])->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');
    $saleInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    foreach ($saleInput['ledgers'] as $key => $item) {
        $saleInput['ledgers'][$key]['gst_id'] = $gst->id;
    }

    //act
    $response = $this->postJson(route('sale-transaction'), $saleInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('sales_transactions', [
        'id' => $response->json('data.sale_transaction.id'),
        'company_id' => 1,
        'invoice_number' => $response->json('data.sale_transaction.invoice_number'),
        'sales_item_type' => SaleTransaction::ACCOUNTING_INVOICE,
    ]);
    $saleTransaction = SaleTransaction::whereId($response->json('data.sale_transaction.id'))->first();
    $this->assertEquals(67.04, $saleTransaction->cgst);
    $this->assertEquals(67.04, $saleTransaction->sgst);
});

test('validation with update negative and wrong igst when sale inter state taxable classification for sale transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $saleInput = SaleTransactionFactory::new()->make([
        'sales_item_type' => SaleTransaction::ACCOUNTING_INVOICE,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'main_classification_nature_type' => 'Sale Inter State Taxable',
    ])->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');
    $saleInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    foreach ($saleInput['ledgers'] as $key => $item) {
        $saleInput['ledgers'][$key]['gst_id'] = $gst->id;
    }

    //act
    $response = $this->postJson(route('sale-transaction'), $saleInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('sales_transactions', [
        'id' => $response->json('data.sale_transaction.id'),
        'company_id' => 1,
        'invoice_number' => $response->json('data.sale_transaction.invoice_number'),
        'sales_item_type' => SaleTransaction::ACCOUNTING_INVOICE,
    ]);
    $saleTransaction = SaleTransaction::whereId($response->json('data.sale_transaction.id'))->first();
    $this->assertEquals(134.08, $saleTransaction->igst);
});

test('validation with update negative and wrong cgst when deemed export - intrastate classification for sale transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $saleInput = SaleTransactionFactory::new()->make([
        'sales_item_type' => SaleTransaction::ACCOUNTING_INVOICE,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'main_classification_nature_type' => 'Deemed Export - Intrastate',
    ])->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');
    $saleInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    foreach ($saleInput['ledgers'] as $key => $item) {
        $saleInput['ledgers'][$key]['gst_id'] = $gst->id;
    }

    //act
    $response = $this->postJson(route('sale-transaction'), $saleInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('sales_transactions', [
        'id' => $response->json('data.sale_transaction.id'),
        'company_id' => 1,
        'invoice_number' => $response->json('data.sale_transaction.invoice_number'),
        'sales_item_type' => SaleTransaction::ACCOUNTING_INVOICE,
    ]);
    $saleTransaction = SaleTransaction::whereId($response->json('data.sale_transaction.id'))->first();
    $this->assertEquals(67.04, $saleTransaction->cgst);
});

test('validation with update negative and wrong igst when deemed export - interstate classification for sale transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $saleInput = SaleTransactionFactory::new()->make([
        'sales_item_type' => SaleTransaction::ACCOUNTING_INVOICE,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'main_classification_nature_type' => 'Deemed Export - Interstate',
    ])->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');
    $saleInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    foreach ($saleInput['ledgers'] as $key => $item) {
        $saleInput['ledgers'][$key]['gst_id'] = $gst->id;
    }

    //act
    $response = $this->postJson(route('sale-transaction'), $saleInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('sales_transactions', [
        'id' => $response->json('data.sale_transaction.id'),
        'company_id' => 1,
        'invoice_number' => $response->json('data.sale_transaction.invoice_number'),
        'sales_item_type' => SaleTransaction::ACCOUNTING_INVOICE,
    ]);
    $saleTransaction = SaleTransaction::whereId($response->json('data.sale_transaction.id'))->first();
    $this->assertEquals(134.08, $saleTransaction->igst);
});

test('validation with update negative and wrong taxable value for sale transaction account invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $saleInput = SaleTransactionFactory::new()->make(['sales_item_type' => SaleTransaction::ACCOUNTING_INVOICE, 'taxable_value' => -100])->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = $this->postJson(route('sale-transaction'), $saleInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('sales_transactions', [
        'id' => $response->json('data.sale_transaction.id'),
        'company_id' => 1,
        'invoice_number' => $response->json('data.sale_transaction.invoice_number'),
        'sales_item_type' => SaleTransaction::ACCOUNTING_INVOICE,
        'taxable_value' => 610.00,
    ]);
});

test('validation with update negative and wrong addless total for sale transaction account invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $saleInput = SaleTransactionFactory::new()->make(['sales_item_type' => SaleTransaction::ACCOUNTING_INVOICE])->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');
    $saleInput['add_less'][0]['al_total'] = -100;

    //act
    $response = $this->postJson(route('sale-transaction'), $saleInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('sales_transactions', [
        'id' => $response->json('data.sale_transaction.id'),
        'company_id' => 1,
        'invoice_number' => $response->json('data.sale_transaction.invoice_number'),
        'sales_item_type' => SaleTransaction::ACCOUNTING_INVOICE,
        'gross_value' => 600.00,
    ]);
    $saleTransaction = SaleTransaction::with('additionalCharges')->whereId($response->json('data.sale_transaction.id'))->first();
    $this->assertEquals(10, intval($saleTransaction->addLess->first()->total));
});

test('validation with update negative and wrong grand total for sale transaction account invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $saleInput = SaleTransactionFactory::new()->make(['sales_item_type' => SaleTransaction::ACCOUNTING_INVOICE, 'grand_total' => -100])->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = $this->postJson(route('sale-transaction'), $saleInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('sales_transactions', [
        'id' => $response->json('data.sale_transaction.id'),
        'company_id' => 1,
        'invoice_number' => $response->json('data.sale_transaction.invoice_number'),
        'sales_item_type' => SaleTransaction::ACCOUNTING_INVOICE,
        'grand_total' => 620.00,
    ]);
});

test('validation with update negative and wrong round off amount and method for sale transaction', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);
    $saleInput = SaleTransactionFactory::new()->make()->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');
    $saleInput['additional_charges'][0]['ac_value'] = 10.54;

    $roundOffMethods = [
        ['method' => 1, 'correct_amount' => 0],
        ['method' => 2, 'correct_amount' => -0.54],
        ['method' => 3, 'correct_amount' => 0.46],
        ['method' => 4, 'correct_amount' => 0.46],
    ];
    foreach ($roundOffMethods as $roundOffMethod) {
        $saleInput['invoice_number'] = 'INV-'.$roundOffMethod['method'];
        $saleInput['round_off_method'] = 0;
        IncomeTransactionMaster::whereCompanyId(1)->update(['round_off_method' => $roundOffMethod['method']]);
        $saleInput['rounding_amount'] = -10.50;

        //act
        $response = $this->postJson(route('sale-transaction'), $saleInput);

        //asert
        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Sale Transaction Created Successfully',
            ]);
        $this->assertDatabaseHas('sales_transactions', [
            'id' => $response->json('data.sale_transaction.id'),
            'company_id' => 1,
            'invoice_number' => $response->json('data.sale_transaction.invoice_number'),
            'round_off_method' => $roundOffMethod,
            'rounding_amount' => $roundOffMethod['correct_amount'],
        ]);
    }
});

/*
|----------------------------------------------------------------------------------------------------------------------------------------------------
| Update Sale Transaction Item Invoice Backend Validation with update Test Case
|----------------------------------------------------------------------------------------------------------------------------------------------------
*/

test('validation with update decimal rpu amount when decimal place is 1 for update sale transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 1]);
    $saleInput = SaleTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');
    $saleData = StoreSaleTransactionAction::run($saleInput);
    $saleInput['items'][0]['rpu'] = 100.********;
    $saleInput['items'][0]['rpu_without_gst'] = 100.********;
    $saleInput['items'][0]['rpu_with_gst'] = 100.********;
    $saleInput['items'][0]['gst_id'] = $gst->id;
    $saleInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('sale-transaction-update', $saleData['transaction_id']), $saleInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('sales_transactions', [
        'id' => $saleData['transaction_id'],
        'company_id' => 1,
        'invoice_number' => $response->json('data.sale_transaction.invoice_number'),
        'sales_item_type' => SaleTransaction::ITEM_INVOICE,
    ]);
    $saleTransaction = SaleTransaction::with('saleItems')->whereId($saleData['transaction_id'])->first();
    $this->assertEquals(78.2, $saleTransaction->saleItems->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 2 for update sale transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 2]);
    $saleInput = SaleTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');
    $saleData = StoreSaleTransactionAction::run($saleInput);
    $saleInput['items'][0]['rpu'] = 100.********;
    $saleInput['items'][0]['rpu_without_gst'] = 100.********;
    $saleInput['items'][0]['rpu_with_gst'] = 100.********;
    $saleInput['items'][0]['gst_id'] = $gst->id;
    $saleInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('sale-transaction-update', $saleData['transaction_id']), $saleInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('sales_transactions', [
        'id' => $saleData['transaction_id'],
        'company_id' => 1,
        'invoice_number' => $response->json('data.sale_transaction.invoice_number'),
        'sales_item_type' => SaleTransaction::ITEM_INVOICE,
    ]);
    $saleTransaction = SaleTransaction::with('saleItems')->whereId($saleData['transaction_id'])->first();
    $this->assertEquals(78.22, $saleTransaction->saleItems->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 3 for update sale transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 3]);
    $saleInput = SaleTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');
    $saleData = StoreSaleTransactionAction::run($saleInput);
    $saleInput['items'][0]['rpu'] = 100.********;
    $saleInput['items'][0]['rpu_without_gst'] = 100.********;
    $saleInput['items'][0]['rpu_with_gst'] = 100.********;
    $saleInput['items'][0]['gst_id'] = $gst->id;
    $saleInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('sale-transaction-update', $saleData['transaction_id']), $saleInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('sales_transactions', [
        'id' => $saleData['transaction_id'],
        'company_id' => 1,
        'invoice_number' => $response->json('data.sale_transaction.invoice_number'),
        'sales_item_type' => SaleTransaction::ITEM_INVOICE,
    ]);
    $saleTransaction = SaleTransaction::with('saleItems')->whereId($saleData['transaction_id'])->first();
    $this->assertEquals(78.220, $saleTransaction->saleItems->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 4 for update sale transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 4]);
    $saleInput = SaleTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');
    $saleData = StoreSaleTransactionAction::run($saleInput);
    $saleInput['items'][0]['rpu'] = 100.********;
    $saleInput['items'][0]['rpu_without_gst'] = 100.********;
    $saleInput['items'][0]['rpu_with_gst'] = 100.********;
    $saleInput['items'][0]['gst_id'] = $gst->id;
    $saleInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('sale-transaction-update', $saleData['transaction_id']), $saleInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('sales_transactions', [
        'id' => $saleData['transaction_id'],
        'company_id' => 1,
        'invoice_number' => $response->json('data.sale_transaction.invoice_number'),
        'sales_item_type' => SaleTransaction::ITEM_INVOICE,
    ]);
    $saleTransaction = SaleTransaction::with('saleItems')->whereId($saleData['transaction_id'])->first();
    $this->assertEquals(78.2197, $saleTransaction->saleItems->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 5 for update sale transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 5]);
    $saleInput = SaleTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');
    $saleData = StoreSaleTransactionAction::run($saleInput);
    $saleInput['items'][0]['rpu'] = 100.********;
    $saleInput['items'][0]['rpu_without_gst'] = 100.********;
    $saleInput['items'][0]['rpu_with_gst'] = 100.********;
    $saleInput['items'][0]['gst_id'] = $gst->id;
    $saleInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('sale-transaction-update', $saleData['transaction_id']), $saleInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('sales_transactions', [
        'id' => $saleData['transaction_id'],
        'company_id' => 1,
        'invoice_number' => $response->json('data.sale_transaction.invoice_number'),
        'sales_item_type' => SaleTransaction::ITEM_INVOICE,
    ]);
    $saleTransaction = SaleTransaction::with('saleItems')->whereId($saleData['transaction_id'])->first();
    $this->assertEquals(78.21970, $saleTransaction->saleItems->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 1 for update sale transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 1]);
    $saleInput = SaleTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');
    $saleData = StoreSaleTransactionAction::run($saleInput);
    $saleInput['items'][0]['rpu'] = 100.********;
    $saleInput['items'][0]['rpu_without_gst'] = 100.********;
    $saleInput['items'][0]['rpu_with_gst'] = 100.********;
    $saleInput['items'][0]['gst_id'] = $gst->id;
    $saleInput['items'][0]['gst_tax_percentage'] = 2;
    $saleInput['items'][0]['with_tax'] = false;

    //act
    $response = $this->postJson(route('sale-transaction-update', $saleData['transaction_id']), $saleInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('sales_transactions', [
        'id' => $saleData['transaction_id'],
        'company_id' => 1,
        'invoice_number' => $response->json('data.sale_transaction.invoice_number'),
        'sales_item_type' => SaleTransaction::ITEM_INVOICE,
    ]);
    $saleTransaction = SaleTransaction::with('saleItems')->whereId($saleData['transaction_id'])->first();
    $this->assertEquals(128.2, $saleTransaction->saleItems->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 2 for update sale transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 2]);
    $saleInput = SaleTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');
    $saleData = StoreSaleTransactionAction::run($saleInput);
    $saleInput['items'][0]['rpu'] = 100.********;
    $saleInput['items'][0]['rpu_without_gst'] = 100.********;
    $saleInput['items'][0]['rpu_with_gst'] = 100.********;
    $saleInput['items'][0]['gst_id'] = $gst->id;
    $saleInput['items'][0]['gst_tax_percentage'] = 2;
    $saleInput['items'][0]['with_tax'] = false;

    //act
    $response = $this->postJson(route('sale-transaction-update', $saleData['transaction_id']), $saleInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('sales_transactions', [
        'id' => $saleData['transaction_id'],
        'company_id' => 1,
        'invoice_number' => $response->json('data.sale_transaction.invoice_number'),
        'sales_item_type' => SaleTransaction::ITEM_INVOICE,
    ]);
    $saleTransaction = SaleTransaction::with('saleItems')->whereId($saleData['transaction_id'])->first();
    $this->assertEquals(128.16, $saleTransaction->saleItems->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 3 for update sale transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 3]);
    $saleInput = SaleTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');
    $saleData = StoreSaleTransactionAction::run($saleInput);
    $saleInput['items'][0]['rpu'] = 100.********;
    $saleInput['items'][0]['rpu_without_gst'] = 100.********;
    $saleInput['items'][0]['rpu_with_gst'] = 100.********;
    $saleInput['items'][0]['gst_id'] = $gst->id;
    $saleInput['items'][0]['gst_tax_percentage'] = 2;
    $saleInput['items'][0]['with_tax'] = false;

    //act
    $response = $this->postJson(route('sale-transaction-update', $saleData['transaction_id']), $saleInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('sales_transactions', [
        'id' => $saleData['transaction_id'],
        'company_id' => 1,
        'invoice_number' => $response->json('data.sale_transaction.invoice_number'),
        'sales_item_type' => SaleTransaction::ITEM_INVOICE,
    ]);
    $saleTransaction = SaleTransaction::with('saleItems')->whereId($saleData['transaction_id'])->first();
    $this->assertEquals(128.155, $saleTransaction->saleItems->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 4 for update sale transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 4]);
    $saleInput = SaleTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');
    $saleData = StoreSaleTransactionAction::run($saleInput);
    $saleInput['items'][0]['rpu'] = 100.********;
    $saleInput['items'][0]['rpu_without_gst'] = 100.********;
    $saleInput['items'][0]['rpu_with_gst'] = 100.********;
    $saleInput['items'][0]['gst_id'] = $gst->id;
    $saleInput['items'][0]['gst_tax_percentage'] = 2;
    $saleInput['items'][0]['with_tax'] = false;

    //act
    $response = $this->postJson(route('sale-transaction-update', $saleData['transaction_id']), $saleInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('sales_transactions', [
        'id' => $saleData['transaction_id'],
        'company_id' => 1,
        'invoice_number' => $response->json('data.sale_transaction.invoice_number'),
        'sales_item_type' => SaleTransaction::ITEM_INVOICE,
    ]);
    $saleTransaction = SaleTransaction::with('saleItems')->whereId($saleData['transaction_id'])->first();
    $this->assertEquals(128.1552, $saleTransaction->saleItems->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 5 for update sale transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 5]);
    $saleInput = SaleTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');
    $saleData = StoreSaleTransactionAction::run($saleInput);
    $saleInput['items'][0]['rpu'] = 100.********;
    $saleInput['items'][0]['rpu_without_gst'] = 100.********;
    $saleInput['items'][0]['rpu_with_gst'] = 100.********;
    $saleInput['items'][0]['gst_id'] = $gst->id;
    $saleInput['items'][0]['gst_tax_percentage'] = 2;
    $saleInput['items'][0]['with_tax'] = false;

    //act
    $response = $this->postJson(route('sale-transaction-update', $saleData['transaction_id']), $saleInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('sales_transactions', [
        'id' => $saleData['transaction_id'],
        'company_id' => 1,
        'invoice_number' => $response->json('data.sale_transaction.invoice_number'),
        'sales_item_type' => SaleTransaction::ITEM_INVOICE,
    ]);
    $saleTransaction = SaleTransaction::with('saleItems')->whereId($saleData['transaction_id'])->first();
    $this->assertEquals(128.15515, $saleTransaction->saleItems->first()->rpu_with_gst);
});

test('validation with update negative and wrong rpu without or with gst for update sale transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);

    $saleInput = SaleTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');
    $saleData = StoreSaleTransactionAction::run($saleInput);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $saleInput['items'][0]['rpu_without_gst'] = -10000;
    $saleInput['items'][0]['rpu_with_gst'] = -10000;
    $saleInput['items'][0]['gst_id'] = $gst->id;
    $saleInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('sale-transaction-update', $saleData['transaction_id']), $saleInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('sales_transactions', [
        'id' => $saleData['transaction_id'],
        'company_id' => 1,
        'invoice_number' => $response->json('data.sale_transaction.invoice_number'),
        'sales_item_type' => SaleTransaction::ITEM_INVOICE,
    ]);
    $saleTransaction = SaleTransaction::with('saleItems')->whereId($saleData['transaction_id'])->first();
    $this->assertNotEquals(100, $saleTransaction->saleItems->first()->rpu_without_gst);
    $this->assertEquals(100.0, $saleTransaction->saleItems->first()->rpu_with_gst);
});

test('validation with update negative and wrong gst tax percentage for update sale transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);

    $saleInput = SaleTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');
    $saleData = StoreSaleTransactionAction::run($saleInput);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $saleInput['items'][0]['total'] = -10000;
    $saleInput['items'][0]['gst_id'] = $gst->id;
    $saleInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('sale-transaction-update', $saleData['transaction_id']), $saleInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('sales_transactions', [
        'id' => $saleData['transaction_id'],
        'company_id' => 1,
        'invoice_number' => $response->json('data.sale_transaction.invoice_number'),
        'sales_item_type' => SaleTransaction::ITEM_INVOICE,
    ]);
    $saleTransaction = SaleTransaction::with('saleItems')->whereId($saleData['transaction_id'])->first();
    $this->assertEquals(28, intval($saleTransaction->saleItems->first()->gst_tax_percentage));
});

test('validation with update negative and wrong total discount for update sale transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(5.00)->first();
    $saleInput = SaleTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');
    $saleData = StoreSaleTransactionAction::run($saleInput);

    foreach ($saleInput['items'] as $key => $ledger) {
        $saleInput['items'][$key]['gst_id'] = $gst->id;
        $saleInput['items'][$key]['discount_type'] = 2;
        $saleInput['items'][$key]['discount_value'] = 10;
        $saleInput['items'][$key]['discount_type_2'] = 2;
        $saleInput['items'][$key]['discount_value_2'] = 10;
        $saleInput['items'][$key]['total_discount_amount'] = 10;
    }

    //act
    $response = $this->postJson(route('sale-transaction-update', $saleData['transaction_id']), $saleInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('sales_transactions', [
        'id' => $saleData['transaction_id'],
        'company_id' => 1,
        'invoice_number' => $response->json('data.sale_transaction.invoice_number'),
        'sales_item_type' => SaleTransaction::ITEM_INVOICE,
    ]);
    $saleTransaction = SaleTransaction::with('saleItems')->whereId($saleData['transaction_id'])->first();
    foreach ($saleTransaction->saleItems as $item) {
        $this->assertEquals(18.1, $item->total_discount_amount);
    }
});

test('validation with update negative and wrong sub total for update sale transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);

    $saleInput = SaleTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');
    $saleData = StoreSaleTransactionAction::run($saleInput);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $saleInput['items'][0]['total'] = -10000;
    $saleInput['items'][0]['gst_id'] = $gst->id;
    $saleInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('sale-transaction-update', $saleData['transaction_id']), $saleInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('sales_transactions', [
        'id' => $saleData['transaction_id'],
        'company_id' => 1,
        'invoice_number' => $response->json('data.sale_transaction.invoice_number'),
        'sales_item_type' => SaleTransaction::ITEM_INVOICE,
    ]);
    $saleTransaction = SaleTransaction::with('saleItems')->whereId($saleData['transaction_id'])->first();
    $this->assertEquals(78.13, $saleTransaction->saleItems->first()->total);
});

test('validation with update negative and wrong sub total for update sale transaction item invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $saleInput = SaleTransactionFactory::new()->make()->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');
    $saleData = StoreSaleTransactionAction::run($saleInput);

    $saleInput['items'][0]['total'] = -10000;

    //act
    $response = $this->postJson(route('sale-transaction-update', $saleData['transaction_id']), $saleInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('sales_transactions', [
        'id' => $saleData['transaction_id'],
        'company_id' => 1,
        'invoice_number' => $saleInput['invoice_number'],
        'sales_item_type' => SaleTransaction::ITEM_INVOICE,
    ]);
    $saleTransaction = SaleTransaction::with('saleItems')->whereId($saleData['transaction_id'])->first();
    $this->assertEquals(100, intval($saleTransaction->saleItems->first()->total));
});

test('validation with update negative and wrong total for update sale transaction item invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $saleInput = SaleTransactionFactory::new()->make()->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');
    $saleData = StoreSaleTransactionAction::run($saleInput);

    $saleInput['total'] = -620;

    //act
    $response = $this->postJson(route('sale-transaction-update', $saleData['transaction_id']), $saleInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('sales_transactions', [
        'id' => $saleData['transaction_id'],
        'company_id' => 1,
        'invoice_number' => $saleInput['invoice_number'],
        'sales_item_type' => SaleTransaction::ITEM_INVOICE,
        'total' => 620.00,
    ]);
});

test('validation with update negative and wrong gross value for update sale transaction item invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $saleInput = SaleTransactionFactory::new()->make()->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');
    $saleData = StoreSaleTransactionAction::run($saleInput);

    $saleInput['gross_value'] = -100;

    //act
    $response = $this->postJson(route('sale-transaction-update', $saleData['transaction_id']), $saleInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('sales_transactions', [
        'id' => $saleData['transaction_id'],
        'company_id' => 1,
        'invoice_number' => $saleInput['invoice_number'],
        'sales_item_type' => SaleTransaction::ITEM_INVOICE,
        'gross_value' => 600.00,
    ]);
});

test('validation with update negative and wrong additional charges gst tax percentage for update sale transaction item invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $saleInput = SaleTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');
    $saleData = StoreSaleTransactionAction::run($saleInput);

    $saleInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    $saleInput['additional_charges'][0]['ac_total'] = -2000;

    //act
    $response = $this->postJson(route('sale-transaction-update', $saleData['transaction_id']), $saleInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('sales_transactions', [
        'id' => $saleData['transaction_id'],
        'company_id' => 1,
        'invoice_number' => $saleInput['invoice_number'],
        'sales_item_type' => SaleTransaction::ITEM_INVOICE,
    ]);
    $saleTransaction = SaleTransaction::with('additionalCharges')->whereId($saleData['transaction_id'])->first();
    $this->assertNotEquals(10.00, $saleTransaction->additionalCharges->first()->total);
});

test('validation with update negative and wrong additional chanrges total for update sale transaction item invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $saleInput = SaleTransactionFactory::new()->make()->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');
    $saleData = StoreSaleTransactionAction::run($saleInput);

    $saleInput['additional_charges'][0]['ac_total'] = -200;

    //act
    $response = $this->postJson(route('sale-transaction-update', $saleData['transaction_id']), $saleInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('sales_transactions', [
        'id' => $saleData['transaction_id'],
        'company_id' => 1,
        'invoice_number' => $saleInput['invoice_number'],
        'sales_item_type' => SaleTransaction::ITEM_INVOICE,
        'gross_value' => 600.00,
    ]);
    $saleTransaction = SaleTransaction::with('additionalCharges')->whereId($saleData['transaction_id'])->first();
    $this->assertEquals(10, intval($saleTransaction->additionalCharges->first()->total));
});

test('validation with update negative and wrong cgst and sgst when sale intra state taxable classification for update sale transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $saleInput = SaleTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'main_classification_nature_type' => 'Sale Intra State Taxable',
    ])->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');
    $saleData = StoreSaleTransactionAction::run($saleInput);
    foreach ($saleInput['items'] as $key => $item) {
        $saleInput['items'][$key]['gst_id'] = $gst->id;
    }

    $saleInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;

    //act
    $response = $this->postJson(route('sale-transaction-update', $saleData['transaction_id']), $saleInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('sales_transactions', [
        'id' => $saleData['transaction_id'],
        'company_id' => 1,
        'invoice_number' => $saleInput['invoice_number'],
        'sales_item_type' => SaleTransaction::ITEM_INVOICE,
    ]);
    $saleTransaction = SaleTransaction::with('saleItems')->whereId($response->json('data.sale_transaction.id'))->first();
    $this->assertEquals(67.04, $saleTransaction->cgst);
    $this->assertEquals(67.04, $saleTransaction->sgst);
});

test('validation with update negative and wrong igst when sale inter state taxable classification for update sale transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $saleInput = SaleTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'main_classification_nature_type' => 'Sale Inter State Taxable',
    ])->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');
    $saleData = StoreSaleTransactionAction::run($saleInput);

    $saleInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    foreach ($saleInput['items'] as $key => $item) {
        $saleInput['items'][$key]['gst_id'] = $gst->id;
    }

    //act
    $response = $this->postJson(route('sale-transaction-update', $saleData['transaction_id']), $saleInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('sales_transactions', [
        'id' => $saleData['transaction_id'],
        'company_id' => 1,
        'invoice_number' => $saleInput['invoice_number'],
        'sales_item_type' => SaleTransaction::ITEM_INVOICE,
    ]);
    $saleTransaction = SaleTransaction::whereId($response->json('data.sale_transaction.id'))->first();
    $this->assertEquals(134.08, $saleTransaction->igst);
});

test('validation with update negative and wrong cgst when deemed export - intrastate classification for update sale transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $saleInput = SaleTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'main_classification_nature_type' => 'Deemed Export - Intrastate',
    ])->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');
    $saleData = StoreSaleTransactionAction::run($saleInput);

    $saleInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    foreach ($saleInput['items'] as $key => $item) {
        $saleInput['items'][$key]['gst_id'] = $gst->id;
    }

    //act
    $response = $this->postJson(route('sale-transaction-update', $saleData['transaction_id']), $saleInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('sales_transactions', [
        'id' => $saleData['transaction_id'],
        'company_id' => 1,
        'invoice_number' => $saleInput['invoice_number'],
        'sales_item_type' => SaleTransaction::ITEM_INVOICE,
    ]);
    $saleTransaction = SaleTransaction::whereId($response->json('data.sale_transaction.id'))->first();
    $this->assertEquals(67.04, $saleTransaction->cgst);
});

test('validation with update negative and wrong igst when deemed export - interstate classification for update sale transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $saleInput = SaleTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'main_classification_nature_type' => 'Deemed Export - Interstate',
    ])->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');
    $saleData = StoreSaleTransactionAction::run($saleInput);

    $saleInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    foreach ($saleInput['items'] as $key => $item) {
        $saleInput['items'][$key]['gst_id'] = $gst->id;
    }

    //act
    $response = $this->postJson(route('sale-transaction-update', $saleData['transaction_id']), $saleInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('sales_transactions', [
        'id' => $saleData['transaction_id'],
        'company_id' => 1,
        'invoice_number' => $saleInput['invoice_number'],
        'sales_item_type' => SaleTransaction::ITEM_INVOICE,
    ]);
    $saleTransaction = SaleTransaction::whereId($response->json('data.sale_transaction.id'))->first();
    $this->assertEquals(134.08, $saleTransaction->igst);
});

test('validation with update negative and wrong taxable value for update sale transaction item invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $saleInput = SaleTransactionFactory::new()->make()->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');
    $saleData = StoreSaleTransactionAction::run($saleInput);

    $saleInput['taxable_value'] = -100;

    //act
    $response = $this->postJson(route('sale-transaction-update', $saleData['transaction_id']), $saleInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('sales_transactions', [
        'id' => $saleData['transaction_id'],
        'company_id' => 1,
        'invoice_number' => $saleInput['invoice_number'],
        'sales_item_type' => SaleTransaction::ITEM_INVOICE,
        'taxable_value' => 610.00,
    ]);
});

test('validation with update negative and wrong addless total for update sale transaction item invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $saleInput = SaleTransactionFactory::new()->make()->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');
    $saleData = StoreSaleTransactionAction::run($saleInput);

    $saleInput['add_less'][0]['al_total'] = -100;

    //act
    $response = $this->postJson(route('sale-transaction-update', $saleData['transaction_id']), $saleInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('sales_transactions', [
        'id' => $saleData['transaction_id'],
        'company_id' => 1,
        'invoice_number' => $saleInput['invoice_number'],
        'sales_item_type' => SaleTransaction::ITEM_INVOICE,
        'gross_value' => 600.00,
    ]);
    $saleTransaction = SaleTransaction::with('additionalCharges')->whereId($saleData['transaction_id'])->first();
    $this->assertEquals(10, intval($saleTransaction->addLess->first()->total));
});

test('validation with update negative and wrong grand total for update sale transaction item invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $saleInput = SaleTransactionFactory::new()->make()->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');
    $saleData = StoreSaleTransactionAction::run($saleInput);

    $saleInput['grand_total'] = -100;

    //act
    $response = $this->postJson(route('sale-transaction-update', $saleData['transaction_id']), $saleInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('sales_transactions', [
        'id' => $saleData['transaction_id'],
        'company_id' => 1,
        'invoice_number' => $saleInput['invoice_number'],
        'sales_item_type' => SaleTransaction::ITEM_INVOICE,
        'grand_total' => 620.00,
    ]);
});

/*
|----------------------------------------------------------------------------------------------------------------------------------------------------
| Update Sale Transaction Account Invoice Backend Validation with update Test Case
|----------------------------------------------------------------------------------------------------------------------------------------------------
*/

test('validation with update decimal rpu amount when decimal place is 1 for update sale transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $saleInput = SaleTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'sales_item_type' => SaleTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');
    $saleData = StoreSaleTransactionAction::run($saleInput);
    $saleInput['ledgers'][0]['decimal'] = 1;
    $saleInput['ledgers'][0]['rpu'] = 100.********;
    $saleInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $saleInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $saleInput['ledgers'][0]['gst_id'] = $gst->id;
    $saleInput['ledgers'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('sale-transaction-update', $saleData['transaction_id']), $saleInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('sales_transactions', [
        'id' => $saleData['transaction_id'],
        'company_id' => 1,
        'invoice_number' => $response->json('data.sale_transaction.invoice_number'),
        'sales_item_type' => SaleTransaction::ACCOUNTING_INVOICE,
    ]);
    $saleTransaction = SaleTransaction::with('saleLedgers')->whereId($saleData['transaction_id'])->first();
    $this->assertEquals(78.2, $saleTransaction->saleLedgers->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 2 for update sale transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $saleInput = SaleTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'sales_item_type' => SaleTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');
    $saleData = StoreSaleTransactionAction::run($saleInput);
    $saleInput['ledgers'][0]['decimal'] = 2;
    $saleInput['ledgers'][0]['rpu'] = 100.********;
    $saleInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $saleInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $saleInput['ledgers'][0]['gst_id'] = $gst->id;
    $saleInput['ledgers'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('sale-transaction-update', $saleData['transaction_id']), $saleInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('sales_transactions', [
        'id' => $saleData['transaction_id'],
        'company_id' => 1,
        'invoice_number' => $response->json('data.sale_transaction.invoice_number'),
        'sales_item_type' => SaleTransaction::ACCOUNTING_INVOICE,
    ]);
    $saleTransaction = SaleTransaction::with('saleLedgers')->whereId($saleData['transaction_id'])->first();
    $this->assertEquals(78.22, $saleTransaction->saleLedgers->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 3 for update sale transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $saleInput = SaleTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'sales_item_type' => SaleTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');
    $saleData = StoreSaleTransactionAction::run($saleInput);
    $saleInput['ledgers'][0]['decimal'] = 3;
    $saleInput['ledgers'][0]['rpu'] = 100.********;
    $saleInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $saleInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $saleInput['ledgers'][0]['gst_id'] = $gst->id;
    $saleInput['ledgers'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('sale-transaction-update', $saleData['transaction_id']), $saleInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('sales_transactions', [
        'id' => $saleData['transaction_id'],
        'company_id' => 1,
        'invoice_number' => $response->json('data.sale_transaction.invoice_number'),
        'sales_item_type' => SaleTransaction::ACCOUNTING_INVOICE,
    ]);
    $saleTransaction = SaleTransaction::with('saleLedgers')->whereId($saleData['transaction_id'])->first();
    $this->assertEquals(78.220, $saleTransaction->saleLedgers->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 4 for update sale transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $saleInput = SaleTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'sales_item_type' => SaleTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');
    $saleData = StoreSaleTransactionAction::run($saleInput);
    $saleInput['ledgers'][0]['decimal'] = 4;
    $saleInput['ledgers'][0]['rpu'] = 100.********;
    $saleInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $saleInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $saleInput['ledgers'][0]['gst_id'] = $gst->id;
    $saleInput['ledgers'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('sale-transaction-update', $saleData['transaction_id']), $saleInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('sales_transactions', [
        'id' => $saleData['transaction_id'],
        'company_id' => 1,
        'invoice_number' => $response->json('data.sale_transaction.invoice_number'),
        'sales_item_type' => SaleTransaction::ACCOUNTING_INVOICE,
    ]);
    $saleTransaction = SaleTransaction::with('saleLedgers')->whereId($saleData['transaction_id'])->first();
    $this->assertEquals(78.2197, $saleTransaction->saleLedgers->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 5 for update sale transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $saleInput = SaleTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'sales_item_type' => SaleTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');
    $saleData = StoreSaleTransactionAction::run($saleInput);
    $saleInput['ledgers'][0]['decimal'] = 5;
    $saleInput['ledgers'][0]['rpu'] = 100.********;
    $saleInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $saleInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $saleInput['ledgers'][0]['gst_id'] = $gst->id;
    $saleInput['ledgers'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('sale-transaction-update', $saleData['transaction_id']), $saleInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('sales_transactions', [
        'id' => $response->json('data.sale_transaction.id'),
        'company_id' => 1,
        'invoice_number' => $response->json('data.sale_transaction.invoice_number'),
        'sales_item_type' => SaleTransaction::ACCOUNTING_INVOICE,
    ]);
    $saleTransaction = SaleTransaction::with('saleLedgers')->whereId($saleData['transaction_id'])->first();
    $this->assertEquals(78.21970, $saleTransaction->saleLedgers->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 1 for update sale transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 1]);
    $saleInput = SaleTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'sales_item_type' => SaleTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');
    $saleData = StoreSaleTransactionAction::run($saleInput);
    $saleInput['ledgers'][0]['decimal'] = 1;
    $saleInput['ledgers'][0]['rpu'] = 100.********;
    $saleInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $saleInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $saleInput['ledgers'][0]['gst_id'] = $gst->id;
    $saleInput['ledgers'][0]['gst_tax_percentage'] = 2;
    $saleInput['ledgers'][0]['with_tax'] = false;

    //act
    $response = $this->postJson(route('sale-transaction-update', $saleData['transaction_id']), $saleInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('sales_transactions', [
        'id' => $saleData['transaction_id'],
        'company_id' => 1,
        'invoice_number' => $response->json('data.sale_transaction.invoice_number'),
        'sales_item_type' => SaleTransaction::ACCOUNTING_INVOICE,
    ]);
    $saleTransaction = SaleTransaction::with('saleLedgers')->whereId($saleData['transaction_id'])->first();
    $this->assertEquals(128.2, $saleTransaction->saleLedgers->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 2 for update sale transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $saleInput = SaleTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'sales_item_type' => SaleTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');
    $saleData = StoreSaleTransactionAction::run($saleInput);
    $saleInput['ledgers'][0]['decimal'] = 2;
    $saleInput['ledgers'][0]['rpu'] = 100.********;
    $saleInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $saleInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $saleInput['ledgers'][0]['gst_id'] = $gst->id;
    $saleInput['ledgers'][0]['gst_tax_percentage'] = 2;
    $saleInput['ledgers'][0]['with_tax'] = false;

    //act
    $response = $this->postJson(route('sale-transaction-update', $saleData['transaction_id']), $saleInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('sales_transactions', [
        'id' => $saleData['transaction_id'],
        'company_id' => 1,
        'invoice_number' => $response->json('data.sale_transaction.invoice_number'),
        'sales_item_type' => SaleTransaction::ACCOUNTING_INVOICE,
    ]);
    $saleTransaction = SaleTransaction::with('saleLedgers')->whereId($saleData['transaction_id'])->first();
    $this->assertEquals(128.16, $saleTransaction->saleLedgers->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 3 for update sale transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $saleInput = SaleTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'sales_item_type' => SaleTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');
    $saleData = StoreSaleTransactionAction::run($saleInput);
    $saleInput['ledgers'][0]['decimal'] = 3;
    $saleInput['ledgers'][0]['rpu'] = 100.********;
    $saleInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $saleInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $saleInput['ledgers'][0]['gst_id'] = $gst->id;
    $saleInput['ledgers'][0]['gst_tax_percentage'] = 2;
    $saleInput['ledgers'][0]['with_tax'] = false;

    //act
    $response = $this->postJson(route('sale-transaction-update', $saleData['transaction_id']), $saleInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('sales_transactions', [
        'id' => $saleData['transaction_id'],
        'company_id' => 1,
        'invoice_number' => $response->json('data.sale_transaction.invoice_number'),
        'sales_item_type' => SaleTransaction::ACCOUNTING_INVOICE,
    ]);
    $saleTransaction = SaleTransaction::with('saleLedgers')->whereId($saleData['transaction_id'])->first();
    $this->assertEquals(128.155, $saleTransaction->saleLedgers->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 4 for update sale transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $saleInput = SaleTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'sales_item_type' => SaleTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');
    $saleData = StoreSaleTransactionAction::run($saleInput);
    $saleInput['ledgers'][0]['decimal'] = 4;
    $saleInput['ledgers'][0]['rpu'] = 100.********;
    $saleInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $saleInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $saleInput['ledgers'][0]['gst_id'] = $gst->id;
    $saleInput['ledgers'][0]['gst_tax_percentage'] = 2;
    $saleInput['ledgers'][0]['with_tax'] = false;

    //act
    $response = $this->postJson(route('sale-transaction-update', $saleData['transaction_id']), $saleInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('sales_transactions', [
        'id' => $saleData['transaction_id'],
        'company_id' => 1,
        'invoice_number' => $response->json('data.sale_transaction.invoice_number'),
        'sales_item_type' => SaleTransaction::ACCOUNTING_INVOICE,
    ]);
    $saleTransaction = SaleTransaction::with('saleLedgers')->whereId($saleData['transaction_id'])->first();
    $this->assertEquals(128.1552, $saleTransaction->saleLedgers->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 5 for update sale transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $saleInput = SaleTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'sales_item_type' => SaleTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');
    $saleData = StoreSaleTransactionAction::run($saleInput);
    $saleInput['ledgers'][0]['decimal'] = 5;
    $saleInput['ledgers'][0]['rpu'] = 100.********;
    $saleInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $saleInput['ledgers'][0]['gst_id'] = $gst->id;
    $saleInput['ledgers'][0]['gst_tax_percentage'] = 2;
    $saleInput['ledgers'][0]['with_tax'] = false;

    //act
    $response = $this->postJson(route('sale-transaction-update', $saleData['transaction_id']), $saleInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('sales_transactions', [
        'id' => $saleData['transaction_id'],
        'company_id' => 1,
        'invoice_number' => $response->json('data.sale_transaction.invoice_number'),
        'sales_item_type' => SaleTransaction::ACCOUNTING_INVOICE,
    ]);
    $saleTransaction = SaleTransaction::with('saleLedgers')->whereId($saleData['transaction_id'])->first();
    $this->assertEquals(128.15515, $saleTransaction->saleLedgers->first()->rpu_with_gst);
});

test('validation with update negative and wrong rpu without or with gst for update sale transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);

    $saleInput = SaleTransactionFactory::new()->make([
        'sales_item_type' => SaleTransaction::ACCOUNTING_INVOICE,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');
    $saleData = StoreSaleTransactionAction::run($saleInput);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $saleInput['ledgers'][0]['rpu_without_gst'] = -10000;
    $saleInput['ledgers'][0]['rpu_with_gst'] = -10000;
    $saleInput['ledgers'][0]['gst_id'] = $gst->id;
    $saleInput['ledgers'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('sale-transaction-update', $saleData['transaction_id']), $saleInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('sales_transactions', [
        'id' => $saleData['transaction_id'],
        'company_id' => 1,
        'invoice_number' => $response->json('data.sale_transaction.invoice_number'),
        'sales_item_type' => SaleTransaction::ACCOUNTING_INVOICE,
    ]);
    $saleTransaction = SaleTransaction::with('saleLedgers')->whereId($saleData['transaction_id'])->first();
    $this->assertNotEquals(100, $saleTransaction->saleLedgers->first()->rpu_without_gst);
    $this->assertEquals(100.0, $saleTransaction->saleLedgers->first()->rpu_with_gst);
});

test('validation with update negative and wrong gst tax percentage for update sale transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);

    $saleInput = SaleTransactionFactory::new()->make([
        'sales_item_type' => SaleTransaction::ACCOUNTING_INVOICE,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');
    $saleData = StoreSaleTransactionAction::run($saleInput);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $saleInput['ledgers'][0]['total'] = -10000;
    $saleInput['ledgers'][0]['gst_id'] = $gst->id;
    $saleInput['ledgers'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('sale-transaction-update', $saleData['transaction_id']), $saleInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('sales_transactions', [
        'id' => $saleData['transaction_id'],
        'company_id' => 1,
        'invoice_number' => $response->json('data.sale_transaction.invoice_number'),
        'sales_item_type' => SaleTransaction::ACCOUNTING_INVOICE,
    ]);
    $saleTransaction = SaleTransaction::with('saleLedgers')->whereId($saleData['transaction_id'])->first();
    $this->assertEquals(28, intval($saleTransaction->saleLedgers->first()->gst_tax_percentage));
});

test('validation with update negative and wrong total discount for update sale transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(5.00)->first();
    $saleInput = SaleTransactionFactory::new()->make([
        'sales_item_type' => SaleTransaction::ACCOUNTING_INVOICE,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');
    $saleData = StoreSaleTransactionAction::run($saleInput);

    foreach ($saleInput['ledgers'] as $key => $ledger) {
        $saleInput['ledgers'][$key]['gst_id'] = $gst->id;
        $saleInput['ledgers'][$key]['discount_type'] = 2;
        $saleInput['ledgers'][$key]['discount_value'] = 10;
        $saleInput['ledgers'][$key]['discount_type_2'] = 2;
        $saleInput['ledgers'][$key]['discount_value_2'] = 10;
        $saleInput['ledgers'][$key]['total_discount_amount'] = 10;
    }

    //act
    $response = $this->postJson(route('sale-transaction-update', $saleData['transaction_id']), $saleInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('sales_transactions', [
        'id' => $saleData['transaction_id'],
        'company_id' => 1,
        'invoice_number' => $response->json('data.sale_transaction.invoice_number'),
        'sales_item_type' => SaleTransaction::ACCOUNTING_INVOICE,
    ]);
    $saleTransaction = SaleTransaction::with('saleLedgers')->whereId($saleData['transaction_id'])->first();
    foreach ($saleTransaction->saleLedgers as $ledger) {
        $this->assertEquals(18.1, $ledger->total_discount_amount);
    }
});

test('validation with update negative and wrong sub total for update sale transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);

    $saleInput = SaleTransactionFactory::new()->make([
        'sales_item_type' => SaleTransaction::ACCOUNTING_INVOICE,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');
    $saleData = StoreSaleTransactionAction::run($saleInput);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $saleInput['ledgers'][0]['total'] = -10000;
    $saleInput['ledgers'][0]['gst_id'] = $gst->id;
    $saleInput['ledgers'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('sale-transaction-update', $saleData['transaction_id']), $saleInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('sales_transactions', [
        'id' => $saleData['transaction_id'],
        'company_id' => 1,
        'invoice_number' => $response->json('data.sale_transaction.invoice_number'),
        'sales_item_type' => SaleTransaction::ACCOUNTING_INVOICE,
    ]);
    $saleTransaction = SaleTransaction::with('saleLedgers')->whereId($saleData['transaction_id'])->first();
    $this->assertEquals(78.13, $saleTransaction->saleLedgers->first()->total);
});

test('validation with update negative and wrong sub total for update sale transaction account invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $saleInput = SaleTransactionFactory::new()->make(['sales_item_type' => SaleTransaction::ACCOUNTING_INVOICE])->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');
    $saleData = StoreSaleTransactionAction::run($saleInput);

    $saleInput['ledgers'][0]['total'] = -10000;

    //act
    $response = $this->postJson(route('sale-transaction-update', $saleData['transaction_id']), $saleInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('sales_transactions', [
        'id' => $saleData['transaction_id'],
        'company_id' => 1,
        'invoice_number' => $saleInput['invoice_number'],
        'sales_item_type' => SaleTransaction::ACCOUNTING_INVOICE,
    ]);
    $saleTransaction = SaleTransaction::with('saleLedgers')->whereId($saleData['transaction_id'])->first();
    $this->assertEquals(100, intval($saleTransaction->saleLedgers->first()->total));
});

test('validation with update negative and wrong total for update sale transaction account invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $saleInput = SaleTransactionFactory::new()->make(['sales_item_type' => SaleTransaction::ACCOUNTING_INVOICE])->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');
    $saleData = StoreSaleTransactionAction::run($saleInput);

    $saleInput['total'] = -620;

    //act
    $response = $this->postJson(route('sale-transaction-update', $saleData['transaction_id']), $saleInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('sales_transactions', [
        'id' => $saleData['transaction_id'],
        'company_id' => 1,
        'invoice_number' => $saleInput['invoice_number'],
        'sales_item_type' => SaleTransaction::ACCOUNTING_INVOICE,
        'total' => 620.00,
    ]);
});

test('validation with update negative and wrong gross value for update sale transaction account invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $saleInput = SaleTransactionFactory::new()->make(['sales_item_type' => SaleTransaction::ACCOUNTING_INVOICE])->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');
    $saleData = StoreSaleTransactionAction::run($saleInput);

    $saleInput['gross_value'] = -100;

    //act
    $response = $this->postJson(route('sale-transaction-update', $saleData['transaction_id']), $saleInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('sales_transactions', [
        'id' => $saleData['transaction_id'],
        'company_id' => 1,
        'invoice_number' => $saleInput['invoice_number'],
        'sales_item_type' => SaleTransaction::ACCOUNTING_INVOICE,
        'gross_value' => 600.00,
    ]);
});

test('validation with update negative and wrong additional charges gst tax percentage for update sale transaction account invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $saleInput = SaleTransactionFactory::new()->make([
        'sales_item_type' => SaleTransaction::ACCOUNTING_INVOICE,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');
    $saleData = StoreSaleTransactionAction::run($saleInput);

    $saleInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    $saleInput['additional_charges'][0]['ac_total'] = -2000;

    //act
    $response = $this->postJson(route('sale-transaction-update', $saleData['transaction_id']), $saleInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('sales_transactions', [
        'id' => $saleData['transaction_id'],
        'company_id' => 1,
        'invoice_number' => $saleInput['invoice_number'],
        'sales_item_type' => SaleTransaction::ACCOUNTING_INVOICE,
    ]);
    $saleTransaction = SaleTransaction::with('additionalCharges')->whereId($saleData['transaction_id'])->first();
    $this->assertNotEquals(10.00, $saleTransaction->additionalCharges->first()->total);
});

test('validation with update negative and wrong additional chanrges total for update sale transaction account invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $saleInput = SaleTransactionFactory::new()->make(['sales_item_type' => SaleTransaction::ACCOUNTING_INVOICE])->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');
    $saleData = StoreSaleTransactionAction::run($saleInput);

    $saleInput['additional_charges'][0]['ac_total'] = -200;

    //act
    $response = $this->postJson(route('sale-transaction-update', $saleData['transaction_id']), $saleInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('sales_transactions', [
        'id' => $saleData['transaction_id'],
        'company_id' => 1,
        'invoice_number' => $saleInput['invoice_number'],
        'sales_item_type' => SaleTransaction::ACCOUNTING_INVOICE,
        'gross_value' => 600.00,
    ]);
    $saleTransaction = SaleTransaction::with('additionalCharges')->whereId($saleData['transaction_id'])->first();
    $this->assertEquals(10, intval($saleTransaction->additionalCharges->first()->total));
});

test('validation with update negative and wrong cgst and sgst when sale intra state taxable classification for update sale transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $saleInput = SaleTransactionFactory::new()->make([
        'sales_item_type' => SaleTransaction::ACCOUNTING_INVOICE,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'main_classification_nature_type' => 'Sale Intra State Taxable',
    ])->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');
    $saleData = StoreSaleTransactionAction::run($saleInput);

    $saleInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    foreach ($saleInput['ledgers'] as $key => $item) {
        $saleInput['ledgers'][$key]['gst_id'] = $gst->id;
    }

    //act
    $response = $this->postJson(route('sale-transaction-update', $saleData['transaction_id']), $saleInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('sales_transactions', [
        'id' => $saleData['transaction_id'],
        'company_id' => 1,
        'invoice_number' => $saleInput['invoice_number'],
        'sales_item_type' => SaleTransaction::ACCOUNTING_INVOICE,
    ]);
    $saleTransaction = SaleTransaction::whereId($response->json('data.sale_transaction.id'))->first();
    $this->assertEquals(67.04, $saleTransaction->cgst);
    $this->assertEquals(67.04, $saleTransaction->sgst);
});

test('validation with update negative and wrong igst when sale inter state taxable classification for update sale transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $saleInput = SaleTransactionFactory::new()->make([
        'sales_item_type' => SaleTransaction::ACCOUNTING_INVOICE,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'main_classification_nature_type' => 'Sale Inter State Taxable',
    ])->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');
    $saleData = StoreSaleTransactionAction::run($saleInput);

    $saleInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    foreach ($saleInput['ledgers'] as $key => $item) {
        $saleInput['ledgers'][$key]['gst_id'] = $gst->id;
    }

    //act
    $response = $this->postJson(route('sale-transaction-update', $saleData['transaction_id']), $saleInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('sales_transactions', [
        'id' => $saleData['transaction_id'],
        'company_id' => 1,
        'invoice_number' => $saleInput['invoice_number'],
        'sales_item_type' => SaleTransaction::ACCOUNTING_INVOICE,
    ]);
    $saleTransaction = SaleTransaction::whereId($response->json('data.sale_transaction.id'))->first();
    $this->assertEquals(134.08, $saleTransaction->igst);
});

test('validation with update negative and wrong cgst when deemed export - intrastate classification for update sale transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $saleInput = SaleTransactionFactory::new()->make([
        'sales_item_type' => SaleTransaction::ACCOUNTING_INVOICE,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'main_classification_nature_type' => 'Deemed Export - Intrastate',
    ])->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');
    $saleData = StoreSaleTransactionAction::run($saleInput);

    $saleInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    foreach ($saleInput['ledgers'] as $key => $item) {
        $saleInput['ledgers'][$key]['gst_id'] = $gst->id;
    }

    //act
    $response = $this->postJson(route('sale-transaction-update', $saleData['transaction_id']), $saleInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('sales_transactions', [
        'id' => $saleData['transaction_id'],
        'company_id' => 1,
        'invoice_number' => $saleInput['invoice_number'],
        'sales_item_type' => SaleTransaction::ACCOUNTING_INVOICE,
    ]);
    $saleTransaction = SaleTransaction::whereId($response->json('data.sale_transaction.id'))->first();
    $this->assertEquals(67.04, $saleTransaction->cgst);
});

test('validation with update negative and wrong igst when deemed export - interstate classification for update sale transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $saleInput = SaleTransactionFactory::new()->make([
        'sales_item_type' => SaleTransaction::ACCOUNTING_INVOICE,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'main_classification_nature_type' => 'Deemed Export - Interstate',
    ])->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');
    $saleData = StoreSaleTransactionAction::run($saleInput);

    $saleInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    foreach ($saleInput['ledgers'] as $key => $item) {
        $saleInput['ledgers'][$key]['gst_id'] = $gst->id;
    }

    //act
    $response = $this->postJson(route('sale-transaction-update', $saleData['transaction_id']), $saleInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('sales_transactions', [
        'id' => $saleData['transaction_id'],
        'company_id' => 1,
        'invoice_number' => $saleInput['invoice_number'],
        'sales_item_type' => SaleTransaction::ACCOUNTING_INVOICE,
    ]);
    $saleTransaction = SaleTransaction::whereId($response->json('data.sale_transaction.id'))->first();
    $this->assertEquals(134.08, $saleTransaction->igst);
});

test('validation with update negative and wrong taxable value for update sale transaction account invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $saleInput = SaleTransactionFactory::new()->make(['sales_item_type' => SaleTransaction::ACCOUNTING_INVOICE])->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');
    $saleData = StoreSaleTransactionAction::run($saleInput);

    $saleInput['taxable_value'] = -100;

    //act
    $response = $this->postJson(route('sale-transaction-update', $saleData['transaction_id']), $saleInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('sales_transactions', [
        'id' => $saleData['transaction_id'],
        'company_id' => 1,
        'invoice_number' => $saleInput['invoice_number'],
        'sales_item_type' => SaleTransaction::ACCOUNTING_INVOICE,
        'taxable_value' => 610.00,
    ]);
});

test('validation with update negative and wrong addless total for update sale transaction account invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $saleInput = SaleTransactionFactory::new()->make(['sales_item_type' => SaleTransaction::ACCOUNTING_INVOICE])->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');
    $saleData = StoreSaleTransactionAction::run($saleInput);

    $saleInput['add_less'][0]['al_total'] = -100;

    //act
    $response = $this->postJson(route('sale-transaction-update', $saleData['transaction_id']), $saleInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('sales_transactions', [
        'id' => $saleData['transaction_id'],
        'company_id' => 1,
        'invoice_number' => $saleInput['invoice_number'],
        'sales_item_type' => SaleTransaction::ACCOUNTING_INVOICE,
        'gross_value' => 600.00,
    ]);
    $saleTransaction = SaleTransaction::with('additionalCharges')->whereId($saleData['transaction_id'])->first();
    $this->assertEquals(10, intval($saleTransaction->addLess->first()->total));
});

test('validation with update negative and wrong grand total for update sale transaction account invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $saleInput = SaleTransactionFactory::new()->make(['sales_item_type' => SaleTransaction::ACCOUNTING_INVOICE])->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');
    $saleData = StoreSaleTransactionAction::run($saleInput);

    $saleInput['grand_total'] = -100;

    //act
    $response = $this->postJson(route('sale-transaction-update', $saleData['transaction_id']), $saleInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('sales_transactions', [
        'id' => $saleData['transaction_id'],
        'company_id' => 1,
        'invoice_number' => $saleInput['invoice_number'],
        'sales_item_type' => SaleTransaction::ACCOUNTING_INVOICE,
        'grand_total' => 620.00,
    ]);
});

test('validation with update negative and wrong round off amount and method for update sale transaction', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);
    $saleInput = SaleTransactionFactory::new()->make()->toArray();
    $saleInput['date'] = Carbon::now()->format('d-m-Y');
    $saleInput['additional_charges'][0]['ac_value'] = 10.54;

    $roundOffMethods = [
        ['method' => 1, 'correct_amount' => 0],
        ['method' => 2, 'correct_amount' => -0.54],
        ['method' => 3, 'correct_amount' => 0.46],
        ['method' => 4, 'correct_amount' => 0.46],
    ];
    foreach ($roundOffMethods as $roundOffMethod) {
        $saleInput['invoice_number'] = 'INV-'.$roundOffMethod['method'];
        $saleData = StoreSaleTransactionAction::run($saleInput);
        $saleInput['round_off_method'] = 0;
        IncomeTransactionMaster::whereCompanyId(1)->update(['round_off_method' => $roundOffMethod['method']]);
        $saleInput['rounding_amount'] = -10.50;

        //act
        $response = $this->postJson(route('sale-transaction-update', $saleData['transaction_id']), $saleInput);

        //asert
        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Sale Transaction Updated Successfully',
            ]);
        $this->assertDatabaseHas('sales_transactions', [
            'id' => $saleData['transaction_id'],
            'company_id' => 1,
            'invoice_number' => $saleInput['invoice_number'],
            'round_off_method' => $roundOffMethod,
            'rounding_amount' => $roundOffMethod['correct_amount'],
        ]);
    }
});
