<?php

use App\Actions\v1\Transactions\IncomeEstimateQuote\StoreEstimateQuoteTransactionAction;
use App\Models\GstTax;
use App\Models\IncomeEstimateQuoteTransaction;
use App\Models\Master\IncomeEstimateQuoteTransactionMaster;
use App\Models\Master\ItemMasterGoods;
use Database\Factories\Api\IncomeEstimateQuoteTransactionFactory;

/*
|----------------------------------------------------------------------------------------------------------------------------------------------------
| Estimate Quote Transaction Item Invoice Backend Validation Test Case
|----------------------------------------------------------------------------------------------------------------------------------------------------
*/

test('validate estimate quote transaction item invoice will return gst na incorrect', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false, 'app.is_validation_testing' => false]);

    $estimateInput = IncomeEstimateQuoteTransactionFactory::new()->make()->toArray();

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-estimate-quote', $estimateInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Is GST NA is not correct.',
        ]);
});

test('validate estimate quote transaction item invoice will return cgst, sgst, and igst calculated is not correct', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false, 'app.is_validation_testing' => false]);

    $estimateInput = IncomeEstimateQuoteTransactionFactory::new()->make(['is_gst_na' => true, 'is_cgst_sgst_igst_calculated' => true])->toArray();

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-estimate-quote', $estimateInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Is CGST, SGST, and IGST Calculated is not correct.',
        ]);
});

test('validate estimate quote transaction item invoice will return subtotal is not correct when pass wrong total', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $estimateInput = IncomeEstimateQuoteTransactionFactory::new()->make([
        'is_gst_na' => true,
    ])->toArray();
    $estimateInput['items'][0]['total'] = 10;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-estimate-quote', $estimateInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Subtotal is not correct.',
        ]);
});

test('validate estimate quote transaction item invoice will return subtotal is not correct when pass wrong total with gst na, exempt, zero in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $estimateInput = IncomeEstimateQuoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $estimateInput['items'][0]['total'] = 10;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-estimate-quote', $estimateInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Subtotal is not correct.',
        ]);
});

test('validate estimate quote transaction item invoice will return subtotal is not correct when pass wrong total with gst percentage', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $gst = GstTax::whereTaxRate(28.00)->first();
    $estimateInput = IncomeEstimateQuoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $estimateInput['items'][0]['gst_id'] = $gst->id;
    $estimateInput['items'][0]['gst_tax_percentage'] = $gst->tax_rate;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-estimate-quote', $estimateInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Subtotal is not correct.',
        ]);
});

test('validate estimate quote transaction item invoice will return gross value is not correct', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $estimateInput = IncomeEstimateQuoteTransactionFactory::new()->make([
        'is_gst_na' => true,
        'gross_value' => 10,
    ])->toArray();

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-estimate-quote', $estimateInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Gross value is not correct.',
        ]);
});

test('validate estimate quote transaction item invoice will return addition charge total is not correct', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $estimateInput = IncomeEstimateQuoteTransactionFactory::new()->make([
        'is_gst_na' => true,
    ])->toArray();
    $estimateInput['additional_charges'][0]['ac_value'] = 20;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-estimate-quote', $estimateInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Additional charge total is not correct.',
        ]);
});

test('validate estimate quote transaction item invoice will return Taxable value is not correct when pass additional gst percentage in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $gst = GstTax::whereTaxRate(28.00)->first();
    $estimateInput = IncomeEstimateQuoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $estimateInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    foreach ($estimateInput['items'] as $key => $item) {
        $estimateInput['items'][$key]['gst_id'] = 2;
    }

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-estimate-quote', $estimateInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Taxable value is not correct.',
        ]);
});

test('validate estimate quote transaction item invoice will return taxable value is not correct', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $estimateInput = IncomeEstimateQuoteTransactionFactory::new()->make([
        'is_gst_na' => true,
        'taxable_value' => 10,
    ])->toArray();

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-estimate-quote', $estimateInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Taxable value is not correct.',
        ]);
});

test('validate estimate quote transaction item invoice will return cgst is not correct in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $estimateInput = IncomeEstimateQuoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'gross_value' => 468.78,
        'taxable_value' => 478.78,
        'main_classification_nature_type' => 'Sale Intra State Taxable',
    ])->toArray();
    foreach ($estimateInput['items'] as $key => $item) {
        $estimateInput['items'][$key]['gst_id'] = $gst->id;
        $estimateInput['items'][$key]['total'] = 78.13;
    }

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-estimate-quote', $estimateInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'CGST is not correct.',
        ]);
});

test('validate estimate quote transaction item invoice will return sgst is not correct in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $estimateInput = IncomeEstimateQuoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'gross_value' => 468.78,
        'taxable_value' => 478.78,
        'main_classification_nature_type' => 'Sale Intra State Taxable',
        'cgst' => 65.64,
    ])->toArray();
    foreach ($estimateInput['items'] as $key => $item) {
        $estimateInput['items'][$key]['gst_id'] = $gst->id;
        $estimateInput['items'][$key]['total'] = 78.13;
    }

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-estimate-quote', $estimateInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'SGST is not correct.',
        ]);
});

test('validate estimate quote transaction item invoice will return igst is not correct in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $estimateInput = IncomeEstimateQuoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'gross_value' => 468.78,
        'taxable_value' => 478.78,
    ])->toArray();
    foreach ($estimateInput['items'] as $key => $item) {
        $estimateInput['items'][$key]['gst_id'] = $gst->id;
        $estimateInput['items'][$key]['total'] = 78.13;
    }

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-estimate-quote', $estimateInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'IGST is not correct.',
        ]);
});

test('validate estimate quote transaction item invoice will return classification nature type is empty', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(0.00)->first();

    $estimateInput = IncomeEstimateQuoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'main_classification_nature_type' => null,
        'taxable_value' => 610,
    ])->toArray();
    foreach ($estimateInput['items'] as $key => $item) {
        $estimateInput['items'][$key]['gst_id'] = $gst->id;
    }

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-estimate-quote', $estimateInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Classification Nature Type is empty.',
        ]);
});

test('validate estimate quote transaction item invoice will return add less total is not correct', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $estimateInput = IncomeEstimateQuoteTransactionFactory::new()->make([
        'is_gst_na' => true,
        'taxable_value' => 610,
    ])->toArray();
    $estimateInput['add_less'][0]['al_total'] = 100;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-estimate-quote', $estimateInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Add Less total is not correct.',
        ]);
});

test('validate estimate quote transaction item invoice will return round off amount is not correct', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $estimateInput = IncomeEstimateQuoteTransactionFactory::new()->make([
        'is_gst_na' => true,
        'is_round_off_not_changed' => 1,
        'taxable_value' => 610,
        'rounding_amount' => 10.50,
    ])->toArray();

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-estimate-quote', $estimateInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Round off amount is not correct.',
        ]);
});

test('validate estimate quote transaction item invoice will return grand total is not correct', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $estimateInput = IncomeEstimateQuoteTransactionFactory::new()->make([
        'is_gst_na' => true,
        'taxable_value' => 610,
        'grand_total' => 100,
    ])->toArray();

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-estimate-quote', $estimateInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Grand total is not correct.',
        ]);
});

/*
|----------------------------------------------------------------------------------------------------------------------------------------------------
| Estimate Quote Transaction Account Invoice Backend Validation Test Case
|----------------------------------------------------------------------------------------------------------------------------------------------------
*/

test('validate estimate quote transaction account invoice will return gst na incorrect', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false, 'app.is_validation_testing' => false]);

    $estimateInput = IncomeEstimateQuoteTransactionFactory::new()->make(['invoice_type' => IncomeEstimateQuoteTransaction::ACCOUNTING_INVOICE])->toArray();

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-estimate-quote', $estimateInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Is GST NA is not correct.',
        ]);
});

test('validate estimate quote transaction account invoice will return cgst, sgst, and igst calculated is not correct', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false, 'app.is_validation_testing' => false]);

    $estimateInput = IncomeEstimateQuoteTransactionFactory::new()->make([
        'invoice_type' => IncomeEstimateQuoteTransaction::ACCOUNTING_INVOICE,
        'is_gst_na' => true,
        'is_cgst_sgst_igst_calculated' => true,
    ])->toArray();

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-estimate-quote', $estimateInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Is CGST, SGST, and IGST Calculated is not correct.',
        ]);
});

test('validate estimate quote transaction account invoice will return subtotal is not correct when pass wrong total', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $estimateInput = IncomeEstimateQuoteTransactionFactory::new()->make([
        'invoice_type' => IncomeEstimateQuoteTransaction::ACCOUNTING_INVOICE,
        'is_gst_na' => true,
    ])->toArray();
    $estimateInput['ledgers'][0]['total'] = 10;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-estimate-quote', $estimateInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Subtotal is not correct.',
        ]);
});

test('validate estimate quote transaction account invoice will return subtotal is not correct when pass wrong total with gst na, exempt, zero in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $estimateInput = IncomeEstimateQuoteTransactionFactory::new()->make([
        'invoice_type' => IncomeEstimateQuoteTransaction::ACCOUNTING_INVOICE,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $estimateInput['ledgers'][0]['total'] = 10;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-estimate-quote', $estimateInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Subtotal is not correct.',
        ]);
});

test('validate estimate quote transaction account invoice will return subtotal is not correct when pass wrong total with gst percentage', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $gst = GstTax::whereTaxRate(28.00)->first();
    $estimateInput = IncomeEstimateQuoteTransactionFactory::new()->make([
        'invoice_type' => IncomeEstimateQuoteTransaction::ACCOUNTING_INVOICE,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $estimateInput['ledgers'][0]['gst_id'] = $gst->id;
    $estimateInput['ledgers'][0]['with_tax'] = true;
    $estimateInput['ledgers'][0]['gst_tax_percentage'] = $gst->tax_rate;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-estimate-quote', $estimateInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Subtotal is not correct.',
        ]);
});

test('validate estimte quote transaction account invoice will return gross value is not correct', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $estimateInput = IncomeEstimateQuoteTransactionFactory::new()->make([
        'invoice_type' => IncomeEstimateQuoteTransaction::ACCOUNTING_INVOICE,
        'is_gst_na' => true,
        'gross_value' => 10,
    ])->toArray();

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-estimate-quote', $estimateInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Gross value is not correct.',
        ]);
});

test('validate estimate quote transaction account invoice will return addition charge total is not correct', function () {
    //arrange
    $User = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $estimateInput = IncomeEstimateQuoteTransactionFactory::new()->make([
        'invoice_type' => IncomeEstimateQuoteTransaction::ACCOUNTING_INVOICE,
        'is_gst_na' => true,
    ])->toArray();
    $estimateInput['additional_charges'][0]['ac_value'] = 20;

    //act
    $response = postAPI($User->token, $User->company->id, 'api/v2/income-estimate-quote', $estimateInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Additional charge total is not correct.',
        ]);
});

test('validate estimate quote transaction account invoice will return Taxable value is not correct when pass additional gst percentage in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $gst = GstTax::whereTaxRate(28.00)->first();
    $estimateInput = IncomeEstimateQuoteTransactionFactory::new()->make([
        'invoice_type' => IncomeEstimateQuoteTransaction::ACCOUNTING_INVOICE,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $estimateInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    foreach ($estimateInput['ledgers'] as $key => $ledger) {
        $estimateInput['ledgers'][$key]['gst_id'] = 2;
    }

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-estimate-quote', $estimateInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Taxable value is not correct.',
        ]);
});

test('validate estimate quote transaction account invoice will return taxable value is not correct', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $estimateInput = IncomeEstimateQuoteTransactionFactory::new()->make([
        'invoice_type' => IncomeEstimateQuoteTransaction::ACCOUNTING_INVOICE,
        'is_gst_na' => true,
        'taxable_value' => 10,
    ])->toArray();

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-estimate-quote', $estimateInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Taxable value is not correct.',
        ]);
});

test('validate estimate quote transaction account invoice will return cgst is not correct in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $estimateInput = IncomeEstimateQuoteTransactionFactory::new()->make([
        'invoice_type' => IncomeEstimateQuoteTransaction::ACCOUNTING_INVOICE,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'gross_value' => 468.78,
        'taxable_value' => 478.78,
        'main_classification_nature_type' => 'Sale Intra State Taxable',
    ])->toArray();
    foreach ($estimateInput['ledgers'] as $key => $ledger) {
        $estimateInput['ledgers'][$key]['gst_id'] = $gst->id;
        $estimateInput['ledgers'][$key]['total'] = 78.13;
    }

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-estimate-quote', $estimateInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'CGST is not correct.',
        ]);
});

test('validate estimate quote transaction account invoice will return sgst is not correct in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $estimateInput = IncomeEstimateQuoteTransactionFactory::new()->make([
        'invoice_type' => IncomeEstimateQuoteTransaction::ACCOUNTING_INVOICE,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'gross_value' => 468.78,
        'taxable_value' => 478.78,
        'main_classification_nature_type' => 'Sale Intra State Taxable',
        'cgst' => 65.64,
    ])->toArray();
    foreach ($estimateInput['ledgers'] as $key => $ledger) {
        $estimateInput['ledgers'][$key]['gst_id'] = $gst->id;
        $estimateInput['ledgers'][$key]['total'] = 78.13;
    }

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-estimate-quote', $estimateInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'SGST is not correct.',
        ]);
});

test('validate estimate quote transaction account invoice will return igst is not correct in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $estimateInput = IncomeEstimateQuoteTransactionFactory::new()->make([
        'invoice_type' => IncomeEstimateQuoteTransaction::ACCOUNTING_INVOICE,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'gross_value' => 468.78,
        'taxable_value' => 478.78,
    ])->toArray();
    foreach ($estimateInput['ledgers'] as $key => $ledger) {
        $estimateInput['ledgers'][$key]['gst_id'] = $gst->id;
        $estimateInput['ledgers'][$key]['total'] = 78.13;
    }

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-estimate-quote', $estimateInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'IGST is not correct.',
        ]);
});

test('validate estimate quote transaction account invoice will return classification nature type is empty', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(0.00)->first();

    $estimateInput = IncomeEstimateQuoteTransactionFactory::new()->make([
        'invoice_type' => IncomeEstimateQuoteTransaction::ACCOUNTING_INVOICE,
        'is_gst_enabled' => true,
        'main_classification_nature_type' => null,
        'taxable_value' => 610,
    ])->toArray();
    foreach ($estimateInput['ledgers'] as $key => $ledger) {
        $estimateInput['ledgers'][$key]['gst_id'] = $gst->id;
    }

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-estimate-quote', $estimateInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Classification Nature Type is empty.',
        ]);
});

test('validate estimate quote transaction account invoice will return add less total is not correct', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $estimateInput = IncomeEstimateQuoteTransactionFactory::new()->make([
        'invoice_type' => IncomeEstimateQuoteTransaction::ACCOUNTING_INVOICE,
        'is_gst_na' => true,
        'taxable_value' => 610,
    ])->toArray();
    $estimateInput['add_less'][0]['al_total'] = 100;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-estimate-quote', $estimateInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Add Less total is not correct.',
        ]);
});

test('validate estimate quote transaction account invoice will return round off amount is not correct', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $estimateInput = IncomeEstimateQuoteTransactionFactory::new()->make([
        'invoice_type' => IncomeEstimateQuoteTransaction::ACCOUNTING_INVOICE,
        'is_round_off_not_changed' => 1,
        'is_gst_na' => true,
        'taxable_value' => 610,
        'rounding_amount' => 10.50,
    ])->toArray();

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-estimate-quote', $estimateInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Round off amount is not correct.',
        ]);
});

test('validate estimate quote transaction account invoice will return grand total is not correct', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $estimateInput = IncomeEstimateQuoteTransactionFactory::new()->make([
        'invoice_type' => IncomeEstimateQuoteTransaction::ACCOUNTING_INVOICE,
        'is_gst_na' => true,
        'taxable_value' => 610,
        'grand_total' => 100,
    ])->toArray();

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-estimate-quote', $estimateInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Grand total is not correct.',
        ]);
});

/*
|----------------------------------------------------------------------------------------------------------------------------------------------------
| Store Estimate Quote Transaction Item Invoice Backend Validation with update Test Case
|----------------------------------------------------------------------------------------------------------------------------------------------------
*/

test('validation with update decimal rpu amount when decimal place is 1 for estimate quote transaction item invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 1]);
    $estimateInput = IncomeEstimateQuoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $estimateInput['items'][0]['rpu'] = 100.********;
    $estimateInput['items'][0]['rpu_without_gst'] = 100.********;
    $estimateInput['items'][0]['rpu_with_gst'] = 100.********;
    $estimateInput['items'][0]['gst_id'] = $gst->id;
    $estimateInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-estimate-quote', $estimateInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Estimate/Quote Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('income_estimate_quote_transactions', [
        'id' => $response->json('data.estimate_quote_transaction.id'),
        'company_id' => 1,
        'document_number' => $estimateInput['document_number'],
        'invoice_type' => IncomeEstimateQuoteTransaction::ITEM_INVOICE,
    ]);
    $estimateTransaction = IncomeEstimateQuoteTransaction::with('transactionItems')->whereId($response->json('data.estimate_quote_transaction.id'))->first();
    $this->assertEquals(78.2, $estimateTransaction->transactionItems->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 2 for estimate quote transaction item invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 2]);
    $estimateInput = IncomeEstimateQuoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $estimateInput['items'][0]['rpu'] = 100.********;
    $estimateInput['items'][0]['rpu_without_gst'] = 100.********;
    $estimateInput['items'][0]['rpu_with_gst'] = 100.********;
    $estimateInput['items'][0]['gst_id'] = $gst->id;
    $estimateInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-estimate-quote', $estimateInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Estimate/Quote Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('income_estimate_quote_transactions', [
        'id' => $response->json('data.estimate_quote_transaction.id'),
        'company_id' => 1,
        'document_number' => $response->json('data.estimate_quote_transaction.document_number'),
        'invoice_type' => IncomeEstimateQuoteTransaction::ITEM_INVOICE,
    ]);
    $estimateTransaction = IncomeEstimateQuoteTransaction::with('transactionItems')->whereId($response->json('data.estimate_quote_transaction.id'))->first();
    $this->assertEquals(78.22, $estimateTransaction->transactionItems->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 3 for estimate quote transaction item invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 3]);
    $estimateInput = IncomeEstimateQuoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $estimateInput['items'][0]['rpu'] = 100.********;
    $estimateInput['items'][0]['rpu_without_gst'] = 100.********;
    $estimateInput['items'][0]['rpu_with_gst'] = 100.********;
    $estimateInput['items'][0]['gst_id'] = $gst->id;
    $estimateInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-estimate-quote', $estimateInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Estimate/Quote Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('income_estimate_quote_transactions', [
        'id' => $response->json('data.estimate_quote_transaction.id'),
        'company_id' => 1,
        'document_number' => $response->json('data.estimate_quote_transaction.document_number'),
        'invoice_type' => IncomeEstimateQuoteTransaction::ITEM_INVOICE,
    ]);
    $estimateTransaction = IncomeEstimateQuoteTransaction::with('transactionItems')->whereId($response->json('data.estimate_quote_transaction.id'))->first();
    $this->assertEquals(78.220, $estimateTransaction->transactionItems->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 4 for estimate quote transaction item invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 4]);
    $estimateInput = IncomeEstimateQuoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $estimateInput['items'][0]['rpu'] = 100.********;
    $estimateInput['items'][0]['rpu_without_gst'] = 100.********;
    $estimateInput['items'][0]['rpu_with_gst'] = 100.********;
    $estimateInput['items'][0]['gst_id'] = $gst->id;
    $estimateInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-estimate-quote', $estimateInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Estimate/Quote Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('income_estimate_quote_transactions', [
        'id' => $response->json('data.estimate_quote_transaction.id'),
        'company_id' => 1,
        'document_number' => $response->json('data.estimate_quote_transaction.document_number'),
        'invoice_type' => IncomeEstimateQuoteTransaction::ITEM_INVOICE,
    ]);
    $estimateTransaction = IncomeEstimateQuoteTransaction::with('transactionItems')->whereId($response->json('data.estimate_quote_transaction.id'))->first();
    $this->assertEquals(78.2197, $estimateTransaction->transactionItems->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 5 for estimate quote transaction item invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 5]);
    $estimateInput = IncomeEstimateQuoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $estimateInput['items'][0]['rpu'] = 100.********;
    $estimateInput['items'][0]['rpu_without_gst'] = 100.********;
    $estimateInput['items'][0]['rpu_with_gst'] = 100.********;
    $estimateInput['items'][0]['gst_id'] = $gst->id;
    $estimateInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-estimate-quote', $estimateInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Estimate/Quote Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('income_estimate_quote_transactions', [
        'id' => $response->json('data.estimate_quote_transaction.id'),
        'company_id' => 1,
        'document_number' => $response->json('data.estimate_quote_transaction.document_number'),
        'invoice_type' => IncomeEstimateQuoteTransaction::ITEM_INVOICE,
    ]);
    $estimateTransaction = IncomeEstimateQuoteTransaction::with('transactionItems')->whereId($response->json('data.estimate_quote_transaction.id'))->first();
    $this->assertEquals(78.21970, $estimateTransaction->transactionItems->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 1 for estimate quote transaction item invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 1]);
    $estimateInput = IncomeEstimateQuoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $estimateInput['items'][0]['rpu'] = 100.********;
    $estimateInput['items'][0]['rpu_without_gst'] = 100.********;
    $estimateInput['items'][0]['rpu_with_gst'] = 100.********;
    $estimateInput['items'][0]['gst_id'] = $gst->id;
    $estimateInput['items'][0]['gst_tax_percentage'] = 2;
    $estimateInput['items'][0]['with_tax'] = false;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-estimate-quote', $estimateInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Estimate/Quote Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('income_estimate_quote_transactions', [
        'id' => $response->json('data.estimate_quote_transaction.id'),
        'company_id' => 1,
        'document_number' => $response->json('data.estimate_quote_transaction.document_number'),
        'invoice_type' => IncomeEstimateQuoteTransaction::ITEM_INVOICE,
    ]);
    $estimateTransaction = IncomeEstimateQuoteTransaction::with('transactionItems')->whereId($response->json('data.estimate_quote_transaction.id'))->first();
    $this->assertEquals(128.2, $estimateTransaction->transactionItems->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 2 for estimate quote transaction item invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 2]);
    $estimateInput = IncomeEstimateQuoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $estimateInput['items'][0]['rpu'] = 100.********;
    $estimateInput['items'][0]['rpu_without_gst'] = 100.********;
    $estimateInput['items'][0]['rpu_with_gst'] = 100.********;
    $estimateInput['items'][0]['gst_id'] = $gst->id;
    $estimateInput['items'][0]['gst_tax_percentage'] = 2;
    $estimateInput['items'][0]['with_tax'] = false;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-estimate-quote', $estimateInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Estimate/Quote Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('income_estimate_quote_transactions', [
        'id' => $response->json('data.estimate_quote_transaction.id'),
        'company_id' => 1,
        'document_number' => $response->json('data.estimate_quote_transaction.document_number'),
        'invoice_type' => IncomeEstimateQuoteTransaction::ITEM_INVOICE,
    ]);
    $estimateTransaction = IncomeEstimateQuoteTransaction::with('transactionItems')->whereId($response->json('data.estimate_quote_transaction.id'))->first();
    $this->assertEquals(128.16, $estimateTransaction->transactionItems->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 3 for estimate quote transaction item invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 3]);
    $estimateInput = IncomeEstimateQuoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $estimateInput['items'][0]['rpu'] = 100.********;
    $estimateInput['items'][0]['rpu_without_gst'] = 100.********;
    $estimateInput['items'][0]['rpu_with_gst'] = 100.********;
    $estimateInput['items'][0]['gst_id'] = $gst->id;
    $estimateInput['items'][0]['gst_tax_percentage'] = 2;
    $estimateInput['items'][0]['with_tax'] = false;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-estimate-quote', $estimateInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Estimate/Quote Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('income_estimate_quote_transactions', [
        'id' => $response->json('data.estimate_quote_transaction.id'),
        'company_id' => 1,
        'document_number' => $response->json('data.estimate_quote_transaction.document_number'),
        'invoice_type' => IncomeEstimateQuoteTransaction::ITEM_INVOICE,
    ]);
    $estimateTransaction = IncomeEstimateQuoteTransaction::with('transactionItems')->whereId($response->json('data.estimate_quote_transaction.id'))->first();
    $this->assertEquals(128.155, $estimateTransaction->transactionItems->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 4 for estimate quote transaction item invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 4]);
    $estimateInput = IncomeEstimateQuoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $estimateInput['items'][0]['rpu'] = 100.********;
    $estimateInput['items'][0]['rpu_without_gst'] = 100.********;
    $estimateInput['items'][0]['rpu_with_gst'] = 100.********;
    $estimateInput['items'][0]['gst_id'] = $gst->id;
    $estimateInput['items'][0]['gst_tax_percentage'] = 2;
    $estimateInput['items'][0]['with_tax'] = false;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-estimate-quote', $estimateInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Estimate/Quote Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('income_estimate_quote_transactions', [
        'id' => $response->json('data.estimate_quote_transaction.id'),
        'company_id' => 1,
        'document_number' => $response->json('data.estimate_quote_transaction.document_number'),
        'invoice_type' => IncomeEstimateQuoteTransaction::ITEM_INVOICE,
    ]);
    $estimateTransaction = IncomeEstimateQuoteTransaction::with('transactionItems')->whereId($response->json('data.estimate_quote_transaction.id'))->first();
    $this->assertEquals(128.1552, $estimateTransaction->transactionItems->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 5 for estimate quote transaction item invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 5]);
    $estimateInput = IncomeEstimateQuoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $estimateInput['items'][0]['rpu'] = 100.********;
    $estimateInput['items'][0]['rpu_without_gst'] = 100.********;
    $estimateInput['items'][0]['rpu_with_gst'] = 100.********;
    $estimateInput['items'][0]['gst_id'] = $gst->id;
    $estimateInput['items'][0]['gst_tax_percentage'] = 2;
    $estimateInput['items'][0]['with_tax'] = false;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-estimate-quote', $estimateInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Estimate/Quote Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('income_estimate_quote_transactions', [
        'id' => $response->json('data.estimate_quote_transaction.id'),
        'company_id' => 1,
        'document_number' => $response->json('data.estimate_quote_transaction.document_number'),
        'invoice_type' => IncomeEstimateQuoteTransaction::ITEM_INVOICE,
    ]);
    $estimateTransaction = IncomeEstimateQuoteTransaction::with('transactionItems')->whereId($response->json('data.estimate_quote_transaction.id'))->first();
    $this->assertEquals(128.15515, $estimateTransaction->transactionItems->first()->rpu_with_gst);
});

test('validation with update negative and wrong rpu without or with gst for estimate quote transaction item invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $estimateInput = IncomeEstimateQuoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $estimateInput['items'][0]['rpu_without_gst'] = -10000;
    $estimateInput['items'][0]['rpu_with_gst'] = -10000;
    $estimateInput['items'][0]['gst_id'] = $gst->id;
    $estimateInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-estimate-quote', $estimateInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Estimate/Quote Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('income_estimate_quote_transactions', [
        'id' => $response->json('data.estimate_quote_transaction.id'),
        'company_id' => 1,
        'document_number' => $response->json('data.estimate_quote_transaction.document_number'),
        'invoice_type' => IncomeEstimateQuoteTransaction::ITEM_INVOICE,
    ]);
    $estimateTransaction = IncomeEstimateQuoteTransaction::with('transactionItems')->whereId($response->json('data.estimate_quote_transaction.id'))->first();
    $this->assertNotEquals(100, $estimateTransaction->transactionItems->first()->rpu_without_gst);
    $this->assertEquals(100.0, $estimateTransaction->transactionItems->first()->rpu_with_gst);
});

test('validation with update negative and wrong gst tax percentage for estimate quote transaction item invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $estimateInput = IncomeEstimateQuoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $estimateInput['items'][0]['total'] = -10000;
    $estimateInput['items'][0]['gst_id'] = $gst->id;
    $estimateInput['items'][0]['gst_tax_percentage'] = $gst->tax_rate;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-estimate-quote', $estimateInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Estimate/Quote Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('income_estimate_quote_transactions', [
        'id' => $response->json('data.estimate_quote_transaction.id'),
        'company_id' => 1,
        'document_number' => $response->json('data.estimate_quote_transaction.document_number'),
        'invoice_type' => IncomeEstimateQuoteTransaction::ITEM_INVOICE,
    ]);
    $estimateTransaction = IncomeEstimateQuoteTransaction::with('transactionItems')->whereId($response->json('data.estimate_quote_transaction.id'))->first();
    $this->assertEquals(28, intval($estimateTransaction->transactionItems->first()->gst_tax_percentage));
});

test('validation with update negative and wrong total discount for estimate quote transaction item invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(5.00)->first();

    $estimateInput = IncomeEstimateQuoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    foreach ($estimateInput['items'] as $key => $item) {
        $estimateInput['items'][$key]['gst_id'] = $gst->id;
        $estimateInput['items'][$key]['discount_type'] = 2;
        $estimateInput['items'][$key]['discount_value'] = 10;
        $estimateInput['items'][$key]['discount_type_2'] = 2;
        $estimateInput['items'][$key]['discount_value_2'] = 10;
        $estimateInput['items'][$key]['total_discount_amount'] = 10;
    }

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-estimate-quote', $estimateInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Estimate/Quote Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('income_estimate_quote_transactions', [
        'id' => $response->json('data.estimate_quote_transaction.id'),
        'company_id' => 1,
        'document_number' => $response->json('data.estimate_quote_transaction.document_number'),
        'invoice_type' => IncomeEstimateQuoteTransaction::ITEM_INVOICE,
    ]);
    $estimateTransaction = IncomeEstimateQuoteTransaction::with('transactionItems')->whereId($response->json('data.estimate_quote_transaction.id'))->first();
    foreach ($estimateTransaction->transactionItems as $item) {
        $this->assertEquals(18.1, $item->total_discount_amount);
    }
});

test('validation with update negative and wrong sub total for estimate quote transaction item invoice', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $estimateInput = IncomeEstimateQuoteTransactionFactory::new()->make()->toArray();
    $estimateInput['items'][0]['total'] = -10000;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-estimate-quote', $estimateInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Estimate/Quote Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('income_estimate_quote_transactions', [
        'id' => $response->json('data.estimate_quote_transaction.id'),
        'company_id' => 1,
        'document_number' => $response->json('data.estimate_quote_transaction.document_number'),
        'invoice_type' => IncomeEstimateQuoteTransaction::ITEM_INVOICE,
    ]);
    $estimateTransaction = IncomeEstimateQuoteTransaction::with('transactionItems')->whereId($response->json('data.estimate_quote_transaction.id'))->first();
    $this->assertEquals(100, intval($estimateTransaction->transactionItems->first()->total));
});

test('validation with update negative and wrong total for estimate quote transaction item invoice', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $estimateInput = IncomeEstimateQuoteTransactionFactory::new()->make(['total' => -620])->toArray();

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-estimate-quote', $estimateInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Estimate/Quote Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('income_estimate_quote_transactions', [
        'id' => $response->json('data.estimate_quote_transaction.id'),
        'company_id' => 1,
        'document_number' => $response->json('data.estimate_quote_transaction.document_number'),
        'invoice_type' => IncomeEstimateQuoteTransaction::ITEM_INVOICE,
        'total' => 620.00,
    ]);
});

test('validation with update negative and wrong gross value for estimate quote transaction item invoice', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $estimateInput = IncomeEstimateQuoteTransactionFactory::new()->make(['gross_value' => -100])->toArray();

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-estimate-quote', $estimateInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Estimate/Quote Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('income_estimate_quote_transactions', [
        'id' => $response->json('data.estimate_quote_transaction.id'),
        'company_id' => 1,
        'document_number' => $response->json('data.estimate_quote_transaction.document_number'),
        'invoice_type' => IncomeEstimateQuoteTransaction::ITEM_INVOICE,
        'gross_value' => 600.00,
    ]);
});

test('validation with update negative and wrong additional charges gst tax percentage for estimate quote transaction item invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $estimateInput = IncomeEstimateQuoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $estimateInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    $estimateInput['additional_charges'][0]['ac_total'] = -2000;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-estimate-quote', $estimateInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Estimate/Quote Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('income_estimate_quote_transactions', [
        'id' => $response->json('data.estimate_quote_transaction.id'),
        'company_id' => 1,
        'document_number' => $response->json('data.estimate_quote_transaction.document_number'),
        'invoice_type' => IncomeEstimateQuoteTransaction::ITEM_INVOICE,
    ]);
    $estimateTransaction = IncomeEstimateQuoteTransaction::with('additionalCharges')->whereId($response->json('data.estimate_quote_transaction.id'))->first();
    $this->assertNotEquals(10.00, $estimateTransaction->additionalCharges->first()->total);
});

test('validation with update negative and wrong additional charges total for estimate quote transaction item invoice', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $estimateInput = IncomeEstimateQuoteTransactionFactory::new()->make()->toArray();
    $estimateInput['additional_charges'][0]['ac_total'] = -200;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-estimate-quote', $estimateInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Estimate/Quote Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('income_estimate_quote_transactions', [
        'id' => $response->json('data.estimate_quote_transaction.id'),
        'company_id' => 1,
        'document_number' => $response->json('data.estimate_quote_transaction.document_number'),
        'invoice_type' => IncomeEstimateQuoteTransaction::ITEM_INVOICE,
        'gross_value' => 600.00,
    ]);
    $estimateTransaction = IncomeEstimateQuoteTransaction::with('additionalCharges')->whereId($response->json('data.estimate_quote_transaction.id'))->first();
    $this->assertEquals(10, intval($estimateTransaction->additionalCharges->first()->total));
});

test('validation with update negative and wrong cgst and sgst when sale intra state taxable classification for estimate quote transaction item invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $estimateInput = IncomeEstimateQuoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'main_classification_nature_type' => 'Sale Intra State Taxable',
    ])->toArray();
    $estimateInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    foreach ($estimateInput['items'] as $key => $item) {
        $estimateInput['items'][$key]['gst_id'] = $gst->id;
    }

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-estimate-quote', $estimateInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Estimate/Quote Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('income_estimate_quote_transactions', [
        'id' => $response->json('data.estimate_quote_transaction.id'),
        'company_id' => 1,
        'document_number' => $response->json('data.estimate_quote_transaction.document_number'),
        'invoice_type' => IncomeEstimateQuoteTransaction::ITEM_INVOICE,
    ]);
    $estimateTransaction = IncomeEstimateQuoteTransaction::whereId($response->json('data.estimate_quote_transaction.id'))->first();
    $this->assertEquals(67.04, $estimateTransaction->cgst);
    $this->assertEquals(67.04, $estimateTransaction->sgst);
});

test('validation with update negative and wrong igst when sale inter state taxable classification for estimate quote transaction item invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $estimateInput = IncomeEstimateQuoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'main_classification_nature_type' => 'Sale Inter State Taxable',
    ])->toArray();
    $estimateInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    foreach ($estimateInput['items'] as $key => $item) {
        $estimateInput['items'][$key]['gst_id'] = $gst->id;
    }

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-estimate-quote', $estimateInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Estimate/Quote Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('income_estimate_quote_transactions', [
        'id' => $response->json('data.estimate_quote_transaction.id'),
        'company_id' => 1,
        'document_number' => $response->json('data.estimate_quote_transaction.document_number'),
        'invoice_type' => IncomeEstimateQuoteTransaction::ITEM_INVOICE,
    ]);
    $estimateTransaction = IncomeEstimateQuoteTransaction::whereId($response->json('data.estimate_quote_transaction.id'))->first();
    $this->assertEquals(134.08, $estimateTransaction->igst);
});

test('validation with update negative and wrong taxable value for estimate quote transaction item invoice', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $estimateInput = IncomeEstimateQuoteTransactionFactory::new()->make(['taxable_value' => -100])->toArray();

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-estimate-quote', $estimateInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Estimate/Quote Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('income_estimate_quote_transactions', [
        'id' => $response->json('data.estimate_quote_transaction.id'),
        'company_id' => 1,
        'document_number' => $response->json('data.estimate_quote_transaction.document_number'),
        'invoice_type' => IncomeEstimateQuoteTransaction::ITEM_INVOICE,
        'taxable_value' => 610.00,
    ]);
});

test('validation with update negative and wrong addless total for estimate quote transaction item invoice', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $estimateInput = IncomeEstimateQuoteTransactionFactory::new()->make()->toArray();
    $estimateInput['add_less'][0]['al_total'] = -100;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-estimate-quote', $estimateInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Estimate/Quote Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('income_estimate_quote_transactions', [
        'id' => $response->json('data.estimate_quote_transaction.id'),
        'company_id' => 1,
        'document_number' => $response->json('data.estimate_quote_transaction.document_number'),
        'invoice_type' => IncomeEstimateQuoteTransaction::ITEM_INVOICE,
        'gross_value' => 600.00,
    ]);
    $estimateTransaction = IncomeEstimateQuoteTransaction::with('addLess')->whereId($response->json('data.estimate_quote_transaction.id'))->first();
    $this->assertEquals(10, intval($estimateTransaction->addLess->first()->total));
});

test('validation with update negative and wrong grand total for estimate quote transaction item invoice', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $estimateInput = IncomeEstimateQuoteTransactionFactory::new()->make(['grand_total' => -100])->toArray();

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-estimate-quote', $estimateInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Estimate/Quote Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('income_estimate_quote_transactions', [
        'id' => $response->json('data.estimate_quote_transaction.id'),
        'company_id' => 1,
        'document_number' => $response->json('data.estimate_quote_transaction.document_number'),
        'invoice_type' => IncomeEstimateQuoteTransaction::ITEM_INVOICE,
        'grand_total' => 620.00,
    ]);
});

/*
|----------------------------------------------------------------------------------------------------------------------------------------------------
| Store Estimate Quote Transaction Account Invoice Backend Validation with update Test Case
|----------------------------------------------------------------------------------------------------------------------------------------------------
*/

test('validation with update decimal rpu amount when decimal place is 1 for estimate quote transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $estimateInput = IncomeEstimateQuoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'invoice_type' => IncomeEstimateQuoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $estimateInput['ledgers'][0]['decimal'] = 1;
    $estimateInput['ledgers'][0]['rpu'] = 100.********;
    $estimateInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $estimateInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $estimateInput['ledgers'][0]['gst_id'] = $gst->id;
    $estimateInput['ledgers'][0]['gst_tax_percentage'] = 2;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-estimate-quote', $estimateInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Estimate/Quote Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('income_estimate_quote_transactions', [
        'id' => $response->json('data.estimate_quote_transaction.id'),
        'company_id' => 1,
        'document_number' => $response->json('data.estimate_quote_transaction.document_number'),
        'invoice_type' => IncomeEstimateQuoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $estimateTransaction = IncomeEstimateQuoteTransaction::with('transactionLedgers')->whereId($response->json('data.estimate_quote_transaction.id'))->first();
    $this->assertEquals(78.2, $estimateTransaction->transactionLedgers->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 2 for estimate quote transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $estimateInput = IncomeEstimateQuoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'invoice_type' => IncomeEstimateQuoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $estimateInput['ledgers'][0]['decimal'] = 2;
    $estimateInput['ledgers'][0]['rpu'] = 100.********;
    $estimateInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $estimateInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $estimateInput['ledgers'][0]['gst_id'] = $gst->id;
    $estimateInput['ledgers'][0]['gst_tax_percentage'] = 2;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-estimate-quote', $estimateInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Estimate/Quote Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('income_estimate_quote_transactions', [
        'id' => $response->json('data.estimate_quote_transaction.id'),
        'company_id' => 1,
        'document_number' => $response->json('data.estimate_quote_transaction.document_number'),
        'invoice_type' => IncomeEstimateQuoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $estimateTransaction = IncomeEstimateQuoteTransaction::with('transactionLedgers')->whereId($response->json('data.estimate_quote_transaction.id'))->first();
    $this->assertEquals(78.22, $estimateTransaction->transactionLedgers->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 3 for estimate quote transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $estimateInput = IncomeEstimateQuoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'invoice_type' => IncomeEstimateQuoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $estimateInput['ledgers'][0]['decimal'] = 3;
    $estimateInput['ledgers'][0]['rpu'] = 100.********;
    $estimateInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $estimateInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $estimateInput['ledgers'][0]['gst_id'] = $gst->id;
    $estimateInput['ledgers'][0]['gst_tax_percentage'] = 2;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-estimate-quote', $estimateInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Estimate/Quote Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('income_estimate_quote_transactions', [
        'id' => $response->json('data.estimate_quote_transaction.id'),
        'company_id' => 1,
        'document_number' => $response->json('data.estimate_quote_transaction.document_number'),
        'invoice_type' => IncomeEstimateQuoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $estimateTransaction = IncomeEstimateQuoteTransaction::with('transactionLedgers')->whereId($response->json('data.estimate_quote_transaction.id'))->first();
    $this->assertEquals(78.220, $estimateTransaction->transactionLedgers->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 4 for estimate quote transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $estimateInput = IncomeEstimateQuoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'invoice_type' => IncomeEstimateQuoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $estimateInput['ledgers'][0]['decimal'] = 4;
    $estimateInput['ledgers'][0]['rpu'] = 100.********;
    $estimateInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $estimateInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $estimateInput['ledgers'][0]['gst_id'] = $gst->id;
    $estimateInput['ledgers'][0]['gst_tax_percentage'] = 2;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-estimate-quote', $estimateInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Estimate/Quote Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('income_estimate_quote_transactions', [
        'id' => $response->json('data.estimate_quote_transaction.id'),
        'company_id' => 1,
        'document_number' => $response->json('data.estimate_quote_transaction.document_number'),
        'invoice_type' => IncomeEstimateQuoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $estimateTransaction = IncomeEstimateQuoteTransaction::with('transactionLedgers')->whereId($response->json('data.estimate_quote_transaction.id'))->first();
    $this->assertEquals(78.2197, $estimateTransaction->transactionLedgers->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 5 for estimate quote transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $estimateInput = IncomeEstimateQuoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'invoice_type' => IncomeEstimateQuoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $estimateInput['ledgers'][0]['decimal'] = 5;
    $estimateInput['ledgers'][0]['rpu'] = 100.********;
    $estimateInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $estimateInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $estimateInput['ledgers'][0]['gst_id'] = $gst->id;
    $estimateInput['ledgers'][0]['gst_tax_percentage'] = 2;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-estimate-quote', $estimateInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Estimate/Quote Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('income_estimate_quote_transactions', [
        'id' => $response->json('data.estimate_quote_transaction.id'),
        'company_id' => 1,
        'document_number' => $response->json('data.estimate_quote_transaction.document_number'),
        'invoice_type' => IncomeEstimateQuoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $estimateTransaction = IncomeEstimateQuoteTransaction::with('transactionLedgers')->whereId($response->json('data.estimate_quote_transaction.id'))->first();
    $this->assertEquals(78.21970, $estimateTransaction->transactionLedgers->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 1 for estimate quote transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $estimateInput = IncomeEstimateQuoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'invoice_type' => IncomeEstimateQuoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $estimateInput['ledgers'][0]['decimal'] = 1;
    $estimateInput['ledgers'][0]['rpu'] = 100.********;
    $estimateInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $estimateInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $estimateInput['ledgers'][0]['gst_id'] = $gst->id;
    $estimateInput['ledgers'][0]['gst_tax_percentage'] = 2;
    $estimateInput['ledgers'][0]['with_tax'] = false;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-estimate-quote', $estimateInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Estimate/Quote Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('income_estimate_quote_transactions', [
        'id' => $response->json('data.estimate_quote_transaction.id'),
        'company_id' => 1,
        'document_number' => $response->json('data.estimate_quote_transaction.document_number'),
        'invoice_type' => IncomeEstimateQuoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $estimateTransaction = IncomeEstimateQuoteTransaction::with('transactionLedgers')->whereId($response->json('data.estimate_quote_transaction.id'))->first();
    $this->assertEquals(128.2, $estimateTransaction->transactionLedgers->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 2 for estimate quote transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $estimateInput = IncomeEstimateQuoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'invoice_type' => IncomeEstimateQuoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $estimateInput['ledgers'][0]['decimal'] = 2;
    $estimateInput['ledgers'][0]['rpu'] = 100.********;
    $estimateInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $estimateInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $estimateInput['ledgers'][0]['gst_id'] = $gst->id;
    $estimateInput['ledgers'][0]['gst_tax_percentage'] = 2;
    $estimateInput['ledgers'][0]['with_tax'] = false;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-estimate-quote', $estimateInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Estimate/Quote Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('income_estimate_quote_transactions', [
        'id' => $response->json('data.estimate_quote_transaction.id'),
        'company_id' => 1,
        'document_number' => $response->json('data.estimate_quote_transaction.document_number'),
        'invoice_type' => IncomeEstimateQuoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $estimateTransaction = IncomeEstimateQuoteTransaction::with('transactionLedgers')->whereId($response->json('data.estimate_quote_transaction.id'))->first();
    $this->assertEquals(128.16, $estimateTransaction->transactionLedgers->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 3 for estimate quote transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $estimateInput = IncomeEstimateQuoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'invoice_type' => IncomeEstimateQuoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $estimateInput['ledgers'][0]['decimal'] = 3;
    $estimateInput['ledgers'][0]['rpu'] = 100.********;
    $estimateInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $estimateInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $estimateInput['ledgers'][0]['gst_id'] = $gst->id;
    $estimateInput['ledgers'][0]['gst_tax_percentage'] = 2;
    $estimateInput['ledgers'][0]['with_tax'] = false;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-estimate-quote', $estimateInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Estimate/Quote Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('income_estimate_quote_transactions', [
        'id' => $response->json('data.estimate_quote_transaction.id'),
        'company_id' => 1,
        'document_number' => $response->json('data.estimate_quote_transaction.document_number'),
        'invoice_type' => IncomeEstimateQuoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $estimateTransaction = IncomeEstimateQuoteTransaction::with('transactionLedgers')->whereId($response->json('data.estimate_quote_transaction.id'))->first();
    $this->assertEquals(128.155, $estimateTransaction->transactionLedgers->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 4 for estimate quote transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $estimateInput = IncomeEstimateQuoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'invoice_type' => IncomeEstimateQuoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $estimateInput['ledgers'][0]['decimal'] = 4;
    $estimateInput['ledgers'][0]['rpu'] = 100.********;
    $estimateInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $estimateInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $estimateInput['ledgers'][0]['gst_id'] = $gst->id;
    $estimateInput['ledgers'][0]['gst_tax_percentage'] = 2;
    $estimateInput['ledgers'][0]['with_tax'] = false;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-estimate-quote', $estimateInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Estimate/Quote Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('income_estimate_quote_transactions', [
        'id' => $response->json('data.estimate_quote_transaction.id'),
        'company_id' => 1,
        'document_number' => $response->json('data.estimate_quote_transaction.document_number'),
        'invoice_type' => IncomeEstimateQuoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $estimateTransaction = IncomeEstimateQuoteTransaction::with('transactionLedgers')->whereId($response->json('data.estimate_quote_transaction.id'))->first();
    $this->assertEquals(128.1552, $estimateTransaction->transactionLedgers->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 5 for estimate quote transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $estimateInput = IncomeEstimateQuoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'invoice_type' => IncomeEstimateQuoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $estimateInput['ledgers'][0]['decimal'] = 5;
    $estimateInput['ledgers'][0]['rpu'] = 100.********;
    $estimateInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $estimateInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $estimateInput['ledgers'][0]['gst_id'] = $gst->id;
    $estimateInput['ledgers'][0]['gst_tax_percentage'] = 2;
    $estimateInput['ledgers'][0]['with_tax'] = false;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-estimate-quote', $estimateInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Estimate/Quote Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('income_estimate_quote_transactions', [
        'id' => $response->json('data.estimate_quote_transaction.id'),
        'company_id' => 1,
        'document_number' => $response->json('data.estimate_quote_transaction.document_number'),
        'invoice_type' => IncomeEstimateQuoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $estimateTransaction = IncomeEstimateQuoteTransaction::with('transactionLedgers')->whereId($response->json('data.estimate_quote_transaction.id'))->first();
    $this->assertEquals(128.15515, $estimateTransaction->transactionLedgers->first()->rpu_with_gst);
});

test('validation with update negative and wrong rpu without or with gst for estimate quote transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $estimateInput = IncomeEstimateQuoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'invoice_type' => IncomeEstimateQuoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $estimateInput['ledgers'][0]['rpu_without_gst'] = -10000;
    $estimateInput['ledgers'][0]['rpu_with_gst'] = -10000;
    $estimateInput['ledgers'][0]['gst_id'] = $gst->id;
    $estimateInput['ledgers'][0]['gst_tax_percentage'] = 2;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-estimate-quote', $estimateInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Estimate/Quote Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('income_estimate_quote_transactions', [
        'id' => $response->json('data.estimate_quote_transaction.id'),
        'company_id' => 1,
        'document_number' => $response->json('data.estimate_quote_transaction.document_number'),
        'invoice_type' => IncomeEstimateQuoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $estimateTransaction = IncomeEstimateQuoteTransaction::with('transactionLedgers')->whereId($response->json('data.estimate_quote_transaction.id'))->first();
    $this->assertNotEquals(100, $estimateTransaction->transactionLedgers->first()->rpu_without_gst);
    $this->assertEquals(100.0, $estimateTransaction->transactionLedgers->first()->rpu_with_gst);
});

test('validation with update negative and wrong gst tax percentage for estimate quote transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $estimateInput = IncomeEstimateQuoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'invoice_type' => IncomeEstimateQuoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $estimateInput['ledgers'][0]['total'] = -10000;
    $estimateInput['ledgers'][0]['gst_id'] = $gst->id;
    $estimateInput['ledgers'][0]['gst_tax_percentage'] = $gst->tax_rate;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-estimate-quote', $estimateInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Estimate/Quote Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('income_estimate_quote_transactions', [
        'id' => $response->json('data.estimate_quote_transaction.id'),
        'company_id' => 1,
        'document_number' => $response->json('data.estimate_quote_transaction.document_number'),
        'invoice_type' => IncomeEstimateQuoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $estimateTransaction = IncomeEstimateQuoteTransaction::with('transactionLedgers')->whereId($response->json('data.estimate_quote_transaction.id'))->first();
    $this->assertEquals(28, intval($estimateTransaction->transactionLedgers->first()->gst_tax_percentage));
});

test('validation with update negative and wrong total discount for estimate quote transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(5.00)->first();

    $estimateInput = IncomeEstimateQuoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'invoice_type' => IncomeEstimateQuoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    foreach ($estimateInput['ledgers'] as $key => $ledger) {
        $estimateInput['ledgers'][$key]['gst_id'] = $gst->id;
        $estimateInput['ledgers'][$key]['discount_type'] = 2;
        $estimateInput['ledgers'][$key]['discount_value'] = 10;
        $estimateInput['ledgers'][$key]['discount_type_2'] = 2;
        $estimateInput['ledgers'][$key]['discount_value_2'] = 10;
        $estimateInput['ledgers'][$key]['total_discount_amount'] = 10;
    }

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-estimate-quote', $estimateInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Estimate/Quote Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('income_estimate_quote_transactions', [
        'id' => $response->json('data.estimate_quote_transaction.id'),
        'company_id' => 1,
        'document_number' => $response->json('data.estimate_quote_transaction.document_number'),
        'invoice_type' => IncomeEstimateQuoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $estimateTransaction = IncomeEstimateQuoteTransaction::with('transactionLedgers')->whereId($response->json('data.estimate_quote_transaction.id'))->first();
    foreach ($estimateTransaction->transactionLedgers as $ledger) {
        $this->assertEquals(18.1, $ledger->total_discount_amount);
    }
});

test('validation with update negative and wrong sub total for estimate quote transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $estimateInput = IncomeEstimateQuoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'invoice_type' => IncomeEstimateQuoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $estimateInput['ledgers'][0]['total'] = -10000;
    $estimateInput['ledgers'][0]['gst_id'] = $gst->id;
    $estimateInput['ledgers'][0]['gst_tax_percentage'] = $gst->tax_rate;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-estimate-quote', $estimateInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Estimate/Quote Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('income_estimate_quote_transactions', [
        'id' => $response->json('data.estimate_quote_transaction.id'),
        'company_id' => 1,
        'document_number' => $response->json('data.estimate_quote_transaction.document_number'),
        'invoice_type' => IncomeEstimateQuoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $estimateInput = IncomeEstimateQuoteTransaction::with('transactionLedgers')->whereId($response->json('data.estimate_quote_transaction.id'))->first();
    $this->assertEquals(78.13, $estimateInput->transactionLedgers->first()->total);
});

test('validation with update negative and wrong sub total for estimate quote transaction account invoice', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $estimateInput = IncomeEstimateQuoteTransactionFactory::new()->make(['invoice_type' => IncomeEstimateQuoteTransaction::ACCOUNTING_INVOICE])->toArray();
    $estimateInput['ledgers'][0]['total'] = -10000;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-estimate-quote', $estimateInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Estimate/Quote Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('income_estimate_quote_transactions', [
        'id' => $response->json('data.estimate_quote_transaction.id'),
        'company_id' => 1,
        'document_number' => $response->json('data.estimate_quote_transaction.document_number'),
        'invoice_type' => IncomeEstimateQuoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $estimateTransaction = IncomeEstimateQuoteTransaction::with('transactionLedgers')->whereId($response->json('data.estimate_quote_transaction.id'))->first();
    $this->assertEquals(100, intval($estimateTransaction->transactionLedgers->first()->total));
});

test('validation with update negative and wrong total for estimate quote transaction account invoice', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $estimateInput = IncomeEstimateQuoteTransactionFactory::new()->make([
        'total' => -620,
        'invoice_type' => IncomeEstimateQuoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-estimate-quote', $estimateInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Estimate/Quote Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('income_estimate_quote_transactions', [
        'id' => $response->json('data.estimate_quote_transaction.id'),
        'company_id' => 1,
        'document_number' => $response->json('data.estimate_quote_transaction.document_number'),
        'invoice_type' => IncomeEstimateQuoteTransaction::ACCOUNTING_INVOICE,
        'total' => 620.00,
    ]);
});

test('validation with update negative and wrong gross value for estimate quote transaction account invoice', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $estimateInput = IncomeEstimateQuoteTransactionFactory::new()->make([
        'gross_value' => -100,
        'invoice_type' => IncomeEstimateQuoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-estimate-quote', $estimateInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Estimate/Quote Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('income_estimate_quote_transactions', [
        'id' => $response->json('data.estimate_quote_transaction.id'),
        'company_id' => 1,
        'document_number' => $response->json('data.estimate_quote_transaction.document_number'),
        'invoice_type' => IncomeEstimateQuoteTransaction::ACCOUNTING_INVOICE,
        'gross_value' => 600.00,
    ]);
});

test('validation with update negative and wrong additional charges gst tax percentage for estimate quote transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $estimateInput = IncomeEstimateQuoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'invoice_type' => IncomeEstimateQuoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $estimateInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    $estimateInput['additional_charges'][0]['ac_total'] = -2000;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-estimate-quote', $estimateInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Estimate/Quote Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('income_estimate_quote_transactions', [
        'id' => $response->json('data.estimate_quote_transaction.id'),
        'company_id' => 1,
        'document_number' => $response->json('data.estimate_quote_transaction.document_number'),
        'invoice_type' => IncomeEstimateQuoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $estimateTransaction = IncomeEstimateQuoteTransaction::with('additionalCharges')->whereId($response->json('data.estimate_quote_transaction.id'))->first();
    $this->assertNotEquals(10.00, $estimateTransaction->additionalCharges->first()->total);
});

test('validation with update negative and wrong additional charges total for estimate quote transaction account invoice', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $estimateInput = IncomeEstimateQuoteTransactionFactory::new()->make(['invoice_type' => IncomeEstimateQuoteTransaction::ACCOUNTING_INVOICE])->toArray();
    $estimateInput['additional_charges'][0]['ac_total'] = -200;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-estimate-quote', $estimateInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Estimate/Quote Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('income_estimate_quote_transactions', [
        'id' => $response->json('data.estimate_quote_transaction.id'),
        'company_id' => 1,
        'document_number' => $response->json('data.estimate_quote_transaction.document_number'),
        'invoice_type' => IncomeEstimateQuoteTransaction::ACCOUNTING_INVOICE,
        'gross_value' => 600.00,
    ]);
    $estimateTransaction = IncomeEstimateQuoteTransaction::with('additionalCharges')->whereId($response->json('data.estimate_quote_transaction.id'))->first();
    $this->assertEquals(10, intval($estimateTransaction->additionalCharges->first()->total));
});

test('validation with update negative and wrong cgst and sgst when sale intra state taxable classification for estimate quote transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $estimateInput = IncomeEstimateQuoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'main_classification_nature_type' => 'Sale Intra State Taxable',
        'invoice_type' => IncomeEstimateQuoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $estimateInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    foreach ($estimateInput['ledgers'] as $key => $ledger) {
        $estimateInput['ledgers'][$key]['gst_id'] = $gst->id;
    }

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-estimate-quote', $estimateInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Estimate/Quote Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('income_estimate_quote_transactions', [
        'id' => $response->json('data.estimate_quote_transaction.id'),
        'company_id' => 1,
        'document_number' => $response->json('data.estimate_quote_transaction.document_number'),
        'invoice_type' => IncomeEstimateQuoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $estimateTransaction = IncomeEstimateQuoteTransaction::whereId($response->json('data.estimate_quote_transaction.id'))->first();
    $this->assertEquals(67.04, $estimateTransaction->cgst);
    $this->assertEquals(67.04, $estimateTransaction->sgst);
});

test('validation with update negative and wrong igst when sale inter state taxable classification for estimate quote transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $estimateInput = IncomeEstimateQuoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'main_classification_nature_type' => 'Sale Inter State Taxable',
        'invoice_type' => IncomeEstimateQuoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $estimateInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    foreach ($estimateInput['ledgers'] as $key => $ledger) {
        $estimateInput['ledgers'][$key]['gst_id'] = $gst->id;
    }

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-estimate-quote', $estimateInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Estimate/Quote Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('income_estimate_quote_transactions', [
        'id' => $response->json('data.estimate_quote_transaction.id'),
        'company_id' => 1,
        'document_number' => $response->json('data.estimate_quote_transaction.document_number'),
        'invoice_type' => IncomeEstimateQuoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $estimateTransaction = IncomeEstimateQuoteTransaction::whereId($response->json('data.estimate_quote_transaction.id'))->first();
    $this->assertEquals(134.08, $estimateTransaction->igst);
});

test('validation with update negative and wrong taxable value for estimate quote transaction account invoice', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $estimateInput = IncomeEstimateQuoteTransactionFactory::new()->make([
        'taxable_value' => -100,
        'invoice_type' => IncomeEstimateQuoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-estimate-quote', $estimateInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Estimate/Quote Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('income_estimate_quote_transactions', [
        'id' => $response->json('data.estimate_quote_transaction.id'),
        'company_id' => 1,
        'document_number' => $response->json('data.estimate_quote_transaction.document_number'),
        'invoice_type' => IncomeEstimateQuoteTransaction::ACCOUNTING_INVOICE,
        'taxable_value' => 610.00,
    ]);
});

test('validation with update negative and wrong addless total for estimate quote transaction account invoice', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $estimateInput = IncomeEstimateQuoteTransactionFactory::new()->make([
        'invoice_type' => IncomeEstimateQuoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $estimateInput['add_less'][0]['al_total'] = -100;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-estimate-quote', $estimateInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Estimate/Quote Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('income_estimate_quote_transactions', [
        'id' => $response->json('data.estimate_quote_transaction.id'),
        'company_id' => 1,
        'document_number' => $response->json('data.estimate_quote_transaction.document_number'),
        'invoice_type' => IncomeEstimateQuoteTransaction::ACCOUNTING_INVOICE,
        'gross_value' => 600.00,
    ]);
    $estimateTransaction = IncomeEstimateQuoteTransaction::with('addLess')->whereId($response->json('data.estimate_quote_transaction.id'))->first();
    $this->assertEquals(10, intval($estimateTransaction->addLess->first()->total));
});

test('validation with update negative and wrong grand total for estimate quote transaction account invoice', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $estimateInput = IncomeEstimateQuoteTransactionFactory::new()->make([
        'grand_total' => -100,
        'invoice_type' => IncomeEstimateQuoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-estimate-quote', $estimateInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Estimate/Quote Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('income_estimate_quote_transactions', [
        'id' => $response->json('data.estimate_quote_transaction.id'),
        'company_id' => 1,
        'document_number' => $response->json('data.estimate_quote_transaction.document_number'),
        'invoice_type' => IncomeEstimateQuoteTransaction::ACCOUNTING_INVOICE,
        'grand_total' => 620.00,
    ]);
});

test('validation with update negative and wrong round off amount and method for estimate quote transaction', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);
    $estimateInput = IncomeEstimateQuoteTransactionFactory::new()->make()->toArray();
    $estimateInput['additional_charges'][0]['ac_value'] = 10.54;

    $roundOffMethods = [
        ['method' => 1, 'correct_amount' => 0],
        ['method' => 2, 'correct_amount' => -0.54],
        ['method' => 3, 'correct_amount' => 0.46],
        ['method' => 4, 'correct_amount' => 0.46],
    ];
    foreach ($roundOffMethods as $roundOffMethod) {
        $estimateInput['document_number'] = 'INV-'.$roundOffMethod['method'];
        $estimateInput['round_off_method'] = 0;
        IncomeEstimateQuoteTransactionMaster::whereCompanyId(1)->update(['round_off_method' => $roundOffMethod['method']]);
        $estimateInput['rounding_amount'] = -10.50;

        //act
        $response = postAPI($user->token, $user->company->id, 'api/v2/income-estimate-quote', $estimateInput);

        //assert
        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Estimate/Quote Transaction Created Successfully.',
            ]);
        $this->assertDatabaseHas('income_estimate_quote_transactions', [
            'id' => $response->json('data.estimate_quote_transaction.id'),
            'company_id' => 1,
            'document_number' => $response->json('data.estimate_quote_transaction.document_number'),
            'round_off_method' => $roundOffMethod,
            'round_off_amount' => $roundOffMethod['correct_amount'],
        ]);
    }
});

/*
|----------------------------------------------------------------------------------------------------------------------------------------------------
| Update Estimate Quote Transaction Item Invoice Backend Validation with update Test Case
|----------------------------------------------------------------------------------------------------------------------------------------------------
*/

test('validation with update decimal rpu amount when decimal place is 1 for update estimate quote transaction item invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 1]);
    $estimateInput = IncomeEstimateQuoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $estimateData = StoreEstimateQuoteTransactionAction::run($estimateInput);
    $estimateInput['items'][0]['rpu'] = 100.********;
    $estimateInput['items'][0]['rpu_without_gst'] = 100.********;
    $estimateInput['items'][0]['rpu_with_gst'] = 100.********;
    $estimateInput['items'][0]['gst_id'] = $gst->id;
    $estimateInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-estimate-quote/'.$estimateData['transaction_id'], $estimateInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Estimate Quote Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_estimate_quote_transactions', [
        'id' => $estimateData['transaction_id'],
        'company_id' => 1,
        'document_number' => $estimateInput['document_number'],
        'invoice_type' => IncomeEstimateQuoteTransaction::ITEM_INVOICE,
    ]);
    $estimateTransaction = IncomeEstimateQuoteTransaction::with('transactionItems')->whereId($estimateData['transaction_id'])->first();
    $this->assertEquals(78.2, $estimateTransaction->transactionItems->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 2 for update estimate quote transaction item invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 2]);
    $estimateInput = IncomeEstimateQuoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $estimateData = StoreEstimateQuoteTransactionAction::run($estimateInput);
    $estimateInput['items'][0]['rpu'] = 100.********;
    $estimateInput['items'][0]['rpu_without_gst'] = 100.********;
    $estimateInput['items'][0]['rpu_with_gst'] = 100.********;
    $estimateInput['items'][0]['gst_id'] = $gst->id;
    $estimateInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-estimate-quote/'.$estimateData['transaction_id'], $estimateInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Estimate Quote Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_estimate_quote_transactions', [
        'id' => $estimateData['transaction_id'],
        'company_id' => 1,
        'document_number' => $estimateInput['document_number'],
        'invoice_type' => IncomeEstimateQuoteTransaction::ITEM_INVOICE,
    ]);
    $estimateTransaction = IncomeEstimateQuoteTransaction::with('transactionItems')->whereId($estimateData['transaction_id'])->first();
    $this->assertEquals(78.22, $estimateTransaction->transactionItems->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 3 for update estimate quote transaction item invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 3]);
    $estimateInput = IncomeEstimateQuoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $estimateData = StoreEstimateQuoteTransactionAction::run($estimateInput);
    $estimateInput['items'][0]['rpu'] = 100.********;
    $estimateInput['items'][0]['rpu_without_gst'] = 100.********;
    $estimateInput['items'][0]['rpu_with_gst'] = 100.********;
    $estimateInput['items'][0]['gst_id'] = $gst->id;
    $estimateInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-estimate-quote/'.$estimateData['transaction_id'], $estimateInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Estimate Quote Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_estimate_quote_transactions', [
        'id' => $estimateData['transaction_id'],
        'company_id' => 1,
        'document_number' => $estimateInput['document_number'],
        'invoice_type' => IncomeEstimateQuoteTransaction::ITEM_INVOICE,
    ]);
    $estimateTransaction = IncomeEstimateQuoteTransaction::with('transactionItems')->whereId($estimateData['transaction_id'])->first();
    $this->assertEquals(78.220, $estimateTransaction->transactionItems->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 4 for update estimate quote transaction item invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 4]);
    $estimateInput = IncomeEstimateQuoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $estimateData = StoreEstimateQuoteTransactionAction::run($estimateInput);
    $estimateInput['items'][0]['rpu'] = 100.********;
    $estimateInput['items'][0]['rpu_without_gst'] = 100.********;
    $estimateInput['items'][0]['rpu_with_gst'] = 100.********;
    $estimateInput['items'][0]['gst_id'] = $gst->id;
    $estimateInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-estimate-quote/'.$estimateData['transaction_id'], $estimateInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Estimate Quote Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_estimate_quote_transactions', [
        'id' => $estimateData['transaction_id'],
        'company_id' => 1,
        'document_number' => $estimateInput['document_number'],
        'invoice_type' => IncomeEstimateQuoteTransaction::ITEM_INVOICE,
    ]);
    $estimateTransaction = IncomeEstimateQuoteTransaction::with('transactionItems')->whereId($estimateData['transaction_id'])->first();
    $this->assertEquals(78.2197, $estimateTransaction->transactionItems->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 5 for update estimate quote transaction item invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 5]);
    $estimateInput = IncomeEstimateQuoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $estimateData = StoreEstimateQuoteTransactionAction::run($estimateInput);
    $estimateInput['items'][0]['rpu'] = 100.********;
    $estimateInput['items'][0]['rpu_without_gst'] = 100.********;
    $estimateInput['items'][0]['rpu_with_gst'] = 100.********;
    $estimateInput['items'][0]['gst_id'] = $gst->id;
    $estimateInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-estimate-quote/'.$estimateData['transaction_id'], $estimateInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Estimate Quote Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_estimate_quote_transactions', [
        'id' => $estimateData['transaction_id'],
        'company_id' => 1,
        'document_number' => $estimateInput['document_number'],
        'invoice_type' => IncomeEstimateQuoteTransaction::ITEM_INVOICE,
    ]);
    $estimateTransaction = IncomeEstimateQuoteTransaction::with('transactionItems')->whereId($estimateData['transaction_id'])->first();
    $this->assertEquals(78.21970, $estimateTransaction->transactionItems->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 1 for update estimate quote transaction item invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 1]);
    $estimateInput = IncomeEstimateQuoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $estimateData = StoreEstimateQuoteTransactionAction::run($estimateInput);
    $estimateInput['items'][0]['rpu'] = 100.********;
    $estimateInput['items'][0]['rpu_without_gst'] = 100.********;
    $estimateInput['items'][0]['rpu_with_gst'] = 100.********;
    $estimateInput['items'][0]['gst_id'] = $gst->id;
    $estimateInput['items'][0]['gst_tax_percentage'] = 2;
    $estimateInput['items'][0]['with_tax'] = false;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-estimate-quote/'.$estimateData['transaction_id'], $estimateInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Estimate Quote Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_estimate_quote_transactions', [
        'id' => $estimateData['transaction_id'],
        'company_id' => 1,
        'document_number' => $estimateInput['document_number'],
        'invoice_type' => IncomeEstimateQuoteTransaction::ITEM_INVOICE,
    ]);
    $estimateTransaction = IncomeEstimateQuoteTransaction::with('transactionItems')->whereId($estimateData['transaction_id'])->first();
    $this->assertEquals(128.2, $estimateTransaction->transactionItems->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 2 for update estimate quote transaction item invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 2]);
    $estimateInput = IncomeEstimateQuoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $estimateData = StoreEstimateQuoteTransactionAction::run($estimateInput);
    $estimateInput['items'][0]['rpu'] = 100.********;
    $estimateInput['items'][0]['rpu_without_gst'] = 100.********;
    $estimateInput['items'][0]['rpu_with_gst'] = 100.********;
    $estimateInput['items'][0]['gst_id'] = $gst->id;
    $estimateInput['items'][0]['gst_tax_percentage'] = 2;
    $estimateInput['items'][0]['with_tax'] = false;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-estimate-quote/'.$estimateData['transaction_id'], $estimateInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Estimate Quote Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_estimate_quote_transactions', [
        'id' => $estimateData['transaction_id'],
        'company_id' => 1,
        'document_number' => $estimateInput['document_number'],
        'invoice_type' => IncomeEstimateQuoteTransaction::ITEM_INVOICE,
    ]);
    $estimateTransaction = IncomeEstimateQuoteTransaction::with('transactionItems')->whereId($estimateData['transaction_id'])->first();
    $this->assertEquals(128.16, $estimateTransaction->transactionItems->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 3 for update estimate quote transaction item invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 3]);
    $estimateInput = IncomeEstimateQuoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $estimateData = StoreEstimateQuoteTransactionAction::run($estimateInput);
    $estimateInput['items'][0]['rpu'] = 100.********;
    $estimateInput['items'][0]['rpu_without_gst'] = 100.********;
    $estimateInput['items'][0]['rpu_with_gst'] = 100.********;
    $estimateInput['items'][0]['gst_id'] = $gst->id;
    $estimateInput['items'][0]['gst_tax_percentage'] = 2;
    $estimateInput['items'][0]['with_tax'] = false;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-estimate-quote/'.$estimateData['transaction_id'], $estimateInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Estimate Quote Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_estimate_quote_transactions', [
        'id' => $estimateData['transaction_id'],
        'company_id' => 1,
        'document_number' => $estimateInput['document_number'],
        'invoice_type' => IncomeEstimateQuoteTransaction::ITEM_INVOICE,
    ]);
    $estimateTransaction = IncomeEstimateQuoteTransaction::with('transactionItems')->whereId($estimateData['transaction_id'])->first();
    $this->assertEquals(128.155, $estimateTransaction->transactionItems->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 4 for update estimate quote transaction item invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 4]);
    $estimateInput = IncomeEstimateQuoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $estimateData = StoreEstimateQuoteTransactionAction::run($estimateInput);
    $estimateInput['items'][0]['rpu'] = 100.********;
    $estimateInput['items'][0]['rpu_without_gst'] = 100.********;
    $estimateInput['items'][0]['rpu_with_gst'] = 100.********;
    $estimateInput['items'][0]['gst_id'] = $gst->id;
    $estimateInput['items'][0]['gst_tax_percentage'] = 2;
    $estimateInput['items'][0]['with_tax'] = false;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-estimate-quote/'.$estimateData['transaction_id'], $estimateInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Estimate Quote Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_estimate_quote_transactions', [
        'id' => $estimateData['transaction_id'],
        'company_id' => 1,
        'document_number' => $estimateInput['document_number'],
        'invoice_type' => IncomeEstimateQuoteTransaction::ITEM_INVOICE,
    ]);
    $estimateTransaction = IncomeEstimateQuoteTransaction::with('transactionItems')->whereId($estimateData['transaction_id'])->first();
    $this->assertEquals(128.1552, $estimateTransaction->transactionItems->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 5 for update estimate quote transaction item invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 5]);
    $estimateInput = IncomeEstimateQuoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $estimateData = StoreEstimateQuoteTransactionAction::run($estimateInput);
    $estimateInput['items'][0]['rpu'] = 100.********;
    $estimateInput['items'][0]['rpu_without_gst'] = 100.********;
    $estimateInput['items'][0]['rpu_with_gst'] = 100.********;
    $estimateInput['items'][0]['gst_id'] = $gst->id;
    $estimateInput['items'][0]['gst_tax_percentage'] = 2;
    $estimateInput['items'][0]['with_tax'] = false;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-estimate-quote/'.$estimateData['transaction_id'], $estimateInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Estimate Quote Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_estimate_quote_transactions', [
        'id' => $estimateData['transaction_id'],
        'company_id' => 1,
        'document_number' => $estimateInput['document_number'],
        'invoice_type' => IncomeEstimateQuoteTransaction::ITEM_INVOICE,
    ]);
    $estimateTransaction = IncomeEstimateQuoteTransaction::with('transactionItems')->whereId($estimateData['transaction_id'])->first();
    $this->assertEquals(128.15515, $estimateTransaction->transactionItems->first()->rpu_with_gst);
});

test('validation with update negative and wrong rpu without or with gst for update estimate quote transaction item invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $estimateInput = IncomeEstimateQuoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $estimateData = StoreEstimateQuoteTransactionAction::run($estimateInput);
    $estimateInput['items'][0]['rpu_without_gst'] = -10000;
    $estimateInput['items'][0]['rpu_with_gst'] = -10000;
    $estimateInput['items'][0]['gst_id'] = $gst->id;
    $estimateInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-estimate-quote/'.$estimateData['transaction_id'], $estimateInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Estimate Quote Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_estimate_quote_transactions', [
        'id' => $estimateData['transaction_id'],
        'company_id' => 1,
        'document_number' => $estimateInput['document_number'],
        'invoice_type' => IncomeEstimateQuoteTransaction::ITEM_INVOICE,
    ]);
    $estimateTransaction = IncomeEstimateQuoteTransaction::with('transactionItems')->whereId($estimateData['transaction_id'])->first();
    $this->assertNotEquals(100, $estimateTransaction->transactionItems->first()->rpu_without_gst);
    $this->assertEquals(100.0, $estimateTransaction->transactionItems->first()->rpu_with_gst);
});

test('validation with update negative and wrong gst tax percentage for update estimate quote transaction item invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $estimateInput = IncomeEstimateQuoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $estimateData = StoreEstimateQuoteTransactionAction::run($estimateInput);
    $estimateInput['items'][0]['total'] = -10000;
    $estimateInput['items'][0]['gst_id'] = $gst->id;
    $estimateInput['items'][0]['gst_tax_percentage'] = $gst->tax_rate;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-estimate-quote/'.$estimateData['transaction_id'], $estimateInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Estimate Quote Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_estimate_quote_transactions', [
        'id' => $estimateData['transaction_id'],
        'company_id' => 1,
        'document_number' => $estimateInput['document_number'],
        'invoice_type' => IncomeEstimateQuoteTransaction::ITEM_INVOICE,
    ]);
    $estimateTransaction = IncomeEstimateQuoteTransaction::with('transactionItems')->whereId($estimateData['transaction_id'])->first();
    $this->assertEquals(28, intval($estimateTransaction->transactionItems->first()->gst_tax_percentage));
});

test('validation with update negative and wrong total discount for update estimate quote transaction item invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(5.00)->first();

    $estimateInput = IncomeEstimateQuoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $estimateData = StoreEstimateQuoteTransactionAction::run($estimateInput);
    foreach ($estimateInput['items'] as $key => $item) {
        $estimateInput['items'][$key]['gst_id'] = $gst->id;
        $estimateInput['items'][$key]['discount_type'] = 2;
        $estimateInput['items'][$key]['discount_value'] = 10;
        $estimateInput['items'][$key]['discount_type_2'] = 2;
        $estimateInput['items'][$key]['discount_value_2'] = 10;
        $estimateInput['items'][$key]['total_discount_amount'] = 10;
    }

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-estimate-quote/'.$estimateData['transaction_id'], $estimateInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Estimate Quote Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_estimate_quote_transactions', [
        'id' => $estimateData['transaction_id'],
        'company_id' => 1,
        'document_number' => $estimateInput['document_number'],
        'invoice_type' => IncomeEstimateQuoteTransaction::ITEM_INVOICE,
    ]);
    $estimateTransaction = IncomeEstimateQuoteTransaction::with('transactionItems')->whereId($estimateData['transaction_id'])->first();
    foreach ($estimateTransaction->transactionItems as $item) {
        $this->assertEquals(18.1, $item->total_discount_amount);
    }
});

test('validation with update negative and wrong sub total for update estimate quote transaction item invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $estimateInput = IncomeEstimateQuoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $estimateData = StoreEstimateQuoteTransactionAction::run($estimateInput);
    $estimateInput['items'][0]['total'] = -10000;
    $estimateInput['items'][0]['gst_id'] = $gst->id;
    $estimateInput['items'][0]['gst_tax_percentage'] = $gst->tax_rate;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-estimate-quote/'.$estimateData['transaction_id'], $estimateInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Estimate Quote Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_estimate_quote_transactions', [
        'id' => $estimateData['transaction_id'],
        'company_id' => 1,
        'document_number' => $estimateInput['document_number'],
        'invoice_type' => IncomeEstimateQuoteTransaction::ITEM_INVOICE,
    ]);
    $estimateTransaction = IncomeEstimateQuoteTransaction::with('transactionItems')->whereId($estimateData['transaction_id'])->first();
    $this->assertEquals(78.13, $estimateTransaction->transactionItems->first()->total);
});

test('validation with update negative and wrong sub total for update estimate quote transaction item invoice', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $estimateInput = IncomeEstimateQuoteTransactionFactory::new()->make()->toArray();
    $estimateData = StoreEstimateQuoteTransactionAction::run($estimateInput);
    $estimateInput['items'][0]['total'] = -10000;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-estimate-quote/'.$estimateData['transaction_id'], $estimateInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Estimate Quote Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_estimate_quote_transactions', [
        'id' => $estimateData['transaction_id'],
        'company_id' => 1,
        'document_number' => $estimateInput['document_number'],
        'invoice_type' => IncomeEstimateQuoteTransaction::ITEM_INVOICE,
    ]);
    $estimateTransaction = IncomeEstimateQuoteTransaction::with('transactionItems')->whereId($estimateData['transaction_id'])->first();
    $this->assertEquals(100, intval($estimateTransaction->transactionItems->first()->total));
});

test('validation with update negative and wrong total for update estimate quote transaction item invoice', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $estimateInput = IncomeEstimateQuoteTransactionFactory::new()->make()->toArray();
    $estimateData = StoreEstimateQuoteTransactionAction::run($estimateInput);
    $estimateInput['total'] = -620;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-estimate-quote/'.$estimateData['transaction_id'], $estimateInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Estimate Quote Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_estimate_quote_transactions', [
        'id' => $estimateData['transaction_id'],
        'company_id' => 1,
        'document_number' => $estimateInput['document_number'],
        'invoice_type' => IncomeEstimateQuoteTransaction::ITEM_INVOICE,
        'total' => 620.00,
    ]);
});

test('validation with update negative and wrong gross value for update estimate quote transaction item invoice', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $estimateInput = IncomeEstimateQuoteTransactionFactory::new()->make()->toArray();
    $estimateData = StoreEstimateQuoteTransactionAction::run($estimateInput);
    $estimateInput['gross_value'] = -100;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-estimate-quote/'.$estimateData['transaction_id'], $estimateInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Estimate Quote Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_estimate_quote_transactions', [
        'id' => $estimateData['transaction_id'],
        'company_id' => 1,
        'document_number' => $estimateInput['document_number'],
        'invoice_type' => IncomeEstimateQuoteTransaction::ITEM_INVOICE,
        'gross_value' => 600.00,
    ]);
});

test('validation with update negative and wrong additional charges gst tax percentage for update estimate quote transaction item invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $estimateInput = IncomeEstimateQuoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $estimateData = StoreEstimateQuoteTransactionAction::run($estimateInput);
    $estimateInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    $estimateInput['additional_charges'][0]['ac_total'] = -2000;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-estimate-quote/'.$estimateData['transaction_id'], $estimateInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Estimate Quote Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_estimate_quote_transactions', [
        'id' => $estimateData['transaction_id'],
        'company_id' => 1,
        'document_number' => $estimateInput['document_number'],
        'invoice_type' => IncomeEstimateQuoteTransaction::ITEM_INVOICE,
    ]);
    $estimateTransaction = IncomeEstimateQuoteTransaction::with('additionalCharges')->whereId($estimateData['transaction_id'])->first();
    $this->assertNotEquals(10.00, $estimateTransaction->additionalCharges->first()->total);
});

test('validation with update negative and wrong additional charges total for update estimate quote transaction item invoice', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $estimateInput = IncomeEstimateQuoteTransactionFactory::new()->make()->toArray();
    $estimateData = StoreEstimateQuoteTransactionAction::run($estimateInput);
    $estimateInput['additional_charges'][0]['ac_total'] = -200;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-estimate-quote/'.$estimateData['transaction_id'], $estimateInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Estimate Quote Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_estimate_quote_transactions', [
        'id' => $estimateData['transaction_id'],
        'company_id' => 1,
        'document_number' => $estimateInput['document_number'],
        'invoice_type' => IncomeEstimateQuoteTransaction::ITEM_INVOICE,
        'gross_value' => 600.00,
    ]);
    $estimateTransaction = IncomeEstimateQuoteTransaction::with('additionalCharges')->whereId($estimateData['transaction_id'])->first();
    $this->assertEquals(10, intval($estimateTransaction->additionalCharges->first()->total));
});

test('validation with update negative and wrong cgst and sgst when sale intra state taxable classification for update estimate quote transaction item invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $estimateInput = IncomeEstimateQuoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'main_classification_nature_type' => 'Sale Intra State Taxable',
    ])->toArray();
    $estimateData = StoreEstimateQuoteTransactionAction::run($estimateInput);
    $estimateInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    foreach ($estimateInput['items'] as $key => $item) {
        $estimateInput['items'][$key]['gst_id'] = $gst->id;
    }

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-estimate-quote/'.$estimateData['transaction_id'], $estimateInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Estimate Quote Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_estimate_quote_transactions', [
        'id' => $estimateData['transaction_id'],
        'company_id' => 1,
        'document_number' => $estimateInput['document_number'],
        'invoice_type' => IncomeEstimateQuoteTransaction::ITEM_INVOICE,
    ]);
    $estimateTransaction = IncomeEstimateQuoteTransaction::whereId($estimateData['transaction_id'])->first();
    $this->assertEquals(67.04, $estimateTransaction->cgst);
    $this->assertEquals(67.04, $estimateTransaction->sgst);
});

test('validation with update negative and wrong igst when sale inter state taxable classification for update estimate quote transaction item invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $estimateInput = IncomeEstimateQuoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'main_classification_nature_type' => 'Sale Inter State Taxable',
    ])->toArray();
    $estimateData = StoreEstimateQuoteTransactionAction::run($estimateInput);
    $estimateInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    foreach ($estimateInput['items'] as $key => $item) {
        $estimateInput['items'][$key]['gst_id'] = $gst->id;
    }

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-estimate-quote/'.$estimateData['transaction_id'], $estimateInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Estimate Quote Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_estimate_quote_transactions', [
        'id' => $estimateData['transaction_id'],
        'company_id' => 1,
        'document_number' => $estimateInput['document_number'],
        'invoice_type' => IncomeEstimateQuoteTransaction::ITEM_INVOICE,
    ]);
    $estimateTransaction = IncomeEstimateQuoteTransaction::whereId($estimateData['transaction_id'])->first();
    $this->assertEquals(134.08, $estimateTransaction->igst);
});

test('validation with update negative and wrong taxable value for update estimate quote transaction item invoice', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $estimateInput = IncomeEstimateQuoteTransactionFactory::new()->make()->toArray();
    $estimateData = StoreEstimateQuoteTransactionAction::run($estimateInput);
    $estimateInput['taxable_value'] = -100;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-estimate-quote/'.$estimateData['transaction_id'], $estimateInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Estimate Quote Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_estimate_quote_transactions', [
        'id' => $estimateData['transaction_id'],
        'company_id' => 1,
        'document_number' => $estimateInput['document_number'],
        'invoice_type' => IncomeEstimateQuoteTransaction::ITEM_INVOICE,
        'taxable_value' => 610.00,
    ]);
});

test('validation with update negative and wrong addless total for update estimate quote transaction item invoice', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $estimateInput = IncomeEstimateQuoteTransactionFactory::new()->make()->toArray();
    $estimateData = StoreEstimateQuoteTransactionAction::run($estimateInput);
    $estimateInput['add_less'][0]['al_total'] = -100;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-estimate-quote/'.$estimateData['transaction_id'], $estimateInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Estimate Quote Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_estimate_quote_transactions', [
        'id' => $estimateData['transaction_id'],
        'company_id' => 1,
        'document_number' => $estimateInput['document_number'],
        'invoice_type' => IncomeEstimateQuoteTransaction::ITEM_INVOICE,
        'gross_value' => 600.00,
    ]);
    $estimateTransaction = IncomeEstimateQuoteTransaction::with('addLess')->whereId($estimateData['transaction_id'])->first();
    $this->assertEquals(10, intval($estimateTransaction->addLess->first()->total));
});

test('validation with update negative and wrong grand total for update estimate quote transaction item invoice', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $estimateInput = IncomeEstimateQuoteTransactionFactory::new()->make()->toArray();
    $estimateData = StoreEstimateQuoteTransactionAction::run($estimateInput);
    $estimateInput['grand_total'] = -100;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-estimate-quote/'.$estimateData['transaction_id'], $estimateInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Estimate Quote Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_estimate_quote_transactions', [
        'id' => $estimateData['transaction_id'],
        'company_id' => 1,
        'document_number' => $estimateInput['document_number'],
        'invoice_type' => IncomeEstimateQuoteTransaction::ITEM_INVOICE,
        'grand_total' => 620.00,
    ]);
});

/*
|----------------------------------------------------------------------------------------------------------------------------------------------------
| Update Estimate Quote Transaction Account Invoice Backend Validation with update Test Case
|----------------------------------------------------------------------------------------------------------------------------------------------------
*/

test('validation with update decimal rpu amount when decimal place is 1 for update estimate quote transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $estimateInput = IncomeEstimateQuoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'invoice_type' => IncomeEstimateQuoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $estimateData = StoreEstimateQuoteTransactionAction::run($estimateInput);
    $estimateInput['ledgers'][0]['decimal'] = 1;
    $estimateInput['ledgers'][0]['rpu'] = 100.********;
    $estimateInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $estimateInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $estimateInput['ledgers'][0]['gst_id'] = $gst->id;
    $estimateInput['ledgers'][0]['gst_tax_percentage'] = 2;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-estimate-quote/'.$estimateData['transaction_id'], $estimateInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Estimate Quote Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_estimate_quote_transactions', [
        'id' => $estimateData['transaction_id'],
        'company_id' => 1,
        'document_number' => $estimateInput['document_number'],
        'invoice_type' => IncomeEstimateQuoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $estimateTransaction = IncomeEstimateQuoteTransaction::with('transactionLedgers')->whereId($estimateData['transaction_id'])->first();
    $this->assertEquals(78.2, $estimateTransaction->transactionLedgers->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 2 for update estimate quote transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $estimateInput = IncomeEstimateQuoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'invoice_type' => IncomeEstimateQuoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $estimateData = StoreEstimateQuoteTransactionAction::run($estimateInput);
    $estimateInput['ledgers'][0]['decimal'] = 2;
    $estimateInput['ledgers'][0]['rpu'] = 100.********;
    $estimateInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $estimateInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $estimateInput['ledgers'][0]['gst_id'] = $gst->id;
    $estimateInput['ledgers'][0]['gst_tax_percentage'] = 2;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-estimate-quote/'.$estimateData['transaction_id'], $estimateInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Estimate Quote Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_estimate_quote_transactions', [
        'id' => $estimateData['transaction_id'],
        'company_id' => 1,
        'document_number' => $estimateInput['document_number'],
        'invoice_type' => IncomeEstimateQuoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $estimateTransaction = IncomeEstimateQuoteTransaction::with('transactionLedgers')->whereId($estimateData['transaction_id'])->first();
    $this->assertEquals(78.22, $estimateTransaction->transactionLedgers->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 3 for update estimate quote transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $estimateInput = IncomeEstimateQuoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'invoice_type' => IncomeEstimateQuoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $estimateData = StoreEstimateQuoteTransactionAction::run($estimateInput);
    $estimateInput['ledgers'][0]['decimal'] = 3;
    $estimateInput['ledgers'][0]['rpu'] = 100.********;
    $estimateInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $estimateInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $estimateInput['ledgers'][0]['gst_id'] = $gst->id;
    $estimateInput['ledgers'][0]['gst_tax_percentage'] = 2;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-estimate-quote/'.$estimateData['transaction_id'], $estimateInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Estimate Quote Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_estimate_quote_transactions', [
        'id' => $estimateData['transaction_id'],
        'company_id' => 1,
        'document_number' => $estimateInput['document_number'],
        'invoice_type' => IncomeEstimateQuoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $estimateTransaction = IncomeEstimateQuoteTransaction::with('transactionLedgers')->whereId($estimateData['transaction_id'])->first();
    $this->assertEquals(78.220, $estimateTransaction->transactionLedgers->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 4 for update estimate quote transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $estimateInput = IncomeEstimateQuoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'invoice_type' => IncomeEstimateQuoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $estimateData = StoreEstimateQuoteTransactionAction::run($estimateInput);
    $estimateInput['ledgers'][0]['decimal'] = 4;
    $estimateInput['ledgers'][0]['rpu'] = 100.********;
    $estimateInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $estimateInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $estimateInput['ledgers'][0]['gst_id'] = $gst->id;
    $estimateInput['ledgers'][0]['gst_tax_percentage'] = 2;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-estimate-quote/'.$estimateData['transaction_id'], $estimateInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Estimate Quote Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_estimate_quote_transactions', [
        'id' => $estimateData['transaction_id'],
        'company_id' => 1,
        'document_number' => $estimateInput['document_number'],
        'invoice_type' => IncomeEstimateQuoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $estimateTransaction = IncomeEstimateQuoteTransaction::with('transactionLedgers')->whereId($estimateData['transaction_id'])->first();
    $this->assertEquals(78.2197, $estimateTransaction->transactionLedgers->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 5 for update estimate quote transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $estimateInput = IncomeEstimateQuoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'invoice_type' => IncomeEstimateQuoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $estimateData = StoreEstimateQuoteTransactionAction::run($estimateInput);
    $estimateInput['ledgers'][0]['decimal'] = 5;
    $estimateInput['ledgers'][0]['rpu'] = 100.********;
    $estimateInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $estimateInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $estimateInput['ledgers'][0]['gst_id'] = $gst->id;
    $estimateInput['ledgers'][0]['gst_tax_percentage'] = 2;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-estimate-quote/'.$estimateData['transaction_id'], $estimateInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Estimate Quote Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_estimate_quote_transactions', [
        'id' => $estimateData['transaction_id'],
        'company_id' => 1,
        'document_number' => $estimateInput['document_number'],
        'invoice_type' => IncomeEstimateQuoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $estimateTransaction = IncomeEstimateQuoteTransaction::with('transactionLedgers')->whereId($estimateData['transaction_id'])->first();
    $this->assertEquals(78.21970, $estimateTransaction->transactionLedgers->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 1 for update estimate quote transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $estimateInput = IncomeEstimateQuoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'invoice_type' => IncomeEstimateQuoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $estimateData = StoreEstimateQuoteTransactionAction::run($estimateInput);
    $estimateInput['ledgers'][0]['decimal'] = 1;
    $estimateInput['ledgers'][0]['rpu'] = 100.********;
    $estimateInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $estimateInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $estimateInput['ledgers'][0]['gst_id'] = $gst->id;
    $estimateInput['ledgers'][0]['gst_tax_percentage'] = 2;
    $estimateInput['ledgers'][0]['with_tax'] = false;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-estimate-quote/'.$estimateData['transaction_id'], $estimateInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Estimate Quote Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_estimate_quote_transactions', [
        'id' => $estimateData['transaction_id'],
        'company_id' => 1,
        'document_number' => $estimateInput['document_number'],
        'invoice_type' => IncomeEstimateQuoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $estimateTransaction = IncomeEstimateQuoteTransaction::with('transactionLedgers')->whereId($estimateData['transaction_id'])->first();
    $this->assertEquals(128.2, $estimateTransaction->transactionLedgers->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 2 for update estimate quote transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $estimateInput = IncomeEstimateQuoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'invoice_type' => IncomeEstimateQuoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $estimateData = StoreEstimateQuoteTransactionAction::run($estimateInput);
    $estimateInput['ledgers'][0]['decimal'] = 2;
    $estimateInput['ledgers'][0]['rpu'] = 100.********;
    $estimateInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $estimateInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $estimateInput['ledgers'][0]['gst_id'] = $gst->id;
    $estimateInput['ledgers'][0]['gst_tax_percentage'] = 2;
    $estimateInput['ledgers'][0]['with_tax'] = false;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-estimate-quote/'.$estimateData['transaction_id'], $estimateInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Estimate Quote Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_estimate_quote_transactions', [
        'id' => $estimateData['transaction_id'],
        'company_id' => 1,
        'document_number' => $estimateInput['document_number'],
        'invoice_type' => IncomeEstimateQuoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $estimateTransaction = IncomeEstimateQuoteTransaction::with('transactionLedgers')->whereId($estimateData['transaction_id'])->first();
    $this->assertEquals(128.16, $estimateTransaction->transactionLedgers->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 3 for update estimate quote transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $estimateInput = IncomeEstimateQuoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'invoice_type' => IncomeEstimateQuoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $estimateData = StoreEstimateQuoteTransactionAction::run($estimateInput);
    $estimateInput['ledgers'][0]['decimal'] = 3;
    $estimateInput['ledgers'][0]['rpu'] = 100.********;
    $estimateInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $estimateInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $estimateInput['ledgers'][0]['gst_id'] = $gst->id;
    $estimateInput['ledgers'][0]['gst_tax_percentage'] = 2;
    $estimateInput['ledgers'][0]['with_tax'] = false;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-estimate-quote/'.$estimateData['transaction_id'], $estimateInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Estimate Quote Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_estimate_quote_transactions', [
        'id' => $estimateData['transaction_id'],
        'company_id' => 1,
        'document_number' => $estimateInput['document_number'],
        'invoice_type' => IncomeEstimateQuoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $estimateTransaction = IncomeEstimateQuoteTransaction::with('transactionLedgers')->whereId($estimateData['transaction_id'])->first();
    $this->assertEquals(128.155, $estimateTransaction->transactionLedgers->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 4 for update estimate quote transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $estimateInput = IncomeEstimateQuoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'invoice_type' => IncomeEstimateQuoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $estimateData = StoreEstimateQuoteTransactionAction::run($estimateInput);
    $estimateInput['ledgers'][0]['decimal'] = 4;
    $estimateInput['ledgers'][0]['rpu'] = 100.********;
    $estimateInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $estimateInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $estimateInput['ledgers'][0]['gst_id'] = $gst->id;
    $estimateInput['ledgers'][0]['gst_tax_percentage'] = 2;
    $estimateInput['ledgers'][0]['with_tax'] = false;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-estimate-quote/'.$estimateData['transaction_id'], $estimateInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Estimate Quote Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_estimate_quote_transactions', [
        'id' => $estimateData['transaction_id'],
        'company_id' => 1,
        'document_number' => $estimateInput['document_number'],
        'invoice_type' => IncomeEstimateQuoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $estimateTransaction = IncomeEstimateQuoteTransaction::with('transactionLedgers')->whereId($estimateData['transaction_id'])->first();
    $this->assertEquals(128.1552, $estimateTransaction->transactionLedgers->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 5 for update estimate quote transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $estimateInput = IncomeEstimateQuoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'invoice_type' => IncomeEstimateQuoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $estimateData = StoreEstimateQuoteTransactionAction::run($estimateInput);
    $estimateInput['ledgers'][0]['decimal'] = 5;
    $estimateInput['ledgers'][0]['rpu'] = 100.********;
    $estimateInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $estimateInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $estimateInput['ledgers'][0]['gst_id'] = $gst->id;
    $estimateInput['ledgers'][0]['gst_tax_percentage'] = 2;
    $estimateInput['ledgers'][0]['with_tax'] = false;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-estimate-quote/'.$estimateData['transaction_id'], $estimateInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Estimate Quote Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_estimate_quote_transactions', [
        'id' => $estimateData['transaction_id'],
        'company_id' => 1,
        'document_number' => $estimateInput['document_number'],
        'invoice_type' => IncomeEstimateQuoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $estimateTransaction = IncomeEstimateQuoteTransaction::with('transactionLedgers')->whereId($estimateData['transaction_id'])->first();
    $this->assertEquals(128.15515, $estimateTransaction->transactionLedgers->first()->rpu_with_gst);
});

test('validation with update negative and wrong rpu without or with gst for update estimate quote transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $estimateInput = IncomeEstimateQuoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'invoice_type' => IncomeEstimateQuoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $estimateData = StoreEstimateQuoteTransactionAction::run($estimateInput);
    $estimateInput['ledgers'][0]['rpu_without_gst'] = -10000;
    $estimateInput['ledgers'][0]['rpu_with_gst'] = -10000;
    $estimateInput['ledgers'][0]['gst_id'] = $gst->id;
    $estimateInput['ledgers'][0]['gst_tax_percentage'] = 2;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-estimate-quote/'.$estimateData['transaction_id'], $estimateInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Estimate Quote Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_estimate_quote_transactions', [
        'id' => $estimateData['transaction_id'],
        'company_id' => 1,
        'document_number' => $estimateInput['document_number'],
        'invoice_type' => IncomeEstimateQuoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $estimateTransaction = IncomeEstimateQuoteTransaction::with('transactionLedgers')->whereId($estimateData['transaction_id'])->first();
    $this->assertNotEquals(100, $estimateTransaction->transactionLedgers->first()->rpu_without_gst);
    $this->assertEquals(100.0, $estimateTransaction->transactionLedgers->first()->rpu_with_gst);
});

test('validation with update negative and wrong gst tax percentage for update estimate quote transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $estimateInput = IncomeEstimateQuoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'invoice_type' => IncomeEstimateQuoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $estimateData = StoreEstimateQuoteTransactionAction::run($estimateInput);
    $estimateInput['ledgers'][0]['total'] = -10000;
    $estimateInput['ledgers'][0]['gst_id'] = $gst->id;
    $estimateInput['ledgers'][0]['gst_tax_percentage'] = $gst->tax_rate;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-estimate-quote/'.$estimateData['transaction_id'], $estimateInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Estimate Quote Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_estimate_quote_transactions', [
        'id' => $estimateData['transaction_id'],
        'company_id' => 1,
        'document_number' => $estimateInput['document_number'],
        'invoice_type' => IncomeEstimateQuoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $estimateTransaction = IncomeEstimateQuoteTransaction::with('transactionLedgers')->whereId($estimateData['transaction_id'])->first();
    $this->assertEquals(28, intval($estimateTransaction->transactionLedgers->first()->gst_tax_percentage));
});

test('validation with update negative and wrong total discount for update estimate quote transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(5.00)->first();

    $estimateInput = IncomeEstimateQuoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'invoice_type' => IncomeEstimateQuoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $estimateData = StoreEstimateQuoteTransactionAction::run($estimateInput);
    foreach ($estimateInput['ledgers'] as $key => $ledger) {
        $estimateInput['ledgers'][$key]['gst_id'] = $gst->id;
        $estimateInput['ledgers'][$key]['discount_type'] = 2;
        $estimateInput['ledgers'][$key]['discount_value'] = 10;
        $estimateInput['ledgers'][$key]['discount_type_2'] = 2;
        $estimateInput['ledgers'][$key]['discount_value_2'] = 10;
        $estimateInput['ledgers'][$key]['total_discount_amount'] = 10;
    }

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-estimate-quote/'.$estimateData['transaction_id'], $estimateInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Estimate Quote Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_estimate_quote_transactions', [
        'id' => $estimateData['transaction_id'],
        'company_id' => 1,
        'document_number' => $estimateInput['document_number'],
        'invoice_type' => IncomeEstimateQuoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $estimateTransaction = IncomeEstimateQuoteTransaction::with('transactionLedgers')->whereId($estimateData['transaction_id'])->first();
    foreach ($estimateTransaction->transactionLedgers as $ledger) {
        $this->assertEquals(18.1, $ledger->total_discount_amount);
    }
});

test('validation with update negative and wrong sub total for update estimate quote transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $estimateInput = IncomeEstimateQuoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'invoice_type' => IncomeEstimateQuoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $estimateData = StoreEstimateQuoteTransactionAction::run($estimateInput);
    $estimateInput['ledgers'][0]['total'] = -10000;
    $estimateInput['ledgers'][0]['gst_id'] = $gst->id;
    $estimateInput['ledgers'][0]['gst_tax_percentage'] = $gst->tax_rate;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-estimate-quote/'.$estimateData['transaction_id'], $estimateInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Estimate Quote Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_estimate_quote_transactions', [
        'id' => $estimateData['transaction_id'],
        'company_id' => 1,
        'document_number' => $estimateInput['document_number'],
        'invoice_type' => IncomeEstimateQuoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $estimateTransaction = IncomeEstimateQuoteTransaction::with('transactionLedgers')->whereId($estimateData['transaction_id'])->first();
    $this->assertEquals(78.13, $estimateTransaction->transactionLedgers->first()->total);
});

test('validation with update negative and wrong sub total for update estimate quote transaction account invoice', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $estimateInput = IncomeEstimateQuoteTransactionFactory::new()->make(['invoice_type' => IncomeEstimateQuoteTransaction::ACCOUNTING_INVOICE])->toArray();
    $estimateData = StoreEstimateQuoteTransactionAction::run($estimateInput);
    $estimateInput['ledgers'][0]['total'] = -10000;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-estimate-quote/'.$estimateData['transaction_id'], $estimateInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Estimate Quote Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_estimate_quote_transactions', [
        'id' => $estimateData['transaction_id'],
        'company_id' => 1,
        'document_number' => $estimateInput['document_number'],
        'invoice_type' => IncomeEstimateQuoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $estimateTransaction = IncomeEstimateQuoteTransaction::with('transactionLedgers')->whereId($estimateData['transaction_id'])->first();
    $this->assertEquals(100, intval($estimateTransaction->transactionLedgers->first()->total));
});

test('validation with update negative and wrong total for update estimate quote transaction account invoice', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $estimateInput = IncomeEstimateQuoteTransactionFactory::new()->make([
        'invoice_type' => IncomeEstimateQuoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $estimateData = StoreEstimateQuoteTransactionAction::run($estimateInput);
    $estimateInput['total'] = 620;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-estimate-quote/'.$estimateData['transaction_id'], $estimateInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Estimate Quote Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_estimate_quote_transactions', [
        'id' => $estimateData['transaction_id'],
        'company_id' => 1,
        'document_number' => $estimateInput['document_number'],
        'invoice_type' => IncomeEstimateQuoteTransaction::ACCOUNTING_INVOICE,
        'total' => 620.00,
    ]);
});

test('validation with update negative and wrong gross value for update estimate quote transaction account invoice', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $estimateInput = IncomeEstimateQuoteTransactionFactory::new()->make([
        'invoice_type' => IncomeEstimateQuoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $estimateData = StoreEstimateQuoteTransactionAction::run($estimateInput);
    $estimateInput['gross_value'] = -100;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-estimate-quote/'.$estimateData['transaction_id'], $estimateInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Estimate Quote Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_estimate_quote_transactions', [
        'id' => $estimateData['transaction_id'],
        'company_id' => 1,
        'document_number' => $estimateInput['document_number'],
        'invoice_type' => IncomeEstimateQuoteTransaction::ACCOUNTING_INVOICE,
        'gross_value' => 600.00,
    ]);
});

test('validation with update negative and wrong additional charges gst tax percentage for update estimate quote transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $estimateInput = IncomeEstimateQuoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'invoice_type' => IncomeEstimateQuoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $estimateData = StoreEstimateQuoteTransactionAction::run($estimateInput);
    $estimateInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    $estimateInput['additional_charges'][0]['ac_total'] = -2000;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-estimate-quote/'.$estimateData['transaction_id'], $estimateInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Estimate Quote Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_estimate_quote_transactions', [
        'id' => $estimateData['transaction_id'],
        'company_id' => 1,
        'document_number' => $estimateInput['document_number'],
        'invoice_type' => IncomeEstimateQuoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $estimateTransaction = IncomeEstimateQuoteTransaction::with('additionalCharges')->whereId($estimateData['transaction_id'])->first();
    $this->assertNotEquals(10.00, $estimateTransaction->additionalCharges->first()->total);
});

test('validation with update negative and wrong additional charges total for update estimate quote transaction account invoice', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $estimateInput = IncomeEstimateQuoteTransactionFactory::new()->make(['invoice_type' => IncomeEstimateQuoteTransaction::ACCOUNTING_INVOICE])->toArray();
    $estimateData = StoreEstimateQuoteTransactionAction::run($estimateInput);
    $estimateInput['additional_charges'][0]['ac_total'] = -200;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-estimate-quote/'.$estimateData['transaction_id'], $estimateInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Estimate Quote Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_estimate_quote_transactions', [
        'id' => $estimateData['transaction_id'],
        'company_id' => 1,
        'document_number' => $estimateInput['document_number'],
        'invoice_type' => IncomeEstimateQuoteTransaction::ACCOUNTING_INVOICE,
        'gross_value' => 600.00,
    ]);
    $estimateTransaction = IncomeEstimateQuoteTransaction::with('additionalCharges')->whereId($estimateData['transaction_id'])->first();
    $this->assertEquals(10, intval($estimateTransaction->additionalCharges->first()->total));
});

test('validation with update negative and wrong cgst and sgst when sale intra state taxable classification for update estimate quote transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $estimateInput = IncomeEstimateQuoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'main_classification_nature_type' => 'Sale Intra State Taxable',
        'invoice_type' => IncomeEstimateQuoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $estimateData = StoreEstimateQuoteTransactionAction::run($estimateInput);
    $estimateInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    foreach ($estimateInput['ledgers'] as $key => $ledger) {
        $estimateInput['ledgers'][$key]['gst_id'] = $gst->id;
    }

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-estimate-quote/'.$estimateData['transaction_id'], $estimateInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Estimate Quote Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_estimate_quote_transactions', [
        'id' => $estimateData['transaction_id'],
        'company_id' => 1,
        'document_number' => $estimateInput['document_number'],
        'invoice_type' => IncomeEstimateQuoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $estimateTransaction = IncomeEstimateQuoteTransaction::whereId($estimateData['transaction_id'])->first();
    $this->assertEquals(67.04, $estimateTransaction->cgst);
    $this->assertEquals(67.04, $estimateTransaction->sgst);
});

test('validation with update negative and wrong igst when sale inter state taxable classification for update estimate quote transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $estimateInput = IncomeEstimateQuoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'main_classification_nature_type' => 'Sale Inter State Taxable',
        'invoice_type' => IncomeEstimateQuoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $estimateData = StoreEstimateQuoteTransactionAction::run($estimateInput);
    $estimateInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    foreach ($estimateInput['ledgers'] as $key => $ledger) {
        $estimateInput['ledgers'][$key]['gst_id'] = $gst->id;
    }

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-estimate-quote/'.$estimateData['transaction_id'], $estimateInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Estimate Quote Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_estimate_quote_transactions', [
        'id' => $estimateData['transaction_id'],
        'company_id' => 1,
        'document_number' => $estimateInput['document_number'],
        'invoice_type' => IncomeEstimateQuoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $estimateTransaction = IncomeEstimateQuoteTransaction::whereId($estimateData['transaction_id'])->first();
    $this->assertEquals(134.08, $estimateTransaction->igst);
});

test('validation with update negative and wrong taxable value for update estimate quote transaction account invoice', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $estimateInput = IncomeEstimateQuoteTransactionFactory::new()->make([
        'invoice_type' => IncomeEstimateQuoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $estimateData = StoreEstimateQuoteTransactionAction::run($estimateInput);
    $estimateInput['taxable_value'] = -100;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-estimate-quote/'.$estimateData['transaction_id'], $estimateInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Estimate Quote Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_estimate_quote_transactions', [
        'id' => $estimateData['transaction_id'],
        'company_id' => 1,
        'document_number' => $estimateInput['document_number'],
        'invoice_type' => IncomeEstimateQuoteTransaction::ACCOUNTING_INVOICE,
        'taxable_value' => 610.00,
    ]);
});

test('validation with update negative and wrong addless total for update estimate quote transaction account invoice', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $estimateInput = IncomeEstimateQuoteTransactionFactory::new()->make([
        'invoice_type' => IncomeEstimateQuoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $estimateData = StoreEstimateQuoteTransactionAction::run($estimateInput);
    $estimateInput['add_less'][0]['al_total'] = -100;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-estimate-quote/'.$estimateData['transaction_id'], $estimateInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Estimate Quote Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_estimate_quote_transactions', [
        'id' => $estimateData['transaction_id'],
        'company_id' => 1,
        'document_number' => $estimateInput['document_number'],
        'invoice_type' => IncomeEstimateQuoteTransaction::ACCOUNTING_INVOICE,
        'gross_value' => 600.00,
    ]);
    $estimateTransaction = IncomeEstimateQuoteTransaction::with('addLess')->whereId($estimateData['transaction_id'])->first();
    $this->assertEquals(10, intval($estimateTransaction->addLess->first()->total));
});

test('validation with update negative and wrong grand total for update estimate quote transaction account invoice', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $estimateInput = IncomeEstimateQuoteTransactionFactory::new()->make([
        'invoice_type' => IncomeEstimateQuoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $estimateData = StoreEstimateQuoteTransactionAction::run($estimateInput);
    $estimateInput['grand_total'] = -100;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-estimate-quote/'.$estimateData['transaction_id'], $estimateInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Estimate Quote Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_estimate_quote_transactions', [
        'id' => $estimateData['transaction_id'],
        'company_id' => 1,
        'document_number' => $estimateInput['document_number'],
        'invoice_type' => IncomeEstimateQuoteTransaction::ACCOUNTING_INVOICE,
        'grand_total' => 620.00,
    ]);
});

test('validation with update negative and wrong round off amount and method for update estimate quote transaction', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);
    $estimateInput = IncomeEstimateQuoteTransactionFactory::new()->make()->toArray();
    $estimateInput['additional_charges'][0]['ac_value'] = 10.54;

    $roundOffMethods = [
        ['method' => 1, 'correct_amount' => 0],
        ['method' => 2, 'correct_amount' => -0.54],
        ['method' => 3, 'correct_amount' => 0.46],
        ['method' => 4, 'correct_amount' => 0.46],
    ];
    foreach ($roundOffMethods as $roundOffMethod) {
        $estimateInput['document_number'] = 'INV-'.$roundOffMethod['method'];
        $estimateData = StoreEstimateQuoteTransactionAction::run($estimateInput);
        $estimateInput['round_off_method'] = 0;
        IncomeEstimateQuoteTransactionMaster::whereCompanyId(1)->update(['round_off_method' => $roundOffMethod['method']]);
        $estimateInput['rounding_amount'] = -10.50;

        //act
        $response = postAPI($user->token, $user->company->id, 'api/v2/income-estimate-quote/'.$estimateData['transaction_id'], $estimateInput);

        //assert
        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Estimate Quote Transaction Updated Successfully',
            ]);
        $this->assertDatabaseHas('income_estimate_quote_transactions', [
            'id' => $estimateData['transaction_id'],
            'company_id' => 1,
            'document_number' => $estimateInput['document_number'],
            'round_off_method' => $roundOffMethod,
            'round_off_amount' => $roundOffMethod['correct_amount'],
        ]);
    }
});
