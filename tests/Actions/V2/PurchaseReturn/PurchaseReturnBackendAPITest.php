<?php

use App\Actions\v1\Transactions\PurchaseReturn\StorePurchaseReturnTransactionAction;
use App\Models\GstTax;
use App\Models\Master\ExpenseReturnTransactionMaster;
use App\Models\Master\ItemMasterGoods;
use App\Models\PurchaseReturnTransaction;
use Carbon\Carbon;
use Database\Factories\Api\PurchaseReturnTransactionFactory;

/*
|----------------------------------------------------------------------------------------------------------------------------------------------------
| Purchase Return Transaction Item Invoice Backend Validation Test Case
|----------------------------------------------------------------------------------------------------------------------------------------------------
*/

test('validate purchase return transaction item invoice will return gst na incorrect', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false, 'app.is_validation_testing' => false]);

    $purchaseReturnInput = PurchaseReturnTransactionFactory::new()->make()->toArray();
    $purchaseReturnInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['supplier_purchase_return_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['original_inv_date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase-return', $purchaseReturnInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Is GST NA is not correct.',
        ]);
});

test('validate purchase return transaction item invoice will return cgst, sgst, and igst calculated is not correct', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false, 'app.is_validation_testing' => false]);

    $purchaseReturnInput = PurchaseReturnTransactionFactory::new()->make(['is_gst_na' => true, 'is_cgst_sgst_igst_calculated' => true])->toArray();
    $purchaseReturnInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['supplier_purchase_return_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['original_inv_date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase-return', $purchaseReturnInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Is CGST, SGST, and IGST Calculated is not correct.',
        ]);
});

test('validate purchase return transaction item invoice will return subtotal is not correct when pass wrong total', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $purchaseReturnInput = PurchaseReturnTransactionFactory::new()->make([
        'is_gst_na' => true,
    ])->toArray();
    $purchaseReturnInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['supplier_purchase_return_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['original_inv_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['items'][0]['total'] = 10;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase-return', $purchaseReturnInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Subtotal is not correct.',
        ]);
});

test('validate purchase return transaction item invoice will return subtotal is not correct when pass wrong total with gst na, exempt, zero in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $purchaseReturnInput = PurchaseReturnTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $purchaseReturnInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['supplier_purchase_return_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['original_inv_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['items'][0]['total'] = 10;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase-return', $purchaseReturnInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Subtotal is not correct.',
        ]);
});

test('validate purchase return transaction item invoice will return subtotal is not correct when pass wrong total with gst percentage', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $gst = GstTax::whereTaxRate(28.00)->first();
    $purchaseReturnInput = PurchaseReturnTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $purchaseReturnInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['supplier_purchase_return_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['original_inv_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['items'][0]['gst_id'] = $gst->id;
    $purchaseReturnInput['items'][0]['gst_tax_percentage'] = $gst->tax_rate;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase-return', $purchaseReturnInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Subtotal is not correct.',
        ]);
});

test('validate purchase return transaction item invoice will return gross value is not correct', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $purchaseReturnInput = PurchaseReturnTransactionFactory::new()->make([
        'is_gst_na' => true,
        'gross_value' => 10,
    ])->toArray();
    $purchaseReturnInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['supplier_purchase_return_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['original_inv_date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase-return', $purchaseReturnInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Gross value is not correct.',
        ]);
});

test('validate purchase return transaction item invoice will return addition charge total is not correct', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $purchaseReturnInput = PurchaseReturnTransactionFactory::new()->make([
        'is_gst_na' => true,
    ])->toArray();
    $purchaseReturnInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['supplier_purchase_return_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['original_inv_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['additional_charges'][0]['ac_value'] = 20;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase-return', $purchaseReturnInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Additional charge total is not correct.',
        ]);
});

test('validate purchase return transaction item invoice will return Taxable value is not correct when pass additional gst percentage in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $gst = GstTax::whereTaxRate(28.00)->first();
    $purchaseReturnInput = PurchaseReturnTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $purchaseReturnInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['supplier_purchase_return_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['original_inv_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    foreach ($purchaseReturnInput['items'] as $key => $item) {
        $purchaseReturnInput['items'][$key]['gst_id'] = 2;
    }

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase-return', $purchaseReturnInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Taxable value is not correct.',
        ]);
});

test('validate purchase return transaction item invoice will return taxable value is not correct', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $purchaseReturnInput = PurchaseReturnTransactionFactory::new()->make([
        'is_gst_na' => true,
        'taxable_value' => 10,
    ])->toArray();
    $purchaseReturnInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['supplier_purchase_return_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['original_inv_date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase-return', $purchaseReturnInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Taxable value is not correct.',
        ]);
});

test('validate purchase return transaction item invoice will return cgst is not correct in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseReturnInput = PurchaseReturnTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'gross_value' => 468.78,
        'taxable_value' => 478.78,
        'main_classification_nature_type' => 'Intrastate Purchase Taxable',
    ])->toArray();
    $purchaseReturnInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['supplier_purchase_return_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['original_inv_date'] = Carbon::now()->format('d-m-Y');
    foreach ($purchaseReturnInput['items'] as $key => $item) {
        $purchaseReturnInput['items'][$key]['gst_id'] = $gst->id;
        $purchaseReturnInput['items'][$key]['total'] = 78.13;
    }

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase-return', $purchaseReturnInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'CGST is not correct.',
        ]);
});

test('validate purchase return transaction item invoice will return sgst is not correct in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseReturnInput = PurchaseReturnTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'gross_value' => 468.78,
        'taxable_value' => 478.78,
        'main_classification_nature_type' => 'Intrastate Purchase Taxable',
        'cgst' => 65.64,
    ])->toArray();
    $purchaseReturnInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['supplier_purchase_return_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['original_inv_date'] = Carbon::now()->format('d-m-Y');
    foreach ($purchaseReturnInput['items'] as $key => $item) {
        $purchaseReturnInput['items'][$key]['gst_id'] = $gst->id;
        $purchaseReturnInput['items'][$key]['total'] = 78.13;
    }

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase-return', $purchaseReturnInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'SGST is not correct.',
        ]);
});

test('validate purchase return transaction item invoice will return igst is not correct in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseReturnInput = PurchaseReturnTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'main_classification_nature_type' => 'Interstate Purchase Taxable',
        'gross_value' => 468.78,
        'taxable_value' => 478.78,
    ])->toArray();
    $purchaseReturnInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['supplier_purchase_return_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['original_inv_date'] = Carbon::now()->format('d-m-Y');
    foreach ($purchaseReturnInput['items'] as $key => $item) {
        $purchaseReturnInput['items'][$key]['gst_id'] = $gst->id;
        $purchaseReturnInput['items'][$key]['total'] = 78.13;
    }

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase-return', $purchaseReturnInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'IGST is not correct.',
        ]);
});

test('validate purchase return transaction item invoice will return classification nature type is empty', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(0.00)->first();

    $purchaseReturnInput = PurchaseReturnTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'main_classification_nature_type' => null,
        'taxable_value' => 610,
    ])->toArray();
    $purchaseReturnInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['supplier_purchase_return_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['original_inv_date'] = Carbon::now()->format('d-m-Y');
    foreach ($purchaseReturnInput['items'] as $key => $item) {
        $purchaseReturnInput['items'][$key]['gst_id'] = $gst->id;
    }

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase-return', $purchaseReturnInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Classification Nature Type is empty.',
        ]);
});

test('validate purchase return transaction item invoice will return add less total is not correct', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $purchaseReturnInput = PurchaseReturnTransactionFactory::new()->make([
        'is_gst_na' => true,
        'taxable_value' => 610,
    ])->toArray();
    $purchaseReturnInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['supplier_purchase_return_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['original_inv_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['add_less'][0]['al_total'] = 100;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase-return', $purchaseReturnInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Add Less total is not correct.',
        ]);
});

test('validate purchase return transaction item invoice will return round off amount is not correct', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $purchaseReturnInput = PurchaseReturnTransactionFactory::new()->make([
        'is_gst_na' => true,
        'is_round_off_not_changed' => 1,
        'taxable_value' => 610,
        'rounding_amount' => 10.50,
    ])->toArray();
    $purchaseReturnInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['supplier_purchase_return_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['original_inv_date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase-return', $purchaseReturnInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Round off amount is not correct.',
        ]);
});

test('validate purchase return transaction item invoice will return grand total is not correct', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $purchaseReturnInput = PurchaseReturnTransactionFactory::new()->make([
        'is_gst_na' => true,
        'taxable_value' => 610,
        'grand_total' => 100,
    ])->toArray();
    $purchaseReturnInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['supplier_purchase_return_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['original_inv_date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase-return', $purchaseReturnInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Grand total is not correct.',
        ]);
});

/*
|----------------------------------------------------------------------------------------------------------------------------------------------------
| Purchase Return Transaction Account Invoice Backend Validation Test Case
|----------------------------------------------------------------------------------------------------------------------------------------------------
*/

test('validate purchase return transaction account invoice will return gst na incorrect', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false, 'app.is_validation_testing' => false]);

    $purchaseReturnInput = PurchaseReturnTransactionFactory::new()->make(['pr_item_type' => PurchaseReturnTransaction::ACCOUNTING_INVOICE])->toArray();
    $purchaseReturnInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['supplier_purchase_return_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['original_inv_date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase-return', $purchaseReturnInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Is GST NA is not correct.',
        ]);
});

test('validate purchase return transaction account invoice will return cgst, sgst, and igst calculated is not correct', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false, 'app.is_validation_testing' => false]);

    $purchaseReturnInput = PurchaseReturnTransactionFactory::new()->make([
        'pr_item_type' => PurchaseReturnTransaction::ACCOUNTING_INVOICE,
        'is_gst_na' => true,
        'is_cgst_sgst_igst_calculated' => true,
    ])->toArray();
    $purchaseReturnInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['supplier_purchase_return_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['original_inv_date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase-return', $purchaseReturnInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Is CGST, SGST, and IGST Calculated is not correct.',
        ]);
});

test('validate purchase return transaction account invoice will return subtotal is not correct when pass wrong total', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $purchaseReturnInput = PurchaseReturnTransactionFactory::new()->make([
        'pr_item_type' => PurchaseReturnTransaction::ACCOUNTING_INVOICE,
        'is_gst_na' => true,
    ])->toArray();
    $purchaseReturnInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['supplier_purchase_return_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['original_inv_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['ledgers'][0]['total'] = 10;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase-return', $purchaseReturnInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Subtotal is not correct.',
        ]);
});

test('validate purchase return transaction account invoice will return subtotal is not correct when pass wrong total with gst na, exempt, zero in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $purchaseReturnInput = PurchaseReturnTransactionFactory::new()->make([
        'pr_item_type' => PurchaseReturnTransaction::ACCOUNTING_INVOICE,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $purchaseReturnInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['supplier_purchase_return_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['original_inv_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['ledgers'][0]['total'] = 10;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase-return', $purchaseReturnInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Subtotal is not correct.',
        ]);
});

test('validate purchase return transaction account invoice will return subtotal is not correct when pass wrong total with gst percentage', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $gst = GstTax::whereTaxRate(28.00)->first();
    $purchaseReturnInput = PurchaseReturnTransactionFactory::new()->make([
        'pr_item_type' => PurchaseReturnTransaction::ACCOUNTING_INVOICE,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $purchaseReturnInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['supplier_purchase_return_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['original_inv_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['ledgers'][0]['gst_id'] = $gst->id;
    $purchaseReturnInput['ledgers'][0]['with_tax'] = true;
    $purchaseReturnInput['ledgers'][0]['gst_tax_percentage'] = $gst->tax_rate;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase-return', $purchaseReturnInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Subtotal is not correct.',
        ]);
});

test('validate purchase return transaction account invoice will return gross value is not correct', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $purchaseReturnInput = PurchaseReturnTransactionFactory::new()->make([
        'pr_item_type' => PurchaseReturnTransaction::ACCOUNTING_INVOICE,
        'is_gst_na' => true,
        'gross_value' => 10,
    ])->toArray();
    $purchaseReturnInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['supplier_purchase_return_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['original_inv_date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase-return', $purchaseReturnInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Gross value is not correct.',
        ]);
});

test('validate purchase return transaction account invoice will return addition charge total is not correct', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $purchaseReturnInput = PurchaseReturnTransactionFactory::new()->make([
        'pr_item_type' => PurchaseReturnTransaction::ACCOUNTING_INVOICE,
        'is_gst_na' => true,
    ])->toArray();
    $purchaseReturnInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['supplier_purchase_return_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['original_inv_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['additional_charges'][0]['ac_value'] = 20;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase-return', $purchaseReturnInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Additional charge total is not correct.',
        ]);
});

test('validate purchase return transaction account invoice will return Taxable value is not correct when pass additional gst percentage in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $gst = GstTax::whereTaxRate(28.00)->first();
    $purchaseReturnInput = PurchaseReturnTransactionFactory::new()->make([
        'pr_item_type' => PurchaseReturnTransaction::ACCOUNTING_INVOICE,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $purchaseReturnInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['supplier_purchase_return_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['original_inv_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    foreach ($purchaseReturnInput['ledgers'] as $key => $item) {
        $purchaseReturnInput['ledgers'][$key]['gst_id'] = 2;
    }

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase-return', $purchaseReturnInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Taxable value is not correct.',
        ]);
});

test('validate purchase return transaction account invoice will return taxable value is not correct', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $purchaseReturnInput = PurchaseReturnTransactionFactory::new()->make([
        'pr_item_type' => PurchaseReturnTransaction::ACCOUNTING_INVOICE,
        'is_gst_na' => true,
        'taxable_value' => 10,
    ])->toArray();
    $purchaseReturnInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['supplier_purchase_return_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['original_inv_date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase-return', $purchaseReturnInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Taxable value is not correct.',
        ]);
});

test('validate purchase return transaction account invoice will return cgst is not correct in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseReturnInput = PurchaseReturnTransactionFactory::new()->make([
        'pr_item_type' => PurchaseReturnTransaction::ACCOUNTING_INVOICE,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'gross_value' => 468.78,
        'taxable_value' => 478.78,
        'main_classification_nature_type' => 'Intrastate Purchase Taxable',
    ])->toArray();
    $purchaseReturnInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['supplier_purchase_return_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['original_inv_date'] = Carbon::now()->format('d-m-Y');
    foreach ($purchaseReturnInput['ledgers'] as $key => $ledger) {
        $purchaseReturnInput['ledgers'][$key]['gst_id'] = $gst->id;
        $purchaseReturnInput['ledgers'][$key]['total'] = 78.13;
    }

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase-return', $purchaseReturnInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'CGST is not correct.',
        ]);
});

test('validate purchase return transaction account invoice will return sgst is not correct in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseReturnInput = PurchaseReturnTransactionFactory::new()->make([
        'pr_item_type' => PurchaseReturnTransaction::ACCOUNTING_INVOICE,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'gross_value' => 468.78,
        'taxable_value' => 478.78,
        'main_classification_nature_type' => 'Intrastate Purchase Taxable',
        'cgst' => 65.64,
    ])->toArray();
    $purchaseReturnInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['supplier_purchase_return_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['original_inv_date'] = Carbon::now()->format('d-m-Y');
    foreach ($purchaseReturnInput['ledgers'] as $key => $ledger) {
        $purchaseReturnInput['ledgers'][$key]['gst_id'] = $gst->id;
        $purchaseReturnInput['ledgers'][$key]['total'] = 78.13;
    }

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase-return', $purchaseReturnInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'SGST is not correct.',
        ]);
});

test('validate purchase return transaction account invoice will return igst is not correct in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseReturnInput = PurchaseReturnTransactionFactory::new()->make([
        'pr_item_type' => PurchaseReturnTransaction::ACCOUNTING_INVOICE,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'gross_value' => 468.78,
        'taxable_value' => 478.78,
        'main_classification_nature_type' => 'Interstate Purchase Taxable',
    ])->toArray();
    $purchaseReturnInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['supplier_purchase_return_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['original_inv_date'] = Carbon::now()->format('d-m-Y');
    foreach ($purchaseReturnInput['ledgers'] as $key => $item) {
        $purchaseReturnInput['ledgers'][$key]['gst_id'] = $gst->id;
        $purchaseReturnInput['ledgers'][$key]['total'] = 78.13;
    }

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase-return', $purchaseReturnInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'IGST is not correct.',
        ]);
});

test('validate purchase return transaction account invoice will return classification nature type is empty', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(0.00)->first();

    $purchaseReturnInput = PurchaseReturnTransactionFactory::new()->make([
        'pr_item_type' => PurchaseReturnTransaction::ACCOUNTING_INVOICE,
        'is_gst_enabled' => true,
        'main_classification_nature_type' => null,
        'taxable_value' => 610,
    ])->toArray();
    $purchaseReturnInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['supplier_purchase_return_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['original_inv_date'] = Carbon::now()->format('d-m-Y');
    foreach ($purchaseReturnInput['ledgers'] as $key => $ledger) {
        $purchaseReturnInput['ledgers'][$key]['gst_id'] = $gst->id;
    }

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase-return', $purchaseReturnInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Classification Nature Type is empty.',
        ]);
});

test('validate purchase return transaction account invoice will return add less total is not correct', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $purchaseReturnInput = PurchaseReturnTransactionFactory::new()->make([
        'pr_item_type' => PurchaseReturnTransaction::ACCOUNTING_INVOICE,
        'is_gst_na' => true,
        'taxable_value' => 610,
    ])->toArray();
    $purchaseReturnInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['supplier_purchase_return_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['original_inv_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['add_less'][0]['al_total'] = 100;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase-return', $purchaseReturnInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Add Less total is not correct.',
        ]);
});

test('validate purchase return transaction account invoice will return round off amount is not correct', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $purchaseReturnInput = PurchaseReturnTransactionFactory::new()->make([
        'pr_item_type' => PurchaseReturnTransaction::ACCOUNTING_INVOICE,
        'is_round_off_not_changed' => 1,
        'is_gst_na' => true,
        'taxable_value' => 610,
        'rounding_amount' => 10.50,
    ])->toArray();
    $purchaseReturnInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['supplier_purchase_return_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['original_inv_date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase-return', $purchaseReturnInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Round off amount is not correct.',
        ]);
});

test('validate purchase return transaction account invoice will return grand total is not correct', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $purchaseReturnInput = PurchaseReturnTransactionFactory::new()->make([
        'pr_item_type' => PurchaseReturnTransaction::ACCOUNTING_INVOICE,
        'is_gst_na' => true,
        'taxable_value' => 610,
        'grand_total' => 100,
    ])->toArray();
    $purchaseReturnInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['supplier_purchase_return_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['original_inv_date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase-return', $purchaseReturnInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Grand total is not correct.',
        ]);
});

/*
|----------------------------------------------------------------------------------------------------------------------------------------------------
| Store Purchase Return Transaction Item Invoice Backend Validation with update Test Case
|----------------------------------------------------------------------------------------------------------------------------------------------------
*/

test('validation with update decimal rpu amount when decimal place is 1 for purchase return transaction item invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 1]);
    $purchaseReturnInput = PurchaseReturnTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $purchaseReturnInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['supplier_purchase_return_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['original_inv_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['items'][0]['rpu'] = 100.********;
    $purchaseReturnInput['items'][0]['rpu_without_gst'] = 100.********;
    $purchaseReturnInput['items'][0]['rpu_with_gst'] = 100.********;
    $purchaseReturnInput['items'][0]['gst_id'] = $gst->id;
    $purchaseReturnInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase-return', $purchaseReturnInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Return Transaction created Successfully',
        ]);
    $this->assertDatabaseHas('purchase_return_transactions', [
        'id' => $response->json('data.purchase_return_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_return_transaction.voucher_number'),
        'pr_item_type' => PurchaseReturnTransaction::ITEM_INVOICE,
    ]);
    $purchaseReturnTransaction = PurchaseReturnTransaction::with('purchaseReturnItems')->whereId($response->json('data.purchase_return_transaction.id'))->first();
    $this->assertEquals(78.2, $purchaseReturnTransaction->purchaseReturnItems->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 2 for purchase return transaction item invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 2]);
    $purchaseReturnInput = PurchaseReturnTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $purchaseReturnInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['supplier_purchase_return_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['original_inv_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['items'][0]['rpu'] = 100.********;
    $purchaseReturnInput['items'][0]['rpu_without_gst'] = 100.********;
    $purchaseReturnInput['items'][0]['rpu_with_gst'] = 100.********;
    $purchaseReturnInput['items'][0]['gst_id'] = $gst->id;
    $purchaseReturnInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase-return', $purchaseReturnInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Return Transaction created Successfully',
        ]);
    $this->assertDatabaseHas('purchase_return_transactions', [
        'id' => $response->json('data.purchase_return_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_return_transaction.voucher_number'),
        'pr_item_type' => PurchaseReturnTransaction::ITEM_INVOICE,
    ]);
    $purchaseReturnTransaction = PurchaseReturnTransaction::with('purchaseReturnItems')->whereId($response->json('data.purchase_return_transaction.id'))->first();
    $this->assertEquals(78.22, $purchaseReturnTransaction->purchaseReturnItems->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 3 for purchase return transaction item invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 3]);
    $purchaseReturnInput = PurchaseReturnTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $purchaseReturnInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['supplier_purchase_return_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['original_inv_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['items'][0]['rpu'] = 100.********;
    $purchaseReturnInput['items'][0]['rpu_without_gst'] = 100.********;
    $purchaseReturnInput['items'][0]['rpu_with_gst'] = 100.********;
    $purchaseReturnInput['items'][0]['gst_id'] = $gst->id;
    $purchaseReturnInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase-return', $purchaseReturnInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Return Transaction created Successfully',
        ]);
    $this->assertDatabaseHas('purchase_return_transactions', [
        'id' => $response->json('data.purchase_return_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_return_transaction.voucher_number'),
        'pr_item_type' => PurchaseReturnTransaction::ITEM_INVOICE,
    ]);
    $purchaseReturnTransaction = PurchaseReturnTransaction::with('purchaseReturnItems')->whereId($response->json('data.purchase_return_transaction.id'))->first();
    $this->assertEquals(78.220, $purchaseReturnTransaction->purchaseReturnItems->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 4 for purchase return transaction item invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 4]);
    $purchaseReturnInput = PurchaseReturnTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $purchaseReturnInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['supplier_purchase_return_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['original_inv_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['items'][0]['rpu'] = 100.********;
    $purchaseReturnInput['items'][0]['rpu_without_gst'] = 100.********;
    $purchaseReturnInput['items'][0]['rpu_with_gst'] = 100.********;
    $purchaseReturnInput['items'][0]['gst_id'] = $gst->id;
    $purchaseReturnInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase-return', $purchaseReturnInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Return Transaction created Successfully',
        ]);
    $this->assertDatabaseHas('purchase_return_transactions', [
        'id' => $response->json('data.purchase_return_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_return_transaction.voucher_number'),
        'pr_item_type' => PurchaseReturnTransaction::ITEM_INVOICE,
    ]);
    $purchaseReturnTransaction = PurchaseReturnTransaction::with('purchaseReturnItems')->whereId($response->json('data.purchase_return_transaction.id'))->first();
    $this->assertEquals(78.2197, $purchaseReturnTransaction->purchaseReturnItems->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 5 for purchase return transaction item invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 5]);
    $purchaseReturnInput = PurchaseReturnTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $purchaseReturnInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['supplier_purchase_return_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['original_inv_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['items'][0]['rpu'] = 100.********;
    $purchaseReturnInput['items'][0]['rpu_without_gst'] = 100.********;
    $purchaseReturnInput['items'][0]['rpu_with_gst'] = 100.********;
    $purchaseReturnInput['items'][0]['gst_id'] = $gst->id;
    $purchaseReturnInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase-return', $purchaseReturnInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Return Transaction created Successfully',
        ]);
    $this->assertDatabaseHas('purchase_return_transactions', [
        'id' => $response->json('data.purchase_return_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_return_transaction.voucher_number'),
        'pr_item_type' => PurchaseReturnTransaction::ITEM_INVOICE,
    ]);
    $purchaseReturnTransaction = PurchaseReturnTransaction::with('purchaseReturnItems')->whereId($response->json('data.purchase_return_transaction.id'))->first();
    $this->assertEquals(78.21970, $purchaseReturnTransaction->purchaseReturnItems->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 1 for purchase return transaction item invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 1]);
    $purchaseReturnInput = PurchaseReturnTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $purchaseReturnInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['supplier_purchase_return_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['original_inv_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['items'][0]['rpu'] = 100.********;
    $purchaseReturnInput['items'][0]['rpu_without_gst'] = 100.********;
    $purchaseReturnInput['items'][0]['rpu_with_gst'] = 100.********;
    $purchaseReturnInput['items'][0]['gst_id'] = $gst->id;
    $purchaseReturnInput['items'][0]['gst_tax_percentage'] = 2;
    $purchaseReturnInput['items'][0]['with_tax'] = false;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase-return', $purchaseReturnInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Return Transaction created Successfully',
        ]);
    $this->assertDatabaseHas('purchase_return_transactions', [
        'id' => $response->json('data.purchase_return_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_return_transaction.voucher_number'),
        'pr_item_type' => PurchaseReturnTransaction::ITEM_INVOICE,
    ]);
    $purchaseReturnTransaction = PurchaseReturnTransaction::with('purchaseReturnItems')->whereId($response->json('data.purchase_return_transaction.id'))->first();
    $this->assertEquals(128.2, $purchaseReturnTransaction->purchaseReturnItems->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 2 for purchase return transaction item invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 2]);
    $purchaseReturnInput = PurchaseReturnTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $purchaseReturnInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['supplier_purchase_return_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['original_inv_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['items'][0]['rpu'] = 100.********;
    $purchaseReturnInput['items'][0]['rpu_without_gst'] = 100.********;
    $purchaseReturnInput['items'][0]['rpu_with_gst'] = 100.********;
    $purchaseReturnInput['items'][0]['gst_id'] = $gst->id;
    $purchaseReturnInput['items'][0]['gst_tax_percentage'] = 2;
    $purchaseReturnInput['items'][0]['with_tax'] = false;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase-return', $purchaseReturnInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Return Transaction created Successfully',
        ]);
    $this->assertDatabaseHas('purchase_return_transactions', [
        'id' => $response->json('data.purchase_return_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_return_transaction.voucher_number'),
        'pr_item_type' => PurchaseReturnTransaction::ITEM_INVOICE,
    ]);
    $purchaseReturnTransaction = PurchaseReturnTransaction::with('purchaseReturnItems')->whereId($response->json('data.purchase_return_transaction.id'))->first();
    $this->assertEquals(128.16, $purchaseReturnTransaction->purchaseReturnItems->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 3 for purchase return transaction item invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 3]);
    $purchaseReturnInput = PurchaseReturnTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $purchaseReturnInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['supplier_purchase_return_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['original_inv_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['items'][0]['rpu'] = 100.********;
    $purchaseReturnInput['items'][0]['rpu_without_gst'] = 100.********;
    $purchaseReturnInput['items'][0]['rpu_with_gst'] = 100.********;
    $purchaseReturnInput['items'][0]['gst_id'] = $gst->id;
    $purchaseReturnInput['items'][0]['gst_tax_percentage'] = 2;
    $purchaseReturnInput['items'][0]['with_tax'] = false;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase-return', $purchaseReturnInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Return Transaction created Successfully',
        ]);
    $this->assertDatabaseHas('purchase_return_transactions', [
        'id' => $response->json('data.purchase_return_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_return_transaction.voucher_number'),
        'pr_item_type' => PurchaseReturnTransaction::ITEM_INVOICE,
    ]);
    $purchaseReturnTransaction = PurchaseReturnTransaction::with('purchaseReturnItems')->whereId($response->json('data.purchase_return_transaction.id'))->first();
    $this->assertEquals(128.155, $purchaseReturnTransaction->purchaseReturnItems->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 4 for purchase return transaction item invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 4]);
    $purchaseReturnInput = PurchaseReturnTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $purchaseReturnInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['supplier_purchase_return_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['original_inv_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['items'][0]['rpu'] = 100.********;
    $purchaseReturnInput['items'][0]['rpu_without_gst'] = 100.********;
    $purchaseReturnInput['items'][0]['rpu_with_gst'] = 100.********;
    $purchaseReturnInput['items'][0]['gst_id'] = $gst->id;
    $purchaseReturnInput['items'][0]['gst_tax_percentage'] = 2;
    $purchaseReturnInput['items'][0]['with_tax'] = false;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase-return', $purchaseReturnInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Return Transaction created Successfully',
        ]);
    $this->assertDatabaseHas('purchase_return_transactions', [
        'id' => $response->json('data.purchase_return_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_return_transaction.voucher_number'),
        'pr_item_type' => PurchaseReturnTransaction::ITEM_INVOICE,
    ]);
    $purchaseReturnTransaction = PurchaseReturnTransaction::with('purchaseReturnItems')->whereId($response->json('data.purchase_return_transaction.id'))->first();
    $this->assertEquals(128.1552, $purchaseReturnTransaction->purchaseReturnItems->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 5 for purchase return transaction item invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 5]);
    $purchaseReturnInput = PurchaseReturnTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $purchaseReturnInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['supplier_purchase_return_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['original_inv_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['items'][0]['rpu'] = 100.********;
    $purchaseReturnInput['items'][0]['rpu_without_gst'] = 100.********;
    $purchaseReturnInput['items'][0]['rpu_with_gst'] = 100.********;
    $purchaseReturnInput['items'][0]['gst_id'] = $gst->id;
    $purchaseReturnInput['items'][0]['gst_tax_percentage'] = 2;
    $purchaseReturnInput['items'][0]['with_tax'] = false;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase-return', $purchaseReturnInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Return Transaction created Successfully',
        ]);
    $this->assertDatabaseHas('purchase_return_transactions', [
        'id' => $response->json('data.purchase_return_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_return_transaction.voucher_number'),
        'pr_item_type' => PurchaseReturnTransaction::ITEM_INVOICE,
    ]);
    $purchaseReturnTransaction = PurchaseReturnTransaction::with('purchaseReturnItems')->whereId($response->json('data.purchase_return_transaction.id'))->first();
    $this->assertEquals(128.15515, $purchaseReturnTransaction->purchaseReturnItems->first()->rpu_with_gst);
});

test('validation with update negative and wrong rpu without or with gst for purchase return transaction item invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseReturnInput = PurchaseReturnTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $purchaseReturnInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['supplier_purchase_return_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['original_inv_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['items'][0]['rpu_without_gst'] = -10000;
    $purchaseReturnInput['items'][0]['rpu_with_gst'] = -10000;
    $purchaseReturnInput['items'][0]['gst_id'] = $gst->id;
    $purchaseReturnInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase-return', $purchaseReturnInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Return Transaction created Successfully',
        ]);
    $this->assertDatabaseHas('purchase_return_transactions', [
        'id' => $response->json('data.purchase_return_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_return_transaction.voucher_number'),
        'pr_item_type' => PurchaseReturnTransaction::ITEM_INVOICE,
    ]);
    $purchaseReturnTransaction = PurchaseReturnTransaction::with('purchaseReturnItems')->whereId($response->json('data.purchase_return_transaction.id'))->first();
    $this->assertNotEquals(100, $purchaseReturnTransaction->purchaseReturnItems->first()->rpu_without_gst);
    $this->assertEquals(100.0, $purchaseReturnTransaction->purchaseReturnItems->first()->rpu_with_gst);
});

test('validation with update negative and wrong gst tax percentage for purchase return transaction item invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseReturnInput = PurchaseReturnTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $purchaseReturnInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['supplier_purchase_return_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['original_inv_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['items'][0]['total'] = -10000;
    $purchaseReturnInput['items'][0]['gst_id'] = $gst->id;
    $purchaseReturnInput['items'][0]['gst_tax_percentage'] = $gst->tax_rate;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase-return', $purchaseReturnInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Return Transaction created Successfully',
        ]);
    $this->assertDatabaseHas('purchase_return_transactions', [
        'id' => $response->json('data.purchase_return_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_return_transaction.voucher_number'),
        'pr_item_type' => PurchaseReturnTransaction::ITEM_INVOICE,
    ]);
    $purchaseReturnTransaction = PurchaseReturnTransaction::with('purchaseReturnItems')->whereId($response->json('data.purchase_return_transaction.id'))->first();
    $this->assertEquals(28, intval($purchaseReturnTransaction->purchaseReturnItems->first()->gst_tax_percentage));
});

test('validation with update negative and wrong total discount for purchase return transaction item invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(5.00)->first();

    $purchaseReturnInput = PurchaseReturnTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $purchaseReturnInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['supplier_purchase_return_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['original_inv_date'] = Carbon::now()->format('d-m-Y');
    foreach ($purchaseReturnInput['items'] as $key => $item) {
        $purchaseReturnInput['items'][$key]['gst_id'] = $gst->id;
        $purchaseReturnInput['items'][$key]['discount_type'] = 2;
        $purchaseReturnInput['items'][$key]['discount_value'] = 10;
        $purchaseReturnInput['items'][$key]['discount_type_2'] = 2;
        $purchaseReturnInput['items'][$key]['discount_value_2'] = 10;
        $purchaseReturnInput['items'][$key]['total_discount_amount'] = 10;
    }

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase-return', $purchaseReturnInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Return Transaction created Successfully',
        ]);
    $this->assertDatabaseHas('purchase_return_transactions', [
        'id' => $response->json('data.purchase_return_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_return_transaction.voucher_number'),
        'pr_item_type' => PurchaseReturnTransaction::ITEM_INVOICE,
    ]);
    $purchaseReturnTransaction = PurchaseReturnTransaction::with('purchaseReturnItems')->whereId($response->json('data.purchase_return_transaction.id'))->first();
    foreach ($purchaseReturnTransaction->purchaseReturnItems as $item) {
        $this->assertEquals(18.1, $item->total_discount_amount);
    }
});

test('validation with update negative and wrong sub total for purchase return transaction item invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseReturnInput = PurchaseReturnTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $purchaseReturnInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['supplier_purchase_return_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['original_inv_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['items'][0]['total'] = -10000;
    $purchaseReturnInput['items'][0]['gst_id'] = $gst->id;
    $purchaseReturnInput['items'][0]['gst_tax_percentage'] = $gst->tax_rate;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase-return', $purchaseReturnInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Return Transaction created Successfully',
        ]);
    $this->assertDatabaseHas('purchase_return_transactions', [
        'id' => $response->json('data.purchase_return_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_return_transaction.voucher_number'),
        'pr_item_type' => PurchaseReturnTransaction::ITEM_INVOICE,
    ]);
    $purchaseReturnTransaction = PurchaseReturnTransaction::with('purchaseReturnItems')->whereId($response->json('data.purchase_return_transaction.id'))->first();
    $this->assertEquals(78.13, $purchaseReturnTransaction->purchaseReturnItems->first()->total);
});

test('validation with update negative and wrong sub total for purchase return transaction item invoice', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $purchaseReturnInput = PurchaseReturnTransactionFactory::new()->make()->toArray();
    $purchaseReturnInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['supplier_purchase_return_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['original_inv_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['items'][0]['total'] = -10000;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase-return', $purchaseReturnInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Return Transaction created Successfully',
        ]);
    $this->assertDatabaseHas('purchase_return_transactions', [
        'id' => $response->json('data.purchase_return_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_return_transaction.voucher_number'),
        'pr_item_type' => PurchaseReturnTransaction::ITEM_INVOICE,
    ]);
    $purchaseReturnTransaction = PurchaseReturnTransaction::with('purchaseReturnItems')->whereId($response->json('data.purchase_return_transaction.id'))->first();
    $this->assertEquals(100, intval($purchaseReturnTransaction->purchaseReturnItems->first()->total));
});

test('validation with update negative and wrong total for purchase return transaction item invoice', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $purchaseReturnInput = PurchaseReturnTransactionFactory::new()->make(['total' => -620])->toArray();
    $purchaseReturnInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['supplier_purchase_return_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['original_inv_date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase-return', $purchaseReturnInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Return Transaction created Successfully',
        ]);
    $this->assertDatabaseHas('purchase_return_transactions', [
        'id' => $response->json('data.purchase_return_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_return_transaction.voucher_number'),
        'pr_item_type' => PurchaseReturnTransaction::ITEM_INVOICE,
        'total' => 620.00,
    ]);
});

test('validation with update negative and wrong gross value for purchase return transaction item invoice', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $purchaseReturnInput = PurchaseReturnTransactionFactory::new()->make(['gross_value' => -100])->toArray();
    $purchaseReturnInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['supplier_purchase_return_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['original_inv_date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase-return', $purchaseReturnInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Return Transaction created Successfully',
        ]);
    $this->assertDatabaseHas('purchase_return_transactions', [
        'id' => $response->json('data.purchase_return_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_return_transaction.voucher_number'),
        'pr_item_type' => PurchaseReturnTransaction::ITEM_INVOICE,
        'gross_value' => 600.00,
    ]);
});

test('validation with update negative and wrong additional charges gst tax percentage for purchase return transaction item invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseReturnInput = PurchaseReturnTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $purchaseReturnInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['supplier_purchase_return_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['original_inv_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    $purchaseReturnInput['additional_charges'][0]['ac_total'] = -2000;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase-return', $purchaseReturnInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Return Transaction created Successfully',
        ]);
    $this->assertDatabaseHas('purchase_return_transactions', [
        'id' => $response->json('data.purchase_return_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_return_transaction.voucher_number'),
        'pr_item_type' => PurchaseReturnTransaction::ITEM_INVOICE,
    ]);
    $purchaseReturnTransaction = PurchaseReturnTransaction::with('additionalCharges')->whereId($response->json('data.purchase_return_transaction.id'))->first();
    $this->assertNotEquals(10.00, $purchaseReturnTransaction->additionalCharges->first()->total);
});

test('validation with update negative and wrong additional charges total for purchase return transaction item invoice', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $purchaseReturnInput = PurchaseReturnTransactionFactory::new()->make()->toArray();
    $purchaseReturnInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['supplier_purchase_return_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['original_inv_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['additional_charges'][0]['ac_total'] = -200;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase-return', $purchaseReturnInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Return Transaction created Successfully',
        ]);
    $this->assertDatabaseHas('purchase_return_transactions', [
        'id' => $response->json('data.purchase_return_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_return_transaction.voucher_number'),
        'pr_item_type' => PurchaseReturnTransaction::ITEM_INVOICE,
        'gross_value' => 600.00,
    ]);
    $purchaseReturnTransaction = PurchaseReturnTransaction::with('additionalCharges')->whereId($response->json('data.purchase_return_transaction.id'))->first();
    $this->assertEquals(10, intval($purchaseReturnTransaction->additionalCharges->first()->total));
});

test('validation with update negative and wrong cgst and sgst when purchase intra state taxable classification for purchase return transaction item invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseReturnInput = PurchaseReturnTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'main_classification_nature_type' => 'Intrastate Purchase Taxable',
    ])->toArray();
    $purchaseReturnInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['supplier_purchase_return_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['original_inv_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    foreach ($purchaseReturnInput['items'] as $key => $item) {
        $purchaseReturnInput['items'][$key]['gst_id'] = $gst->id;
    }

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase-return', $purchaseReturnInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Return Transaction created Successfully',
        ]);
    $this->assertDatabaseHas('purchase_return_transactions', [
        'id' => $response->json('data.purchase_return_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_return_transaction.voucher_number'),
        'pr_item_type' => PurchaseReturnTransaction::ITEM_INVOICE,
    ]);
    $purchaseReturnTransaction = PurchaseReturnTransaction::whereId($response->json('data.purchase_return_transaction.id'))->first();
    $this->assertEquals(67.04, $purchaseReturnTransaction->cgst);
    $this->assertEquals(67.04, $purchaseReturnTransaction->sgst);
});

test('validation with update negative and wrong igst when purchase inter state taxable classification for purchase return transaction item invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseReturnInput = PurchaseReturnTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'main_classification_nature_type' => 'Interstate Purchase Taxable',
    ])->toArray();
    $purchaseReturnInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['supplier_purchase_return_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['original_inv_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    foreach ($purchaseReturnInput['items'] as $key => $item) {
        $purchaseReturnInput['items'][$key]['gst_id'] = $gst->id;
    }

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase-return', $purchaseReturnInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Return Transaction created Successfully',
        ]);
    $this->assertDatabaseHas('purchase_return_transactions', [
        'id' => $response->json('data.purchase_return_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_return_transaction.voucher_number'),
        'pr_item_type' => PurchaseReturnTransaction::ITEM_INVOICE,
    ]);
    $purchaseReturnTransaction = PurchaseReturnTransaction::whereId($response->json('data.purchase_return_transaction.id'))->first();
    $this->assertEquals(134.08, $purchaseReturnTransaction->igst);
});

test('validation with update negative and wrong taxable value for purchase return transaction item invoice', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $purchaseReturnInput = PurchaseReturnTransactionFactory::new()->make(['taxable_value' => -100])->toArray();
    $purchaseReturnInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['supplier_purchase_return_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['original_inv_date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase-return', $purchaseReturnInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Return Transaction created Successfully',
        ]);
    $this->assertDatabaseHas('purchase_return_transactions', [
        'id' => $response->json('data.purchase_return_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_return_transaction.voucher_number'),
        'pr_item_type' => PurchaseReturnTransaction::ITEM_INVOICE,
        'taxable_value' => 610.00,
    ]);
});

test('validation with update negative and wrong addless total for purchase return transaction item invoice', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $purchaseReturnInput = PurchaseReturnTransactionFactory::new()->make()->toArray();
    $purchaseReturnInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['supplier_purchase_return_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['original_inv_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['add_less'][0]['al_total'] = -100;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase-return', $purchaseReturnInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Return Transaction created Successfully',
        ]);
    $this->assertDatabaseHas('purchase_return_transactions', [
        'id' => $response->json('data.purchase_return_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_return_transaction.voucher_number'),
        'pr_item_type' => PurchaseReturnTransaction::ITEM_INVOICE,
        'gross_value' => 600.00,
    ]);
    $purchaseReturnTransaction = PurchaseReturnTransaction::with('additionalCharges')->whereId($response->json('data.purchase_return_transaction.id'))->first();
    $this->assertEquals(10, intval($purchaseReturnTransaction->addLess->first()->total));
});

test('validation with update negative and wrong grand total for purchase return transaction item invoice', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $purchaseReturnInput = PurchaseReturnTransactionFactory::new()->make(['grand_total' => -100])->toArray();
    $purchaseReturnInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['supplier_purchase_return_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['original_inv_date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase-return', $purchaseReturnInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Return Transaction created Successfully',
        ]);
    $this->assertDatabaseHas('purchase_return_transactions', [
        'id' => $response->json('data.purchase_return_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_return_transaction.voucher_number'),
        'pr_item_type' => PurchaseReturnTransaction::ITEM_INVOICE,
        'grand_total' => 620.00,
    ]);
});

/*
|----------------------------------------------------------------------------------------------------------------------------------------------------
| Store Purchase Return Transaction Account Invoice Backend Validation with update Test Case
|----------------------------------------------------------------------------------------------------------------------------------------------------
*/

test('validation with update decimal rpu amount when decimal place is 1 for purchase return transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseReturnInput = PurchaseReturnTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'pr_item_type' => PurchaseReturnTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $purchaseReturnInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['supplier_purchase_return_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['original_inv_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['ledgers'][0]['decimal'] = 1;
    $purchaseReturnInput['ledgers'][0]['rpu'] = 100.********;
    $purchaseReturnInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $purchaseReturnInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $purchaseReturnInput['ledgers'][0]['gst_id'] = $gst->id;
    $purchaseReturnInput['ledgers'][0]['gst_tax_percentage'] = 2;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase-return', $purchaseReturnInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Return Transaction created Successfully',
        ]);
    $this->assertDatabaseHas('purchase_return_transactions', [
        'id' => $response->json('data.purchase_return_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_return_transaction.voucher_number'),
        'pr_item_type' => PurchaseReturnTransaction::ACCOUNTING_INVOICE,
    ]);
    $purchaseReturnTransaction = PurchaseReturnTransaction::with('purchaseReturnLedgers')->whereId($response->json('data.purchase_return_transaction.id'))->first();
    $this->assertEquals(78.2, $purchaseReturnTransaction->purchaseReturnLedgers->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 2 for purchase return transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseReturnInput = PurchaseReturnTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'pr_item_type' => PurchaseReturnTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $purchaseReturnInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['supplier_purchase_return_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['original_inv_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['ledgers'][0]['decimal'] = 2;
    $purchaseReturnInput['ledgers'][0]['rpu'] = 100.********;
    $purchaseReturnInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $purchaseReturnInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $purchaseReturnInput['ledgers'][0]['gst_id'] = $gst->id;
    $purchaseReturnInput['ledgers'][0]['gst_tax_percentage'] = 2;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase-return', $purchaseReturnInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Return Transaction created Successfully',
        ]);
    $this->assertDatabaseHas('purchase_return_transactions', [
        'id' => $response->json('data.purchase_return_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_return_transaction.voucher_number'),
        'pr_item_type' => PurchaseReturnTransaction::ACCOUNTING_INVOICE,
    ]);
    $purchaseReturnTransaction = PurchaseReturnTransaction::with('purchaseReturnLedgers')->whereId($response->json('data.purchase_return_transaction.id'))->first();
    $this->assertEquals(78.22, $purchaseReturnTransaction->purchaseReturnLedgers->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 3 for purchase return transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseReturnInput = PurchaseReturnTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'pr_item_type' => PurchaseReturnTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $purchaseReturnInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['supplier_purchase_return_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['original_inv_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['ledgers'][0]['decimal'] = 3;
    $purchaseReturnInput['ledgers'][0]['rpu'] = 100.********;
    $purchaseReturnInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $purchaseReturnInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $purchaseReturnInput['ledgers'][0]['gst_id'] = $gst->id;
    $purchaseReturnInput['ledgers'][0]['gst_tax_percentage'] = 2;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase-return', $purchaseReturnInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Return Transaction created Successfully',
        ]);
    $this->assertDatabaseHas('purchase_return_transactions', [
        'id' => $response->json('data.purchase_return_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_return_transaction.voucher_number'),
        'pr_item_type' => PurchaseReturnTransaction::ACCOUNTING_INVOICE,
    ]);
    $purchaseReturnTransaction = PurchaseReturnTransaction::with('purchaseReturnLedgers')->whereId($response->json('data.purchase_return_transaction.id'))->first();
    $this->assertEquals(78.220, $purchaseReturnTransaction->purchaseReturnLedgers->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 4 for purchase return transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseReturnInput = PurchaseReturnTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'pr_item_type' => PurchaseReturnTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $purchaseReturnInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['supplier_purchase_return_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['original_inv_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['ledgers'][0]['decimal'] = 4;
    $purchaseReturnInput['ledgers'][0]['rpu'] = 100.********;
    $purchaseReturnInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $purchaseReturnInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $purchaseReturnInput['ledgers'][0]['gst_id'] = $gst->id;
    $purchaseReturnInput['ledgers'][0]['gst_tax_percentage'] = 2;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase-return', $purchaseReturnInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Return Transaction created Successfully',
        ]);
    $this->assertDatabaseHas('purchase_return_transactions', [
        'id' => $response->json('data.purchase_return_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_return_transaction.voucher_number'),
        'pr_item_type' => PurchaseReturnTransaction::ACCOUNTING_INVOICE,
    ]);
    $purchaseReturnTransaction = PurchaseReturnTransaction::with('purchaseReturnLedgers')->whereId($response->json('data.purchase_return_transaction.id'))->first();
    $this->assertEquals(78.2197, $purchaseReturnTransaction->purchaseReturnLedgers->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 5 for purchase return transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseReturnInput = PurchaseReturnTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'pr_item_type' => PurchaseReturnTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $purchaseReturnInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['supplier_purchase_return_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['original_inv_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['ledgers'][0]['decimal'] = 5;
    $purchaseReturnInput['ledgers'][0]['rpu'] = 100.********;
    $purchaseReturnInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $purchaseReturnInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $purchaseReturnInput['ledgers'][0]['gst_id'] = $gst->id;
    $purchaseReturnInput['ledgers'][0]['gst_tax_percentage'] = 2;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase-return', $purchaseReturnInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Return Transaction created Successfully',
        ]);
    $this->assertDatabaseHas('purchase_return_transactions', [
        'id' => $response->json('data.purchase_return_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_return_transaction.voucher_number'),
        'pr_item_type' => PurchaseReturnTransaction::ACCOUNTING_INVOICE,
    ]);
    $purchaseReturnTransaction = PurchaseReturnTransaction::with('purchaseReturnLedgers')->whereId($response->json('data.purchase_return_transaction.id'))->first();
    $this->assertEquals(78.21970, $purchaseReturnTransaction->purchaseReturnLedgers->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 1 for purchase return transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseReturnInput = PurchaseReturnTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'pr_item_type' => PurchaseReturnTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $purchaseReturnInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['supplier_purchase_return_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['original_inv_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['ledgers'][0]['decimal'] = 1;
    $purchaseReturnInput['ledgers'][0]['rpu'] = 100.********;
    $purchaseReturnInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $purchaseReturnInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $purchaseReturnInput['ledgers'][0]['gst_id'] = $gst->id;
    $purchaseReturnInput['ledgers'][0]['gst_tax_percentage'] = 2;
    $purchaseReturnInput['ledgers'][0]['with_tax'] = false;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase-return', $purchaseReturnInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Return Transaction created Successfully',
        ]);
    $this->assertDatabaseHas('purchase_return_transactions', [
        'id' => $response->json('data.purchase_return_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_return_transaction.voucher_number'),
        'pr_item_type' => PurchaseReturnTransaction::ACCOUNTING_INVOICE,
    ]);
    $purchaseReturnTransaction = PurchaseReturnTransaction::with('purchaseReturnLedgers')->whereId($response->json('data.purchase_return_transaction.id'))->first();
    $this->assertEquals(128.2, $purchaseReturnTransaction->purchaseReturnLedgers->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 2 for purchase return transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseReturnInput = PurchaseReturnTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'pr_item_type' => PurchaseReturnTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $purchaseReturnInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['supplier_purchase_return_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['original_inv_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['ledgers'][0]['decimal'] = 2;
    $purchaseReturnInput['ledgers'][0]['rpu'] = 100.********;
    $purchaseReturnInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $purchaseReturnInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $purchaseReturnInput['ledgers'][0]['gst_id'] = $gst->id;
    $purchaseReturnInput['ledgers'][0]['gst_tax_percentage'] = 2;
    $purchaseReturnInput['ledgers'][0]['with_tax'] = false;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase-return', $purchaseReturnInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Return Transaction created Successfully',
        ]);
    $this->assertDatabaseHas('purchase_return_transactions', [
        'id' => $response->json('data.purchase_return_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_return_transaction.voucher_number'),
        'pr_item_type' => PurchaseReturnTransaction::ACCOUNTING_INVOICE,
    ]);
    $purchaseReturnTransaction = PurchaseReturnTransaction::with('purchaseReturnLedgers')->whereId($response->json('data.purchase_return_transaction.id'))->first();
    $this->assertEquals(128.16, $purchaseReturnTransaction->purchaseReturnLedgers->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 3 for purchase return transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseReturnInput = PurchaseReturnTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'pr_item_type' => PurchaseReturnTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $purchaseReturnInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['supplier_purchase_return_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['original_inv_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['ledgers'][0]['decimal'] = 3;
    $purchaseReturnInput['ledgers'][0]['rpu'] = 100.********;
    $purchaseReturnInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $purchaseReturnInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $purchaseReturnInput['ledgers'][0]['gst_id'] = $gst->id;
    $purchaseReturnInput['ledgers'][0]['gst_tax_percentage'] = 2;
    $purchaseReturnInput['ledgers'][0]['with_tax'] = false;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase-return', $purchaseReturnInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Return Transaction created Successfully',
        ]);
    $this->assertDatabaseHas('purchase_return_transactions', [
        'id' => $response->json('data.purchase_return_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_return_transaction.voucher_number'),
        'pr_item_type' => PurchaseReturnTransaction::ACCOUNTING_INVOICE,
    ]);
    $purchaseReturnTransaction = PurchaseReturnTransaction::with('purchaseReturnLedgers')->whereId($response->json('data.purchase_return_transaction.id'))->first();
    $this->assertEquals(128.155, $purchaseReturnTransaction->purchaseReturnLedgers->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 4 for purchase return transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseReturnInput = PurchaseReturnTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'pr_item_type' => PurchaseReturnTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $purchaseReturnInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['supplier_purchase_return_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['original_inv_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['ledgers'][0]['decimal'] = 4;
    $purchaseReturnInput['ledgers'][0]['rpu'] = 100.********;
    $purchaseReturnInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $purchaseReturnInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $purchaseReturnInput['ledgers'][0]['gst_id'] = $gst->id;
    $purchaseReturnInput['ledgers'][0]['gst_tax_percentage'] = 2;
    $purchaseReturnInput['ledgers'][0]['with_tax'] = false;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase-return', $purchaseReturnInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Return Transaction created Successfully',
        ]);
    $this->assertDatabaseHas('purchase_return_transactions', [
        'id' => $response->json('data.purchase_return_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_return_transaction.voucher_number'),
        'pr_item_type' => PurchaseReturnTransaction::ACCOUNTING_INVOICE,
    ]);
    $purchaseReturnTransaction = PurchaseReturnTransaction::with('purchaseReturnLedgers')->whereId($response->json('data.purchase_return_transaction.id'))->first();
    $this->assertEquals(128.1552, $purchaseReturnTransaction->purchaseReturnLedgers->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 5 for purchase return transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseReturnInput = PurchaseReturnTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'pr_item_type' => PurchaseReturnTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $purchaseReturnInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['supplier_purchase_return_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['original_inv_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['ledgers'][0]['decimal'] = 5;
    $purchaseReturnInput['ledgers'][0]['rpu'] = 100.********;
    $purchaseReturnInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $purchaseReturnInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $purchaseReturnInput['ledgers'][0]['gst_id'] = $gst->id;
    $purchaseReturnInput['ledgers'][0]['gst_tax_percentage'] = 2;
    $purchaseReturnInput['ledgers'][0]['with_tax'] = false;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase-return', $purchaseReturnInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Return Transaction created Successfully',
        ]);
    $this->assertDatabaseHas('purchase_return_transactions', [
        'id' => $response->json('data.purchase_return_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_return_transaction.voucher_number'),
        'pr_item_type' => PurchaseReturnTransaction::ACCOUNTING_INVOICE,
    ]);
    $purchaseReturnTransaction = PurchaseReturnTransaction::with('purchaseReturnLedgers')->whereId($response->json('data.purchase_return_transaction.id'))->first();
    $this->assertEquals(128.15515, $purchaseReturnTransaction->purchaseReturnLedgers->first()->rpu_with_gst);
});

test('validation with update negative and wrong rpu without or with gst for purchase return transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseReturnInput = PurchaseReturnTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'pr_item_type' => PurchaseReturnTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $purchaseReturnInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['supplier_purchase_return_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['original_inv_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['ledgers'][0]['rpu_without_gst'] = -10000;
    $purchaseReturnInput['ledgers'][0]['rpu_with_gst'] = -10000;
    $purchaseReturnInput['ledgers'][0]['gst_id'] = $gst->id;
    $purchaseReturnInput['ledgers'][0]['gst_tax_percentage'] = 2;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase-return', $purchaseReturnInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Return Transaction created Successfully',
        ]);
    $this->assertDatabaseHas('purchase_return_transactions', [
        'id' => $response->json('data.purchase_return_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_return_transaction.voucher_number'),
        'pr_item_type' => PurchaseReturnTransaction::ACCOUNTING_INVOICE,
    ]);
    $purchaseReturnTransaction = PurchaseReturnTransaction::with('purchaseReturnLedgers')->whereId($response->json('data.purchase_return_transaction.id'))->first();
    $this->assertNotEquals(100, $purchaseReturnTransaction->purchaseReturnLedgers->first()->rpu_without_gst);
    $this->assertEquals(100.0, $purchaseReturnTransaction->purchaseReturnLedgers->first()->rpu_with_gst);
});

test('validation with update negative and wrong gst tax percentage for purchase return transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseReturnInput = PurchaseReturnTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'pr_item_type' => PurchaseReturnTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $purchaseReturnInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['supplier_purchase_return_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['original_inv_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['ledgers'][0]['total'] = -10000;
    $purchaseReturnInput['ledgers'][0]['gst_id'] = $gst->id;
    $purchaseReturnInput['ledgers'][0]['gst_tax_percentage'] = $gst->tax_rate;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase-return', $purchaseReturnInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Return Transaction created Successfully',
        ]);
    $this->assertDatabaseHas('purchase_return_transactions', [
        'id' => $response->json('data.purchase_return_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_return_transaction.voucher_number'),
        'pr_item_type' => PurchaseReturnTransaction::ACCOUNTING_INVOICE,
    ]);
    $purchaseReturnTransaction = PurchaseReturnTransaction::with('purchaseReturnLedgers')->whereId($response->json('data.purchase_return_transaction.id'))->first();
    $this->assertEquals(28, intval($purchaseReturnTransaction->purchaseReturnLedgers->first()->gst_tax_percentage));
});

test('validation with update negative and wrong total discount for purchase return transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(5.00)->first();

    $purchaseReturnInput = PurchaseReturnTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'pr_item_type' => PurchaseReturnTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $purchaseReturnInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['supplier_purchase_return_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['original_inv_date'] = Carbon::now()->format('d-m-Y');
    foreach ($purchaseReturnInput['ledgers'] as $key => $item) {
        $purchaseReturnInput['ledgers'][$key]['gst_id'] = $gst->id;
        $purchaseReturnInput['ledgers'][$key]['discount_type'] = 2;
        $purchaseReturnInput['ledgers'][$key]['discount_value'] = 10;
        $purchaseReturnInput['ledgers'][$key]['discount_type_2'] = 2;
        $purchaseReturnInput['ledgers'][$key]['discount_value_2'] = 10;
        $purchaseReturnInput['ledgers'][$key]['total_discount_amount'] = 10;
    }

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase-return', $purchaseReturnInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Return Transaction created Successfully',
        ]);
    $this->assertDatabaseHas('purchase_return_transactions', [
        'id' => $response->json('data.purchase_return_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_return_transaction.voucher_number'),
        'pr_item_type' => PurchaseReturnTransaction::ACCOUNTING_INVOICE,
    ]);
    $purchaseReturnTransaction = PurchaseReturnTransaction::with('purchaseReturnLedgers')->whereId($response->json('data.purchase_return_transaction.id'))->first();
    foreach ($purchaseReturnTransaction->purchaseReturnLedgers as $item) {
        $this->assertEquals(18.1, $item->total_discount_amount);
    }
});

test('validation with update negative and wrong sub total for purchase return transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseReturnInput = PurchaseReturnTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'pr_item_type' => PurchaseReturnTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $purchaseReturnInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['supplier_purchase_return_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['original_inv_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['ledgers'][0]['total'] = -10000;
    $purchaseReturnInput['ledgers'][0]['gst_id'] = $gst->id;
    $purchaseReturnInput['ledgers'][0]['gst_tax_percentage'] = $gst->tax_rate;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase-return', $purchaseReturnInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Return Transaction created Successfully',
        ]);
    $this->assertDatabaseHas('purchase_return_transactions', [
        'id' => $response->json('data.purchase_return_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_return_transaction.voucher_number'),
        'pr_item_type' => PurchaseReturnTransaction::ACCOUNTING_INVOICE,
    ]);
    $purchaseReturnTransaction = PurchaseReturnTransaction::with('purchaseReturnLedgers')->whereId($response->json('data.purchase_return_transaction.id'))->first();
    $this->assertEquals(78.13, $purchaseReturnTransaction->purchaseReturnLedgers->first()->total);
});

test('validation with update negative and wrong sub total for purchase return transaction account invoice', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $purchaseReturnInput = PurchaseReturnTransactionFactory::new()->make([
        'pr_item_type' => PurchaseReturnTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $purchaseReturnInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['supplier_purchase_return_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['original_inv_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['ledgers'][0]['total'] = -10000;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase-return', $purchaseReturnInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Return Transaction created Successfully',
        ]);
    $this->assertDatabaseHas('purchase_return_transactions', [
        'id' => $response->json('data.purchase_return_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_return_transaction.voucher_number'),
        'pr_item_type' => PurchaseReturnTransaction::ACCOUNTING_INVOICE,
    ]);
    $purchaseReturnTransaction = PurchaseReturnTransaction::with('purchaseReturnLedgers')->whereId($response->json('data.purchase_return_transaction.id'))->first();
    $this->assertEquals(100, intval($purchaseReturnTransaction->purchaseReturnLedgers->first()->total));
});

test('validation with update negative and wrong total for purchase return transaction account invoice', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $purchaseReturnInput = PurchaseReturnTransactionFactory::new()->make([
        'pr_item_type' => PurchaseReturnTransaction::ACCOUNTING_INVOICE,
        'total' => -620,
    ])->toArray();
    $purchaseReturnInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['supplier_purchase_return_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['original_inv_date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase-return', $purchaseReturnInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Return Transaction created Successfully',
        ]);
    $this->assertDatabaseHas('purchase_return_transactions', [
        'id' => $response->json('data.purchase_return_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_return_transaction.voucher_number'),
        'pr_item_type' => PurchaseReturnTransaction::ACCOUNTING_INVOICE,
        'total' => 620.00,
    ]);
});

test('validation with update negative and wrong gross value for purchase return transaction account invoice', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $purchaseReturnInput = PurchaseReturnTransactionFactory::new()->make([
        'pr_item_type' => PurchaseReturnTransaction::ACCOUNTING_INVOICE,
        'gross_value' => -100,
    ])->toArray();
    $purchaseReturnInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['supplier_purchase_return_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['original_inv_date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase-return', $purchaseReturnInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Return Transaction created Successfully',
        ]);
    $this->assertDatabaseHas('purchase_return_transactions', [
        'id' => $response->json('data.purchase_return_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_return_transaction.voucher_number'),
        'pr_item_type' => PurchaseReturnTransaction::ACCOUNTING_INVOICE,
        'gross_value' => 600.00,
    ]);
});

test('validation with update negative and wrong additional charges gst tax percentage for purchase return transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseReturnInput = PurchaseReturnTransactionFactory::new()->make([
        'pr_item_type' => PurchaseReturnTransaction::ACCOUNTING_INVOICE,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $purchaseReturnInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['supplier_purchase_return_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['original_inv_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    $purchaseReturnInput['additional_charges'][0]['ac_total'] = -2000;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase-return', $purchaseReturnInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Return Transaction created Successfully',
        ]);
    $this->assertDatabaseHas('purchase_return_transactions', [
        'id' => $response->json('data.purchase_return_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_return_transaction.voucher_number'),
        'pr_item_type' => PurchaseReturnTransaction::ACCOUNTING_INVOICE,
    ]);
    $purchaseReturnTransaction = PurchaseReturnTransaction::with('additionalCharges')->whereId($response->json('data.purchase_return_transaction.id'))->first();
    $this->assertNotEquals(10.00, $purchaseReturnTransaction->additionalCharges->first()->total);
});

test('validation with update negative and wrong additional charges total for purchase return transaction account invoice', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $purchaseReturnInput = PurchaseReturnTransactionFactory::new()->make([
        'pr_item_type' => PurchaseReturnTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $purchaseReturnInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['supplier_purchase_return_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['original_inv_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['additional_charges'][0]['ac_total'] = -200;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase-return', $purchaseReturnInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Return Transaction created Successfully',
        ]);
    $this->assertDatabaseHas('purchase_return_transactions', [
        'id' => $response->json('data.purchase_return_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_return_transaction.voucher_number'),
        'pr_item_type' => PurchaseReturnTransaction::ACCOUNTING_INVOICE,
        'gross_value' => 600.00,
    ]);
    $purchaseReturnTransaction = PurchaseReturnTransaction::with('additionalCharges')->whereId($response->json('data.purchase_return_transaction.id'))->first();
    $this->assertEquals(10, intval($purchaseReturnTransaction->additionalCharges->first()->total));
});

test('validation with update negative and wrong cgst and sgst when purchase intra state taxable classification for purchase return transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseReturnInput = PurchaseReturnTransactionFactory::new()->make([
        'pr_item_type' => PurchaseReturnTransaction::ACCOUNTING_INVOICE,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'main_classification_nature_type' => 'Intrastate Purchase Taxable',
    ])->toArray();
    $purchaseReturnInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['supplier_purchase_return_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['original_inv_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    foreach ($purchaseReturnInput['ledgers'] as $key => $ledger) {
        $purchaseReturnInput['ledgers'][$key]['gst_id'] = $gst->id;
    }

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase-return', $purchaseReturnInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Return Transaction created Successfully',
        ]);
    $this->assertDatabaseHas('purchase_return_transactions', [
        'id' => $response->json('data.purchase_return_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_return_transaction.voucher_number'),
        'pr_item_type' => PurchaseReturnTransaction::ACCOUNTING_INVOICE,
    ]);
    $purchaseReturnTransaction = PurchaseReturnTransaction::whereId($response->json('data.purchase_return_transaction.id'))->first();
    $this->assertEquals(67.04, $purchaseReturnTransaction->cgst);
    $this->assertEquals(67.04, $purchaseReturnTransaction->sgst);
});

test('validation with update negative and wrong igst when purchase inter state taxable classification for purchase return transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseReturnInput = PurchaseReturnTransactionFactory::new()->make([
        'pr_item_type' => PurchaseReturnTransaction::ACCOUNTING_INVOICE,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'main_classification_nature_type' => 'Interstate Purchase Taxable',
    ])->toArray();
    $purchaseReturnInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['supplier_purchase_return_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['original_inv_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    foreach ($purchaseReturnInput['ledgers'] as $key => $ledger) {
        $purchaseReturnInput['ledgers'][$key]['gst_id'] = $gst->id;
    }

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase-return', $purchaseReturnInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Return Transaction created Successfully',
        ]);
    $this->assertDatabaseHas('purchase_return_transactions', [
        'id' => $response->json('data.purchase_return_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_return_transaction.voucher_number'),
        'pr_item_type' => PurchaseReturnTransaction::ACCOUNTING_INVOICE,
    ]);
    $purchaseReturnTransaction = PurchaseReturnTransaction::whereId($response->json('data.purchase_return_transaction.id'))->first();
    $this->assertEquals(134.08, $purchaseReturnTransaction->igst);
});

test('validation with update negative and wrong taxable value for purchase return transaction account invoice', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $purchaseReturnInput = PurchaseReturnTransactionFactory::new()->make([
        'pr_item_type' => PurchaseReturnTransaction::ACCOUNTING_INVOICE,
        'taxable_value' => -100,
    ])->toArray();
    $purchaseReturnInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['supplier_purchase_return_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['original_inv_date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase-return', $purchaseReturnInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Return Transaction created Successfully',
        ]);
    $this->assertDatabaseHas('purchase_return_transactions', [
        'id' => $response->json('data.purchase_return_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_return_transaction.voucher_number'),
        'pr_item_type' => PurchaseReturnTransaction::ACCOUNTING_INVOICE,
        'taxable_value' => 610.00,
    ]);
});

test('validation with update negative and wrong addless total for purchase return transaction account invoice', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $purchaseReturnInput = PurchaseReturnTransactionFactory::new()->make([
        'pr_item_type' => PurchaseReturnTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $purchaseReturnInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['supplier_purchase_return_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['original_inv_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['add_less'][0]['al_total'] = -100;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase-return', $purchaseReturnInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Return Transaction created Successfully',
        ]);
    $this->assertDatabaseHas('purchase_return_transactions', [
        'id' => $response->json('data.purchase_return_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_return_transaction.voucher_number'),
        'pr_item_type' => PurchaseReturnTransaction::ACCOUNTING_INVOICE,
        'gross_value' => 600.00,
    ]);
    $purchaseReturnTransaction = PurchaseReturnTransaction::with('additionalCharges')->whereId($response->json('data.purchase_return_transaction.id'))->first();
    $this->assertEquals(10, intval($purchaseReturnTransaction->addLess->first()->total));
});

test('validation with update negative and wrong grand total for purchase return transaction account invoice', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $purchaseReturnInput = PurchaseReturnTransactionFactory::new()->make([
        'pr_item_type' => PurchaseReturnTransaction::ACCOUNTING_INVOICE,
        'grand_total' => -100,
    ])->toArray();
    $purchaseReturnInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['supplier_purchase_return_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['original_inv_date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase-return', $purchaseReturnInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Return Transaction created Successfully',
        ]);
    $this->assertDatabaseHas('purchase_return_transactions', [
        'id' => $response->json('data.purchase_return_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_return_transaction.voucher_number'),
        'pr_item_type' => PurchaseReturnTransaction::ACCOUNTING_INVOICE,
        'grand_total' => 620.00,
    ]);
});

test('validation with update negative and wrong round off amount and method for purchase return transaction', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);
    $purchaseReturnInput = PurchaseReturnTransactionFactory::new()->make()->toArray();
    $purchaseReturnInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['supplier_purchase_return_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['original_inv_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['additional_charges'][0]['ac_value'] = 10.54;
    $purchaseReturnInput['is_round_off_not_changed'] = 1;

    $roundOffMethods = [
        ['method' => 1, 'correct_amount' => 0],
        ['method' => 2, 'correct_amount' => -0.54],
        ['method' => 3, 'correct_amount' => 0.46],
        ['method' => 4, 'correct_amount' => 0.46],
    ];
    foreach ($roundOffMethods as $roundOffMethod) {
        $purchaseReturnInput['voucher_number'] = 'V-'.$roundOffMethod['method'];
        $purchaseReturnInput['round_off_method'] = 0;
        ExpenseReturnTransactionMaster::whereCompanyId(1)->update(['round_off_method' => $roundOffMethod['method']]);
        $purchaseReturnInput['rounding_amount'] = -10.50;

         //act
        $response = postAPI($user->token, $user->company->id, 'api/v2/purchase-return', $purchaseReturnInput);

        //assert
        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Purchase Return Transaction created Successfully',
            ]);
        $this->assertDatabaseHas('purchase_return_transactions', [
            'id' => $response->json('data.purchase_return_transaction.id'),
            'company_id' => 1,
            'voucher_number' => $response->json('data.purchase_return_transaction.voucher_number'),
            'round_off_method' => $roundOffMethod,
            'rounding_amount' => $roundOffMethod['correct_amount'],
        ]);
    }
});

/*
|----------------------------------------------------------------------------------------------------------------------------------------------------
| Update Purchase Return Transaction Item Invoice Backend Validation with update Test Case
|----------------------------------------------------------------------------------------------------------------------------------------------------
*/

test('validation with update decimal rpu amount when decimal place is 1 for update purchase return transaction item invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 1]);
    $purchaseReturnInput = PurchaseReturnTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $purchaseReturnInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['supplier_purchase_return_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['original_inv_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnData = StorePurchaseReturnTransactionAction::run($purchaseReturnInput);
    $purchaseReturnInput['items'][0]['rpu'] = 100.********;
    $purchaseReturnInput['items'][0]['rpu_without_gst'] = 100.********;
    $purchaseReturnInput['items'][0]['rpu_with_gst'] = 100.********;
    $purchaseReturnInput['items'][0]['gst_id'] = $gst->id;
    $purchaseReturnInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase-return/'.$purchaseReturnData['transaction_id'], $purchaseReturnInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Return Transaction updated Successfully',
        ]);
    $this->assertDatabaseHas('purchase_return_transactions', [
        'id' => $purchaseReturnData['transaction_id'],
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_return_transaction.voucher_number'),
        'pr_item_type' => PurchaseReturnTransaction::ITEM_INVOICE,
    ]);
    $purchaseReturnTransaction = PurchaseReturnTransaction::with('purchaseReturnItems')->whereId($purchaseReturnData['transaction_id'])->first();
    $this->assertEquals(78.2, $purchaseReturnTransaction->purchaseReturnItems->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 2 for update purchase return transaction item invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 2]);
    $purchaseReturnInput = PurchaseReturnTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $purchaseReturnInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['supplier_purchase_return_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['original_inv_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnData = StorePurchaseReturnTransactionAction::run($purchaseReturnInput);
    $purchaseReturnInput['items'][0]['rpu'] = 100.********;
    $purchaseReturnInput['items'][0]['rpu_without_gst'] = 100.********;
    $purchaseReturnInput['items'][0]['rpu_with_gst'] = 100.********;
    $purchaseReturnInput['items'][0]['gst_id'] = $gst->id;
    $purchaseReturnInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase-return/'.$purchaseReturnData['transaction_id'], $purchaseReturnInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Return Transaction updated Successfully',
        ]);
    $this->assertDatabaseHas('purchase_return_transactions', [
        'id' => $purchaseReturnData['transaction_id'],
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_return_transaction.voucher_number'),
        'pr_item_type' => PurchaseReturnTransaction::ITEM_INVOICE,
    ]);
    $purchaseReturnTransaction = PurchaseReturnTransaction::with('purchaseReturnItems')->whereId($purchaseReturnData['transaction_id'])->first();
    $this->assertEquals(78.22, $purchaseReturnTransaction->purchaseReturnItems->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 3 for update purchase return transaction item invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 3]);
    $purchaseReturnInput = PurchaseReturnTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $purchaseReturnInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['supplier_purchase_return_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['original_inv_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnData = StorePurchaseReturnTransactionAction::run($purchaseReturnInput);
    $purchaseReturnInput['items'][0]['rpu'] = 100.********;
    $purchaseReturnInput['items'][0]['rpu_without_gst'] = 100.********;
    $purchaseReturnInput['items'][0]['rpu_with_gst'] = 100.********;
    $purchaseReturnInput['items'][0]['gst_id'] = $gst->id;
    $purchaseReturnInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase-return/'.$purchaseReturnData['transaction_id'], $purchaseReturnInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Return Transaction updated Successfully',
        ]);
    $this->assertDatabaseHas('purchase_return_transactions', [
        'id' => $purchaseReturnData['transaction_id'],
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_return_transaction.voucher_number'),
        'pr_item_type' => PurchaseReturnTransaction::ITEM_INVOICE,
    ]);
    $purchaseReturnTransaction = PurchaseReturnTransaction::with('purchaseReturnItems')->whereId($purchaseReturnData['transaction_id'])->first();
    $this->assertEquals(78.220, $purchaseReturnTransaction->purchaseReturnItems->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 4 for update purchase return transaction item invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 4]);
    $purchaseReturnInput = PurchaseReturnTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $purchaseReturnInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['supplier_purchase_return_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['original_inv_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnData = StorePurchaseReturnTransactionAction::run($purchaseReturnInput);
    $purchaseReturnInput['items'][0]['rpu'] = 100.********;
    $purchaseReturnInput['items'][0]['rpu_without_gst'] = 100.********;
    $purchaseReturnInput['items'][0]['rpu_with_gst'] = 100.********;
    $purchaseReturnInput['items'][0]['gst_id'] = $gst->id;
    $purchaseReturnInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase-return/'.$purchaseReturnData['transaction_id'], $purchaseReturnInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Return Transaction updated Successfully',
        ]);
    $this->assertDatabaseHas('purchase_return_transactions', [
        'id' => $purchaseReturnData['transaction_id'],
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_return_transaction.voucher_number'),
        'pr_item_type' => PurchaseReturnTransaction::ITEM_INVOICE,
    ]);
    $purchaseReturnTransaction = PurchaseReturnTransaction::with('purchaseReturnItems')->whereId($purchaseReturnData['transaction_id'])->first();
    $this->assertEquals(78.2197, $purchaseReturnTransaction->purchaseReturnItems->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 5 for update purchase return transaction item invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 5]);
    $purchaseReturnInput = PurchaseReturnTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $purchaseReturnInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['supplier_purchase_return_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['original_inv_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnData = StorePurchaseReturnTransactionAction::run($purchaseReturnInput);
    $purchaseReturnInput['items'][0]['rpu'] = 100.********;
    $purchaseReturnInput['items'][0]['rpu_without_gst'] = 100.********;
    $purchaseReturnInput['items'][0]['rpu_with_gst'] = 100.********;
    $purchaseReturnInput['items'][0]['gst_id'] = $gst->id;
    $purchaseReturnInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase-return/'.$purchaseReturnData['transaction_id'], $purchaseReturnInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Return Transaction updated Successfully',
        ]);
    $this->assertDatabaseHas('purchase_return_transactions', [
        'id' => $purchaseReturnData['transaction_id'],
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_return_transaction.voucher_number'),
        'pr_item_type' => PurchaseReturnTransaction::ITEM_INVOICE,
    ]);
    $purchaseReturnTransaction = PurchaseReturnTransaction::with('purchaseReturnItems')->whereId($purchaseReturnData['transaction_id'])->first();
    $this->assertEquals(78.21970, $purchaseReturnTransaction->purchaseReturnItems->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 1 for update purchase return transaction item invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 1]);
    $purchaseReturnInput = PurchaseReturnTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $purchaseReturnInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['supplier_purchase_return_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['original_inv_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnData = StorePurchaseReturnTransactionAction::run($purchaseReturnInput);
    $purchaseReturnInput['items'][0]['rpu'] = 100.********;
    $purchaseReturnInput['items'][0]['rpu_without_gst'] = 100.********;
    $purchaseReturnInput['items'][0]['rpu_with_gst'] = 100.********;
    $purchaseReturnInput['items'][0]['gst_id'] = $gst->id;
    $purchaseReturnInput['items'][0]['gst_tax_percentage'] = 2;
    $purchaseReturnInput['items'][0]['with_tax'] = false;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase-return/'.$purchaseReturnData['transaction_id'], $purchaseReturnInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Return Transaction updated Successfully',
        ]);
    $this->assertDatabaseHas('purchase_return_transactions', [
        'id' => $purchaseReturnData['transaction_id'],
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_return_transaction.voucher_number'),
        'pr_item_type' => PurchaseReturnTransaction::ITEM_INVOICE,
    ]);
    $purchaseReturnTransaction = PurchaseReturnTransaction::with('purchaseReturnItems')->whereId($purchaseReturnData['transaction_id'])->first();
    $this->assertEquals(128.2, $purchaseReturnTransaction->purchaseReturnItems->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 2 for update purchase return transaction item invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 2]);
    $purchaseReturnInput = PurchaseReturnTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $purchaseReturnInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['supplier_purchase_return_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['original_inv_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnData = StorePurchaseReturnTransactionAction::run($purchaseReturnInput);
    $purchaseReturnInput['items'][0]['rpu'] = 100.********;
    $purchaseReturnInput['items'][0]['rpu_without_gst'] = 100.********;
    $purchaseReturnInput['items'][0]['rpu_with_gst'] = 100.********;
    $purchaseReturnInput['items'][0]['gst_id'] = $gst->id;
    $purchaseReturnInput['items'][0]['gst_tax_percentage'] = 2;
    $purchaseReturnInput['items'][0]['with_tax'] = false;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase-return/'.$purchaseReturnData['transaction_id'], $purchaseReturnInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Return Transaction updated Successfully',
        ]);
    $this->assertDatabaseHas('purchase_return_transactions', [
        'id' => $purchaseReturnData['transaction_id'],
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_return_transaction.voucher_number'),
        'pr_item_type' => PurchaseReturnTransaction::ITEM_INVOICE,
    ]);
    $purchaseReturnTransaction = PurchaseReturnTransaction::with('purchaseReturnItems')->whereId($purchaseReturnData['transaction_id'])->first();
    $this->assertEquals(128.16, $purchaseReturnTransaction->purchaseReturnItems->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 3 for update purchase return transaction item invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 3]);
    $purchaseReturnInput = PurchaseReturnTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $purchaseReturnInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['supplier_purchase_return_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['original_inv_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnData = StorePurchaseReturnTransactionAction::run($purchaseReturnInput);
    $purchaseReturnInput['items'][0]['rpu'] = 100.********;
    $purchaseReturnInput['items'][0]['rpu_without_gst'] = 100.********;
    $purchaseReturnInput['items'][0]['rpu_with_gst'] = 100.********;
    $purchaseReturnInput['items'][0]['gst_id'] = $gst->id;
    $purchaseReturnInput['items'][0]['gst_tax_percentage'] = 2;
    $purchaseReturnInput['items'][0]['with_tax'] = false;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase-return/'.$purchaseReturnData['transaction_id'], $purchaseReturnInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Return Transaction updated Successfully',
        ]);
    $this->assertDatabaseHas('purchase_return_transactions', [
        'id' => $purchaseReturnData['transaction_id'],
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_return_transaction.voucher_number'),
        'pr_item_type' => PurchaseReturnTransaction::ITEM_INVOICE,
    ]);
    $purchaseReturnTransaction = PurchaseReturnTransaction::with('purchaseReturnItems')->whereId($purchaseReturnData['transaction_id'])->first();
    $this->assertEquals(128.155, $purchaseReturnTransaction->purchaseReturnItems->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 4 for update purchase return transaction item invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 4]);
    $purchaseReturnInput = PurchaseReturnTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $purchaseReturnInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['supplier_purchase_return_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['original_inv_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnData = StorePurchaseReturnTransactionAction::run($purchaseReturnInput);
    $purchaseReturnInput['items'][0]['rpu'] = 100.********;
    $purchaseReturnInput['items'][0]['rpu_without_gst'] = 100.********;
    $purchaseReturnInput['items'][0]['rpu_with_gst'] = 100.********;
    $purchaseReturnInput['items'][0]['gst_id'] = $gst->id;
    $purchaseReturnInput['items'][0]['gst_tax_percentage'] = 2;
    $purchaseReturnInput['items'][0]['with_tax'] = false;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase-return/'.$purchaseReturnData['transaction_id'], $purchaseReturnInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Return Transaction updated Successfully',
        ]);
    $this->assertDatabaseHas('purchase_return_transactions', [
        'id' => $purchaseReturnData['transaction_id'],
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_return_transaction.voucher_number'),
        'pr_item_type' => PurchaseReturnTransaction::ITEM_INVOICE,
    ]);
    $purchaseReturnTransaction = PurchaseReturnTransaction::with('purchaseReturnItems')->whereId($purchaseReturnData['transaction_id'])->first();
    $this->assertEquals(128.1552, $purchaseReturnTransaction->purchaseReturnItems->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 5 for update purchase return transaction item invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 5]);
    $purchaseReturnInput = PurchaseReturnTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $purchaseReturnInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['supplier_purchase_return_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['original_inv_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnData = StorePurchaseReturnTransactionAction::run($purchaseReturnInput);
    $purchaseReturnInput['items'][0]['rpu'] = 100.********;
    $purchaseReturnInput['items'][0]['rpu_without_gst'] = 100.********;
    $purchaseReturnInput['items'][0]['rpu_with_gst'] = 100.********;
    $purchaseReturnInput['items'][0]['gst_id'] = $gst->id;
    $purchaseReturnInput['items'][0]['gst_tax_percentage'] = 2;
    $purchaseReturnInput['items'][0]['with_tax'] = false;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase-return/'.$purchaseReturnData['transaction_id'], $purchaseReturnInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Return Transaction updated Successfully',
        ]);
    $this->assertDatabaseHas('purchase_return_transactions', [
        'id' => $purchaseReturnData['transaction_id'],
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_return_transaction.voucher_number'),
        'pr_item_type' => PurchaseReturnTransaction::ITEM_INVOICE,
    ]);
    $purchaseReturnTransaction = PurchaseReturnTransaction::with('purchaseReturnItems')->whereId($purchaseReturnData['transaction_id'])->first();
    $this->assertEquals(128.15515, $purchaseReturnTransaction->purchaseReturnItems->first()->rpu_with_gst);
});

test('validation with update negative and wrong rpu without or with gst for update purchase return transaction item invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseReturnInput = PurchaseReturnTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $purchaseReturnInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['supplier_purchase_return_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['original_inv_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnData = StorePurchaseReturnTransactionAction::run($purchaseReturnInput);
    $purchaseReturnInput['items'][0]['rpu_without_gst'] = -10000;
    $purchaseReturnInput['items'][0]['rpu_with_gst'] = -10000;
    $purchaseReturnInput['items'][0]['gst_id'] = $gst->id;
    $purchaseReturnInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase-return/'.$purchaseReturnData['transaction_id'], $purchaseReturnInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Return Transaction updated Successfully',
        ]);
    $this->assertDatabaseHas('purchase_return_transactions', [
        'id' => $purchaseReturnData['transaction_id'],
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_return_transaction.voucher_number'),
        'pr_item_type' => PurchaseReturnTransaction::ITEM_INVOICE,
    ]);
    $purchaseReturnTransaction = PurchaseReturnTransaction::with('purchaseReturnItems')->whereId($purchaseReturnData['transaction_id'])->first();
    $this->assertNotEquals(100, $purchaseReturnTransaction->purchaseReturnItems->first()->rpu_without_gst);
    $this->assertEquals(100.0, $purchaseReturnTransaction->purchaseReturnItems->first()->rpu_with_gst);
});

test('validation with update negative and wrong gst tax percentage for update purchase return transaction item invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseReturnInput = PurchaseReturnTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $purchaseReturnInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['supplier_purchase_return_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['original_inv_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnData = StorePurchaseReturnTransactionAction::run($purchaseReturnInput);
    $purchaseReturnInput['items'][0]['total'] = -10000;
    $purchaseReturnInput['items'][0]['gst_id'] = $gst->id;
    $purchaseReturnInput['items'][0]['gst_tax_percentage'] = $gst->tax_rate;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase-return/'.$purchaseReturnData['transaction_id'], $purchaseReturnInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Return Transaction updated Successfully',
        ]);
    $this->assertDatabaseHas('purchase_return_transactions', [
        'id' => $purchaseReturnData['transaction_id'],
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_return_transaction.voucher_number'),
        'pr_item_type' => PurchaseReturnTransaction::ITEM_INVOICE,
    ]);
    $purchaseReturnTransaction = PurchaseReturnTransaction::with('purchaseReturnItems')->whereId($purchaseReturnData['transaction_id'])->first();
    $this->assertEquals(28, intval($purchaseReturnTransaction->purchaseReturnItems->first()->gst_tax_percentage));
});

test('validation with update negative and wrong total discount for update purchase return transaction item invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(5.00)->first();

    $purchaseReturnInput = PurchaseReturnTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $purchaseReturnInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['supplier_purchase_return_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['original_inv_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnData = StorePurchaseReturnTransactionAction::run($purchaseReturnInput);
    foreach ($purchaseReturnInput['items'] as $key => $item) {
        $purchaseReturnInput['items'][$key]['gst_id'] = $gst->id;
        $purchaseReturnInput['items'][$key]['discount_type'] = 2;
        $purchaseReturnInput['items'][$key]['discount_value'] = 10;
        $purchaseReturnInput['items'][$key]['discount_type_2'] = 2;
        $purchaseReturnInput['items'][$key]['discount_value_2'] = 10;
        $purchaseReturnInput['items'][$key]['total_discount_amount'] = 10;
    }

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase-return/'.$purchaseReturnData['transaction_id'], $purchaseReturnInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Return Transaction updated Successfully',
        ]);
    $this->assertDatabaseHas('purchase_return_transactions', [
        'id' => $purchaseReturnData['transaction_id'],
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_return_transaction.voucher_number'),
        'pr_item_type' => PurchaseReturnTransaction::ITEM_INVOICE,
    ]);
    $purchaseReturnTransaction = PurchaseReturnTransaction::with('purchaseReturnItems')->whereId($purchaseReturnData['transaction_id'])->first();
    foreach ($purchaseReturnTransaction->purchaseReturnItems as $item) {
        $this->assertEquals(18.1, $item->total_discount_amount);
    }
});

test('validation with update negative and wrong sub total for update purchase return transaction item invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseReturnInput = PurchaseReturnTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $purchaseReturnInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['supplier_purchase_return_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['original_inv_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnData = StorePurchaseReturnTransactionAction::run($purchaseReturnInput);
    $purchaseReturnInput['items'][0]['total'] = -10000;
    $purchaseReturnInput['items'][0]['gst_id'] = $gst->id;
    $purchaseReturnInput['items'][0]['gst_tax_percentage'] = $gst->tax_rate;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase-return/'.$purchaseReturnData['transaction_id'], $purchaseReturnInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Return Transaction updated Successfully',
        ]);
    $this->assertDatabaseHas('purchase_return_transactions', [
        'id' => $purchaseReturnData['transaction_id'],
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_return_transaction.voucher_number'),
        'pr_item_type' => PurchaseReturnTransaction::ITEM_INVOICE,
    ]);
    $purchaseReturnTransaction = PurchaseReturnTransaction::with('purchaseReturnItems')->whereId($purchaseReturnData['transaction_id'])->first();
    $this->assertEquals(78.13, $purchaseReturnTransaction->purchaseReturnItems->first()->total);
});

test('validation with update negative and wrong sub total for update purchase return transaction item invoice', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $purchaseReturnInput = PurchaseReturnTransactionFactory::new()->make()->toArray();
    $purchaseReturnInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['supplier_purchase_return_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['original_inv_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnData = StorePurchaseReturnTransactionAction::run($purchaseReturnInput);
    $purchaseReturnInput['items'][0]['total'] = -10000;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase-return/'.$purchaseReturnData['transaction_id'], $purchaseReturnInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Return Transaction updated Successfully',
        ]);
    $this->assertDatabaseHas('purchase_return_transactions', [
        'id' => $purchaseReturnData['transaction_id'],
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_return_transaction.voucher_number'),
        'pr_item_type' => PurchaseReturnTransaction::ITEM_INVOICE,
    ]);
    $purchaseReturnTransaction = PurchaseReturnTransaction::with('purchaseReturnItems')->whereId($purchaseReturnData['transaction_id'])->first();
    $this->assertEquals(100, intval($purchaseReturnTransaction->purchaseReturnItems->first()->total));
});

test('validation with update negative and wrong total for update purchase return transaction item invoice', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $purchaseReturnInput = PurchaseReturnTransactionFactory::new()->make()->toArray();
    $purchaseReturnInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['supplier_purchase_return_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['original_inv_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnData = StorePurchaseReturnTransactionAction::run($purchaseReturnInput);
    $purchaseReturnInput['total'] = -620;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase-return/'.$purchaseReturnData['transaction_id'], $purchaseReturnInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Return Transaction updated Successfully',
        ]);
    $this->assertDatabaseHas('purchase_return_transactions', [
        'id' => $purchaseReturnData['transaction_id'],
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_return_transaction.voucher_number'),
        'pr_item_type' => PurchaseReturnTransaction::ITEM_INVOICE,
        'total' => 620.00,
    ]);
});

test('validation with update negative and wrong gross value for update purchase return transaction item invoice', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $purchaseReturnInput = PurchaseReturnTransactionFactory::new()->make()->toArray();
    $purchaseReturnInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['supplier_purchase_return_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['original_inv_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnData = StorePurchaseReturnTransactionAction::run($purchaseReturnInput);
    $purchaseReturnInput['gross_value'] = -100;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase-return/'.$purchaseReturnData['transaction_id'], $purchaseReturnInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Return Transaction updated Successfully',
        ]);
    $this->assertDatabaseHas('purchase_return_transactions', [
        'id' => $purchaseReturnData['transaction_id'],
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_return_transaction.voucher_number'),
        'pr_item_type' => PurchaseReturnTransaction::ITEM_INVOICE,
        'gross_value' => 600.00,
    ]);
});

test('validation with update negative and wrong additional charges gst tax percentage for update purchase return transaction item invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseReturnInput = PurchaseReturnTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $purchaseReturnInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['supplier_purchase_return_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['original_inv_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnData = StorePurchaseReturnTransactionAction::run($purchaseReturnInput);
    $purchaseReturnInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    $purchaseReturnInput['additional_charges'][0]['ac_total'] = -2000;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase-return/'.$purchaseReturnData['transaction_id'], $purchaseReturnInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Return Transaction updated Successfully',
        ]);
    $this->assertDatabaseHas('purchase_return_transactions', [
        'id' => $purchaseReturnData['transaction_id'],
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_return_transaction.voucher_number'),
        'pr_item_type' => PurchaseReturnTransaction::ITEM_INVOICE,
    ]);
    $purchaseReturnTransaction = PurchaseReturnTransaction::with('additionalCharges')->whereId($purchaseReturnData['transaction_id'])->first();
    $this->assertNotEquals(10.00, $purchaseReturnTransaction->additionalCharges->first()->total);
});

test('validation with update negative and wrong additional charges total for update purchase return transaction item invoice', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $purchaseReturnInput = PurchaseReturnTransactionFactory::new()->make()->toArray();
    $purchaseReturnInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['supplier_purchase_return_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['original_inv_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnData = StorePurchaseReturnTransactionAction::run($purchaseReturnInput);
    $purchaseReturnInput['additional_charges'][0]['ac_total'] = -200;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase-return/'.$purchaseReturnData['transaction_id'], $purchaseReturnInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Return Transaction updated Successfully',
        ]);
    $this->assertDatabaseHas('purchase_return_transactions', [
        'id' => $purchaseReturnData['transaction_id'],
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_return_transaction.voucher_number'),
        'pr_item_type' => PurchaseReturnTransaction::ITEM_INVOICE,
        'gross_value' => 600.00,
    ]);
    $purchaseReturnTransaction = PurchaseReturnTransaction::with('additionalCharges')->whereId($purchaseReturnData['transaction_id'])->first();
    $this->assertEquals(10, intval($purchaseReturnTransaction->additionalCharges->first()->total));
});

test('validation with update negative and wrong cgst and sgst when purchase intra state taxable classification for update purchase return transaction item invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseReturnInput = PurchaseReturnTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'main_classification_nature_type' => 'Intrastate Purchase Taxable',
    ])->toArray();
    $purchaseReturnInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['supplier_purchase_return_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['original_inv_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnData = StorePurchaseReturnTransactionAction::run($purchaseReturnInput);
    $purchaseReturnInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    foreach ($purchaseReturnInput['items'] as $key => $item) {
        $purchaseReturnInput['items'][$key]['gst_id'] = $gst->id;
    }

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase-return/'.$purchaseReturnData['transaction_id'], $purchaseReturnInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Return Transaction updated Successfully',
        ]);
    $this->assertDatabaseHas('purchase_return_transactions', [
        'id' => $purchaseReturnData['transaction_id'],
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_return_transaction.voucher_number'),
        'pr_item_type' => PurchaseReturnTransaction::ITEM_INVOICE,
    ]);
    $purchaseReturnTransaction = PurchaseReturnTransaction::whereId($purchaseReturnData['transaction_id'])->first();
    $this->assertEquals(67.04, $purchaseReturnTransaction->cgst);
    $this->assertEquals(67.04, $purchaseReturnTransaction->sgst);
});

test('validation with update negative and wrong igst when purchase inter state taxable classification for update purchase return transaction item invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseReturnInput = PurchaseReturnTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'main_classification_nature_type' => 'Interstate Purchase Taxable',
    ])->toArray();
    $purchaseReturnInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['supplier_purchase_return_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['original_inv_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnData = StorePurchaseReturnTransactionAction::run($purchaseReturnInput);
    $purchaseReturnInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    foreach ($purchaseReturnInput['items'] as $key => $item) {
        $purchaseReturnInput['items'][$key]['gst_id'] = $gst->id;
    }

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase-return/'.$purchaseReturnData['transaction_id'], $purchaseReturnInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Return Transaction updated Successfully',
        ]);
    $this->assertDatabaseHas('purchase_return_transactions', [
        'id' => $purchaseReturnData['transaction_id'],
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_return_transaction.voucher_number'),
        'pr_item_type' => PurchaseReturnTransaction::ITEM_INVOICE,
    ]);
    $purchaseReturnTransaction = PurchaseReturnTransaction::whereId($purchaseReturnData['transaction_id'])->first();
    $this->assertEquals(134.08, $purchaseReturnTransaction->igst);
});

test('validation with update negative and wrong taxable value for update purchase return transaction item invoice', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $purchaseReturnInput = PurchaseReturnTransactionFactory::new()->make()->toArray();
    $purchaseReturnInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['supplier_purchase_return_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['original_inv_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnData = StorePurchaseReturnTransactionAction::run($purchaseReturnInput);
    $purchaseReturnInput['taxable_value'] = -100;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase-return/'.$purchaseReturnData['transaction_id'], $purchaseReturnInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Return Transaction updated Successfully',
        ]);
    $this->assertDatabaseHas('purchase_return_transactions', [
        'id' => $purchaseReturnData['transaction_id'],
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_return_transaction.voucher_number'),
        'pr_item_type' => PurchaseReturnTransaction::ITEM_INVOICE,
        'taxable_value' => 610.00,
    ]);
});

test('validation with update negative and wrong addless total for update purchase return transaction item invoice', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $purchaseReturnInput = PurchaseReturnTransactionFactory::new()->make()->toArray();
    $purchaseReturnInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['supplier_purchase_return_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['original_inv_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnData = StorePurchaseReturnTransactionAction::run($purchaseReturnInput);
    $purchaseReturnInput['add_less'][0]['al_total'] = -100;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase-return/'.$purchaseReturnData['transaction_id'], $purchaseReturnInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Return Transaction updated Successfully',
        ]);
    $this->assertDatabaseHas('purchase_return_transactions', [
        'id' => $purchaseReturnData['transaction_id'],
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_return_transaction.voucher_number'),
        'pr_item_type' => PurchaseReturnTransaction::ITEM_INVOICE,
        'gross_value' => 600.00,
    ]);
    $purchaseReturnTransaction = PurchaseReturnTransaction::with('additionalCharges')->whereId($purchaseReturnData['transaction_id'])->first();
    $this->assertEquals(10, intval($purchaseReturnTransaction->addLess->first()->total));
});

test('validation with update negative and wrong grand total for update purchase return transaction item invoice', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $purchaseReturnInput = PurchaseReturnTransactionFactory::new()->make()->toArray();
    $purchaseReturnInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['supplier_purchase_return_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['original_inv_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnData = StorePurchaseReturnTransactionAction::run($purchaseReturnInput);
    $purchaseReturnInput['grand_total'] = -100;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase-return/'.$purchaseReturnData['transaction_id'], $purchaseReturnInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Return Transaction updated Successfully',
        ]);
    $this->assertDatabaseHas('purchase_return_transactions', [
        'id' => $purchaseReturnData['transaction_id'],
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_return_transaction.voucher_number'),
        'pr_item_type' => PurchaseReturnTransaction::ITEM_INVOICE,
        'grand_total' => 620.00,
    ]);
});

/*
|----------------------------------------------------------------------------------------------------------------------------------------------------
| Update Purchase Return Transaction Account Invoice Backend Validation with update Test Case
|----------------------------------------------------------------------------------------------------------------------------------------------------
*/

test('validation with update decimal rpu amount when decimal place is 1 for update purchase return transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseReturnInput = PurchaseReturnTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'pr_item_type' => PurchaseReturnTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $purchaseReturnInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['supplier_purchase_return_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['original_inv_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnData = StorePurchaseReturnTransactionAction::run($purchaseReturnInput);
    $purchaseReturnInput['ledgers'][0]['decimal'] = 1;
    $purchaseReturnInput['ledgers'][0]['rpu'] = 100.********;
    $purchaseReturnInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $purchaseReturnInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $purchaseReturnInput['ledgers'][0]['gst_id'] = $gst->id;
    $purchaseReturnInput['ledgers'][0]['gst_tax_percentage'] = 2;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase-return/'.$purchaseReturnData['transaction_id'], $purchaseReturnInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Return Transaction updated Successfully',
        ]);
    $this->assertDatabaseHas('purchase_return_transactions', [
        'id' => $purchaseReturnData['transaction_id'],
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_return_transaction.voucher_number'),
        'pr_item_type' => PurchaseReturnTransaction::ACCOUNTING_INVOICE,
    ]);
    $purchaseReturnTransaction = PurchaseReturnTransaction::with('purchaseReturnLedgers')->whereId($purchaseReturnData['transaction_id'])->first();
    $this->assertEquals(78.2, $purchaseReturnTransaction->purchaseReturnLedgers->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 2 for update purchase return transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseReturnInput = PurchaseReturnTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'pr_item_type' => PurchaseReturnTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $purchaseReturnInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['supplier_purchase_return_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['original_inv_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnData = StorePurchaseReturnTransactionAction::run($purchaseReturnInput);
    $purchaseReturnInput['ledgers'][0]['decimal'] = 2;
    $purchaseReturnInput['ledgers'][0]['rpu'] = 100.********;
    $purchaseReturnInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $purchaseReturnInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $purchaseReturnInput['ledgers'][0]['gst_id'] = $gst->id;
    $purchaseReturnInput['ledgers'][0]['gst_tax_percentage'] = 2;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase-return/'.$purchaseReturnData['transaction_id'], $purchaseReturnInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Return Transaction updated Successfully',
        ]);
    $this->assertDatabaseHas('purchase_return_transactions', [
        'id' => $purchaseReturnData['transaction_id'],
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_return_transaction.voucher_number'),
        'pr_item_type' => PurchaseReturnTransaction::ACCOUNTING_INVOICE,
    ]);
    $purchaseReturnTransaction = PurchaseReturnTransaction::with('purchaseReturnLedgers')->whereId($purchaseReturnData['transaction_id'])->first();
    $this->assertEquals(78.22, $purchaseReturnTransaction->purchaseReturnLedgers->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 3 for update purchase return transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseReturnInput = PurchaseReturnTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'pr_item_type' => PurchaseReturnTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $purchaseReturnInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['supplier_purchase_return_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['original_inv_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnData = StorePurchaseReturnTransactionAction::run($purchaseReturnInput);
    $purchaseReturnInput['ledgers'][0]['decimal'] = 3;
    $purchaseReturnInput['ledgers'][0]['rpu'] = 100.********;
    $purchaseReturnInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $purchaseReturnInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $purchaseReturnInput['ledgers'][0]['gst_id'] = $gst->id;
    $purchaseReturnInput['ledgers'][0]['gst_tax_percentage'] = 2;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase-return/'.$purchaseReturnData['transaction_id'], $purchaseReturnInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Return Transaction updated Successfully',
        ]);
    $this->assertDatabaseHas('purchase_return_transactions', [
        'id' => $purchaseReturnData['transaction_id'],
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_return_transaction.voucher_number'),
        'pr_item_type' => PurchaseReturnTransaction::ACCOUNTING_INVOICE,
    ]);
    $purchaseReturnTransaction = PurchaseReturnTransaction::with('purchaseReturnLedgers')->whereId($purchaseReturnData['transaction_id'])->first();
    $this->assertEquals(78.220, $purchaseReturnTransaction->purchaseReturnLedgers->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 4 for update purchase return transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseReturnInput = PurchaseReturnTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'pr_item_type' => PurchaseReturnTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $purchaseReturnInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['supplier_purchase_return_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['original_inv_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnData = StorePurchaseReturnTransactionAction::run($purchaseReturnInput);
    $purchaseReturnInput['ledgers'][0]['decimal'] = 4;
    $purchaseReturnInput['ledgers'][0]['rpu'] = 100.********;
    $purchaseReturnInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $purchaseReturnInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $purchaseReturnInput['ledgers'][0]['gst_id'] = $gst->id;
    $purchaseReturnInput['ledgers'][0]['gst_tax_percentage'] = 2;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase-return/'.$purchaseReturnData['transaction_id'], $purchaseReturnInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Return Transaction updated Successfully',
        ]);
    $this->assertDatabaseHas('purchase_return_transactions', [
        'id' => $purchaseReturnData['transaction_id'],
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_return_transaction.voucher_number'),
        'pr_item_type' => PurchaseReturnTransaction::ACCOUNTING_INVOICE,
    ]);
    $purchaseReturnTransaction = PurchaseReturnTransaction::with('purchaseReturnLedgers')->whereId($purchaseReturnData['transaction_id'])->first();
    $this->assertEquals(78.2197, $purchaseReturnTransaction->purchaseReturnLedgers->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 5 for update purchase return transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseReturnInput = PurchaseReturnTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'pr_item_type' => PurchaseReturnTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $purchaseReturnInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['supplier_purchase_return_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['original_inv_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnData = StorePurchaseReturnTransactionAction::run($purchaseReturnInput);
    $purchaseReturnInput['ledgers'][0]['decimal'] = 5;
    $purchaseReturnInput['ledgers'][0]['rpu'] = 100.********;
    $purchaseReturnInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $purchaseReturnInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $purchaseReturnInput['ledgers'][0]['gst_id'] = $gst->id;
    $purchaseReturnInput['ledgers'][0]['gst_tax_percentage'] = 2;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase-return/'.$purchaseReturnData['transaction_id'], $purchaseReturnInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Return Transaction updated Successfully',
        ]);
    $this->assertDatabaseHas('purchase_return_transactions', [
        'id' => $purchaseReturnData['transaction_id'],
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_return_transaction.voucher_number'),
        'pr_item_type' => PurchaseReturnTransaction::ACCOUNTING_INVOICE,
    ]);
    $purchaseReturnTransaction = PurchaseReturnTransaction::with('purchaseReturnLedgers')->whereId($purchaseReturnData['transaction_id'])->first();
    $this->assertEquals(78.21970, $purchaseReturnTransaction->purchaseReturnLedgers->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 1 for update purchase return transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseReturnInput = PurchaseReturnTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'pr_item_type' => PurchaseReturnTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $purchaseReturnInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['supplier_purchase_return_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['original_inv_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnData = StorePurchaseReturnTransactionAction::run($purchaseReturnInput);
    $purchaseReturnInput['ledgers'][0]['decimal'] = 1;
    $purchaseReturnInput['ledgers'][0]['rpu'] = 100.********;
    $purchaseReturnInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $purchaseReturnInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $purchaseReturnInput['ledgers'][0]['gst_id'] = $gst->id;
    $purchaseReturnInput['ledgers'][0]['gst_tax_percentage'] = 2;
    $purchaseReturnInput['ledgers'][0]['with_tax'] = false;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase-return/'.$purchaseReturnData['transaction_id'], $purchaseReturnInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Return Transaction updated Successfully',
        ]);
    $this->assertDatabaseHas('purchase_return_transactions', [
        'id' => $purchaseReturnData['transaction_id'],
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_return_transaction.voucher_number'),
        'pr_item_type' => PurchaseReturnTransaction::ACCOUNTING_INVOICE,
    ]);
    $purchaseReturnTransaction = PurchaseReturnTransaction::with('purchaseReturnLedgers')->whereId($purchaseReturnData['transaction_id'])->first();
    $this->assertEquals(128.2, $purchaseReturnTransaction->purchaseReturnLedgers->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 2 for update purchase return transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseReturnInput = PurchaseReturnTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'pr_item_type' => PurchaseReturnTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $purchaseReturnInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['supplier_purchase_return_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['original_inv_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnData = StorePurchaseReturnTransactionAction::run($purchaseReturnInput);
    $purchaseReturnInput['ledgers'][0]['decimal'] = 2;
    $purchaseReturnInput['ledgers'][0]['rpu'] = 100.********;
    $purchaseReturnInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $purchaseReturnInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $purchaseReturnInput['ledgers'][0]['gst_id'] = $gst->id;
    $purchaseReturnInput['ledgers'][0]['gst_tax_percentage'] = 2;
    $purchaseReturnInput['ledgers'][0]['with_tax'] = false;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase-return/'.$purchaseReturnData['transaction_id'], $purchaseReturnInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Return Transaction updated Successfully',
        ]);
    $this->assertDatabaseHas('purchase_return_transactions', [
        'id' => $purchaseReturnData['transaction_id'],
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_return_transaction.voucher_number'),
        'pr_item_type' => PurchaseReturnTransaction::ACCOUNTING_INVOICE,
    ]);
    $purchaseReturnTransaction = PurchaseReturnTransaction::with('purchaseReturnLedgers')->whereId($purchaseReturnData['transaction_id'])->first();
    $this->assertEquals(128.16, $purchaseReturnTransaction->purchaseReturnLedgers->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 3 for update purchase return transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseReturnInput = PurchaseReturnTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'pr_item_type' => PurchaseReturnTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $purchaseReturnInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['supplier_purchase_return_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['original_inv_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnData = StorePurchaseReturnTransactionAction::run($purchaseReturnInput);
    $purchaseReturnInput['ledgers'][0]['decimal'] = 3;
    $purchaseReturnInput['ledgers'][0]['rpu'] = 100.********;
    $purchaseReturnInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $purchaseReturnInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $purchaseReturnInput['ledgers'][0]['gst_id'] = $gst->id;
    $purchaseReturnInput['ledgers'][0]['gst_tax_percentage'] = 2;
    $purchaseReturnInput['ledgers'][0]['with_tax'] = false;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase-return/'.$purchaseReturnData['transaction_id'], $purchaseReturnInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Return Transaction updated Successfully',
        ]);
    $this->assertDatabaseHas('purchase_return_transactions', [
        'id' => $purchaseReturnData['transaction_id'],
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_return_transaction.voucher_number'),
        'pr_item_type' => PurchaseReturnTransaction::ACCOUNTING_INVOICE,
    ]);
    $purchaseReturnTransaction = PurchaseReturnTransaction::with('purchaseReturnLedgers')->whereId($purchaseReturnData['transaction_id'])->first();
    $this->assertEquals(128.155, $purchaseReturnTransaction->purchaseReturnLedgers->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 4 for update purchase return transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseReturnInput = PurchaseReturnTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'pr_item_type' => PurchaseReturnTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $purchaseReturnInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['supplier_purchase_return_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['original_inv_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnData = StorePurchaseReturnTransactionAction::run($purchaseReturnInput);
    $purchaseReturnInput['ledgers'][0]['decimal'] = 4;
    $purchaseReturnInput['ledgers'][0]['rpu'] = 100.********;
    $purchaseReturnInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $purchaseReturnInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $purchaseReturnInput['ledgers'][0]['gst_id'] = $gst->id;
    $purchaseReturnInput['ledgers'][0]['gst_tax_percentage'] = 2;
    $purchaseReturnInput['ledgers'][0]['with_tax'] = false;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase-return/'.$purchaseReturnData['transaction_id'], $purchaseReturnInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Return Transaction updated Successfully',
        ]);
    $this->assertDatabaseHas('purchase_return_transactions', [
        'id' => $purchaseReturnData['transaction_id'],
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_return_transaction.voucher_number'),
        'pr_item_type' => PurchaseReturnTransaction::ACCOUNTING_INVOICE,
    ]);
    $purchaseReturnTransaction = PurchaseReturnTransaction::with('purchaseReturnLedgers')->whereId($purchaseReturnData['transaction_id'])->first();
    $this->assertEquals(128.1552, $purchaseReturnTransaction->purchaseReturnLedgers->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 5 for update purchase return transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseReturnInput = PurchaseReturnTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'pr_item_type' => PurchaseReturnTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $purchaseReturnInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['supplier_purchase_return_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['original_inv_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnData = StorePurchaseReturnTransactionAction::run($purchaseReturnInput);
    $purchaseReturnInput['ledgers'][0]['decimal'] = 5;
    $purchaseReturnInput['ledgers'][0]['rpu'] = 100.********;
    $purchaseReturnInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $purchaseReturnInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $purchaseReturnInput['ledgers'][0]['gst_id'] = $gst->id;
    $purchaseReturnInput['ledgers'][0]['gst_tax_percentage'] = 2;
    $purchaseReturnInput['ledgers'][0]['with_tax'] = false;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase-return/'.$purchaseReturnData['transaction_id'], $purchaseReturnInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Return Transaction updated Successfully',
        ]);
    $this->assertDatabaseHas('purchase_return_transactions', [
        'id' => $purchaseReturnData['transaction_id'],
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_return_transaction.voucher_number'),
        'pr_item_type' => PurchaseReturnTransaction::ACCOUNTING_INVOICE,
    ]);
    $purchaseReturnTransaction = PurchaseReturnTransaction::with('purchaseReturnLedgers')->whereId($purchaseReturnData['transaction_id'])->first();
    $this->assertEquals(128.15515, $purchaseReturnTransaction->purchaseReturnLedgers->first()->rpu_with_gst);
});

test('validation with update negative and wrong rpu without or with gst for update purchase return transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseReturnInput = PurchaseReturnTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'pr_item_type' => PurchaseReturnTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $purchaseReturnInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['supplier_purchase_return_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['original_inv_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnData = StorePurchaseReturnTransactionAction::run($purchaseReturnInput);
    $purchaseReturnInput['ledgers'][0]['rpu_without_gst'] = -10000;
    $purchaseReturnInput['ledgers'][0]['rpu_with_gst'] = -10000;
    $purchaseReturnInput['ledgers'][0]['gst_id'] = $gst->id;
    $purchaseReturnInput['ledgers'][0]['gst_tax_percentage'] = 2;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase-return/'.$purchaseReturnData['transaction_id'], $purchaseReturnInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Return Transaction updated Successfully',
        ]);
    $this->assertDatabaseHas('purchase_return_transactions', [
        'id' => $response->json('data.purchase_return_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_return_transaction.voucher_number'),
        'pr_item_type' => PurchaseReturnTransaction::ACCOUNTING_INVOICE,
    ]);
    $purchaseReturnTransaction = PurchaseReturnTransaction::with('purchaseReturnLedgers')->whereId($response->json('data.purchase_return_transaction.id'))->first();
    $this->assertNotEquals(100, $purchaseReturnTransaction->purchaseReturnLedgers->first()->rpu_without_gst);
    $this->assertEquals(100.0, $purchaseReturnTransaction->purchaseReturnLedgers->first()->rpu_with_gst);
});

test('validation with update negative and wrong gst tax percentage for update purchase return transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseReturnInput = PurchaseReturnTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'pr_item_type' => PurchaseReturnTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $purchaseReturnInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['supplier_purchase_return_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['original_inv_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnData = StorePurchaseReturnTransactionAction::run($purchaseReturnInput);
    $purchaseReturnInput['ledgers'][0]['total'] = -10000;
    $purchaseReturnInput['ledgers'][0]['gst_id'] = $gst->id;
    $purchaseReturnInput['ledgers'][0]['gst_tax_percentage'] = $gst->tax_rate;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase-return/'.$purchaseReturnData['transaction_id'], $purchaseReturnInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Return Transaction updated Successfully',
        ]);
    $this->assertDatabaseHas('purchase_return_transactions', [
        'id' => $response->json('data.purchase_return_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_return_transaction.voucher_number'),
        'pr_item_type' => PurchaseReturnTransaction::ACCOUNTING_INVOICE,
    ]);
    $purchaseReturnTransaction = PurchaseReturnTransaction::with('purchaseReturnLedgers')->whereId($response->json('data.purchase_return_transaction.id'))->first();
    $this->assertEquals(28, intval($purchaseReturnTransaction->purchaseReturnLedgers->first()->gst_tax_percentage));
});

test('validation with update negative and wrong total discount for update purchase return transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(5.00)->first();

    $purchaseReturnInput = PurchaseReturnTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'pr_item_type' => PurchaseReturnTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $purchaseReturnInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['supplier_purchase_return_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['original_inv_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnData = StorePurchaseReturnTransactionAction::run($purchaseReturnInput);
    foreach ($purchaseReturnInput['ledgers'] as $key => $item) {
        $purchaseReturnInput['ledgers'][$key]['gst_id'] = $gst->id;
        $purchaseReturnInput['ledgers'][$key]['discount_type'] = 2;
        $purchaseReturnInput['ledgers'][$key]['discount_value'] = 10;
        $purchaseReturnInput['ledgers'][$key]['discount_type_2'] = 2;
        $purchaseReturnInput['ledgers'][$key]['discount_value_2'] = 10;
        $purchaseReturnInput['ledgers'][$key]['total_discount_amount'] = 10;
    }

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase-return/'.$purchaseReturnData['transaction_id'], $purchaseReturnInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Return Transaction updated Successfully',
        ]);
    $this->assertDatabaseHas('purchase_return_transactions', [
        'id' => $response->json('data.purchase_return_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_return_transaction.voucher_number'),
        'pr_item_type' => PurchaseReturnTransaction::ACCOUNTING_INVOICE,
    ]);
    $purchaseReturnTransaction = PurchaseReturnTransaction::with('purchaseReturnLedgers')->whereId($response->json('data.purchase_return_transaction.id'))->first();
    foreach ($purchaseReturnTransaction->purchaseReturnLedgers as $item) {
        $this->assertEquals(18.1, $item->total_discount_amount);
    }
});

test('validation with update negative and wrong sub total for update purchase return transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseReturnInput = PurchaseReturnTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'pr_item_type' => PurchaseReturnTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $purchaseReturnInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['supplier_purchase_return_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['original_inv_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnData = StorePurchaseReturnTransactionAction::run($purchaseReturnInput);
    $purchaseReturnInput['ledgers'][0]['total'] = -10000;
    $purchaseReturnInput['ledgers'][0]['gst_id'] = $gst->id;
    $purchaseReturnInput['ledgers'][0]['gst_tax_percentage'] = $gst->tax_rate;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase-return/'.$purchaseReturnData['transaction_id'], $purchaseReturnInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Return Transaction updated Successfully',
        ]);
    $this->assertDatabaseHas('purchase_return_transactions', [
        'id' => $response->json('data.purchase_return_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_return_transaction.voucher_number'),
        'pr_item_type' => PurchaseReturnTransaction::ACCOUNTING_INVOICE,
    ]);
    $purchaseReturnTransaction = PurchaseReturnTransaction::with('purchaseReturnLedgers')->whereId($response->json('data.purchase_return_transaction.id'))->first();
    $this->assertEquals(78.13, $purchaseReturnTransaction->purchaseReturnLedgers->first()->total);
});

test('validation with update negative and wrong sub total for update purchase return transaction account invoice', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $purchaseReturnInput = PurchaseReturnTransactionFactory::new()->make([
        'pr_item_type' => PurchaseReturnTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $purchaseReturnInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['supplier_purchase_return_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['original_inv_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnData = StorePurchaseReturnTransactionAction::run($purchaseReturnInput);
    $purchaseReturnInput['ledgers'][0]['total'] = -10000;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase-return/'.$purchaseReturnData['transaction_id'], $purchaseReturnInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Return Transaction updated Successfully',
        ]);
    $this->assertDatabaseHas('purchase_return_transactions', [
        'id' => $response->json('data.purchase_return_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_return_transaction.voucher_number'),
        'pr_item_type' => PurchaseReturnTransaction::ACCOUNTING_INVOICE,
    ]);
    $purchaseReturnTransaction = PurchaseReturnTransaction::with('purchaseReturnLedgers')->whereId($response->json('data.purchase_return_transaction.id'))->first();
    $this->assertEquals(100, intval($purchaseReturnTransaction->purchaseReturnLedgers->first()->total));
});

test('validation with update negative and wrong total for update purchase return transaction account invoice', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $purchaseReturnInput = PurchaseReturnTransactionFactory::new()->make([
        'pr_item_type' => PurchaseReturnTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $purchaseReturnInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['supplier_purchase_return_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['original_inv_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnData = StorePurchaseReturnTransactionAction::run($purchaseReturnInput);
    $purchaseReturnInput['total'] = -620;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase-return/'.$purchaseReturnData['transaction_id'], $purchaseReturnInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Return Transaction updated Successfully',
        ]);
    $this->assertDatabaseHas('purchase_return_transactions', [
        'id' => $response->json('data.purchase_return_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_return_transaction.voucher_number'),
        'pr_item_type' => PurchaseReturnTransaction::ACCOUNTING_INVOICE,
        'total' => 620.00,
    ]);
});

test('validation with update negative and wrong gross value for update purchase return transaction account invoice', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $purchaseReturnInput = PurchaseReturnTransactionFactory::new()->make([
        'pr_item_type' => PurchaseReturnTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $purchaseReturnInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['supplier_purchase_return_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['original_inv_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnData = StorePurchaseReturnTransactionAction::run($purchaseReturnInput);
    $purchaseReturnInput['gross_value'] = -100;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase-return/'.$purchaseReturnData['transaction_id'], $purchaseReturnInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Return Transaction updated Successfully',
        ]);
    $this->assertDatabaseHas('purchase_return_transactions', [
        'id' => $response->json('data.purchase_return_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_return_transaction.voucher_number'),
        'pr_item_type' => PurchaseReturnTransaction::ACCOUNTING_INVOICE,
        'gross_value' => 600.00,
    ]);
});

test('validation with update negative and wrong additional charges gst tax percentage for update purchase return transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseReturnInput = PurchaseReturnTransactionFactory::new()->make([
        'pr_item_type' => PurchaseReturnTransaction::ACCOUNTING_INVOICE,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $purchaseReturnInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['supplier_purchase_return_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['original_inv_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnData = StorePurchaseReturnTransactionAction::run($purchaseReturnInput);
    $purchaseReturnInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    $purchaseReturnInput['additional_charges'][0]['ac_total'] = -2000;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase-return/'.$purchaseReturnData['transaction_id'], $purchaseReturnInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Return Transaction updated Successfully',
        ]);
    $this->assertDatabaseHas('purchase_return_transactions', [
        'id' => $response->json('data.purchase_return_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_return_transaction.voucher_number'),
        'pr_item_type' => PurchaseReturnTransaction::ACCOUNTING_INVOICE,
    ]);
    $purchaseReturnTransaction = PurchaseReturnTransaction::with('additionalCharges')->whereId($response->json('data.purchase_return_transaction.id'))->first();
    $this->assertNotEquals(10.00, $purchaseReturnTransaction->additionalCharges->first()->total);
});

test('validation with update negative and wrong additional charges total for update purchase return transaction account invoice', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $purchaseReturnInput = PurchaseReturnTransactionFactory::new()->make([
        'pr_item_type' => PurchaseReturnTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $purchaseReturnInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['supplier_purchase_return_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['original_inv_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnData = StorePurchaseReturnTransactionAction::run($purchaseReturnInput);
    $purchaseReturnInput['additional_charges'][0]['ac_total'] = -200;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase-return/'.$purchaseReturnData['transaction_id'], $purchaseReturnInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Return Transaction updated Successfully',
        ]);
    $this->assertDatabaseHas('purchase_return_transactions', [
        'id' => $response->json('data.purchase_return_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_return_transaction.voucher_number'),
        'pr_item_type' => PurchaseReturnTransaction::ACCOUNTING_INVOICE,
        'gross_value' => 600.00,
    ]);
    $purchaseReturnTransaction = PurchaseReturnTransaction::with('additionalCharges')->whereId($response->json('data.purchase_return_transaction.id'))->first();
    $this->assertEquals(10, intval($purchaseReturnTransaction->additionalCharges->first()->total));
});

test('validation with update negative and wrong cgst and sgst when purchase intra state taxable classification for update purchase return transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseReturnInput = PurchaseReturnTransactionFactory::new()->make([
        'pr_item_type' => PurchaseReturnTransaction::ACCOUNTING_INVOICE,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'main_classification_nature_type' => 'Intrastate Purchase Taxable',
    ])->toArray();
    $purchaseReturnInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['supplier_purchase_return_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['original_inv_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnData = StorePurchaseReturnTransactionAction::run($purchaseReturnInput);
    $purchaseReturnInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    foreach ($purchaseReturnInput['ledgers'] as $key => $ledger) {
        $purchaseReturnInput['ledgers'][$key]['gst_id'] = $gst->id;
    }

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase-return/'.$purchaseReturnData['transaction_id'], $purchaseReturnInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Return Transaction updated Successfully',
        ]);
    $this->assertDatabaseHas('purchase_return_transactions', [
        'id' => $response->json('data.purchase_return_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_return_transaction.voucher_number'),
        'pr_item_type' => PurchaseReturnTransaction::ACCOUNTING_INVOICE,
    ]);
    $purchaseReturnTransaction = PurchaseReturnTransaction::whereId($response->json('data.purchase_return_transaction.id'))->first();
    $this->assertEquals(67.04, $purchaseReturnTransaction->cgst);
    $this->assertEquals(67.04, $purchaseReturnTransaction->sgst);
});

test('validation with update negative and wrong igst when purchase inter state taxable classification for update purchase return transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseReturnInput = PurchaseReturnTransactionFactory::new()->make([
        'pr_item_type' => PurchaseReturnTransaction::ACCOUNTING_INVOICE,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'main_classification_nature_type' => 'Interstate Purchase Taxable',
    ])->toArray();
    $purchaseReturnInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['supplier_purchase_return_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['original_inv_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnData = StorePurchaseReturnTransactionAction::run($purchaseReturnInput);
    $purchaseReturnInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    foreach ($purchaseReturnInput['ledgers'] as $key => $ledger) {
        $purchaseReturnInput['ledgers'][$key]['gst_id'] = $gst->id;
    }

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase-return/'.$purchaseReturnData['transaction_id'], $purchaseReturnInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Return Transaction updated Successfully',
        ]);
    $this->assertDatabaseHas('purchase_return_transactions', [
        'id' => $response->json('data.purchase_return_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_return_transaction.voucher_number'),
        'pr_item_type' => PurchaseReturnTransaction::ACCOUNTING_INVOICE,
    ]);
    $purchaseReturnTransaction = PurchaseReturnTransaction::whereId($response->json('data.purchase_return_transaction.id'))->first();
    $this->assertEquals(134.08, $purchaseReturnTransaction->igst);
});

test('validation with update negative and wrong taxable value for update purchase return transaction account invoice', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $purchaseReturnInput = PurchaseReturnTransactionFactory::new()->make([
        'pr_item_type' => PurchaseReturnTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $purchaseReturnInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['supplier_purchase_return_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['original_inv_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnData = StorePurchaseReturnTransactionAction::run($purchaseReturnInput);
    $purchaseReturnInput['taxable_value'] = -1;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase-return/'.$purchaseReturnData['transaction_id'], $purchaseReturnInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Return Transaction updated Successfully',
        ]);
    $this->assertDatabaseHas('purchase_return_transactions', [
        'id' => $response->json('data.purchase_return_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_return_transaction.voucher_number'),
        'pr_item_type' => PurchaseReturnTransaction::ACCOUNTING_INVOICE,
        'taxable_value' => 610.00,
    ]);
});

test('validation with update negative and wrong addless total for update purchase return transaction account invoice', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $purchaseReturnInput = PurchaseReturnTransactionFactory::new()->make([
        'pr_item_type' => PurchaseReturnTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $purchaseReturnInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['supplier_purchase_return_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['original_inv_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnData = StorePurchaseReturnTransactionAction::run($purchaseReturnInput);
    $purchaseReturnInput['add_less'][0]['al_total'] = -100;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase-return/'.$purchaseReturnData['transaction_id'], $purchaseReturnInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Return Transaction updated Successfully',
        ]);
    $this->assertDatabaseHas('purchase_return_transactions', [
        'id' => $response->json('data.purchase_return_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_return_transaction.voucher_number'),
        'pr_item_type' => PurchaseReturnTransaction::ACCOUNTING_INVOICE,
        'gross_value' => 600.00,
    ]);
    $purchaseReturnTransaction = PurchaseReturnTransaction::with('additionalCharges')->whereId($response->json('data.purchase_return_transaction.id'))->first();
    $this->assertEquals(10, intval($purchaseReturnTransaction->addLess->first()->total));
});

test('validation with update negative and wrong grand total for update purchase return transaction account invoice', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $purchaseReturnInput = PurchaseReturnTransactionFactory::new()->make([
        'pr_item_type' => PurchaseReturnTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $purchaseReturnInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['supplier_purchase_return_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['original_inv_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnData = StorePurchaseReturnTransactionAction::run($purchaseReturnInput);
    $purchaseReturnInput['grand_total'] = -100;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase-return/'.$purchaseReturnData['transaction_id'], $purchaseReturnInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Return Transaction updated Successfully',
        ]);
    $this->assertDatabaseHas('purchase_return_transactions', [
        'id' => $response->json('data.purchase_return_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_return_transaction.voucher_number'),
        'pr_item_type' => PurchaseReturnTransaction::ACCOUNTING_INVOICE,
        'grand_total' => 620.00,
    ]);
});

test('validation with update negative and wrong round off amount and method for update purchase return transaction', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);
    $purchaseReturnInput = PurchaseReturnTransactionFactory::new()->make()->toArray();
    $purchaseReturnInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['supplier_purchase_return_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['original_inv_date'] = Carbon::now()->format('d-m-Y');
    $purchaseReturnInput['additional_charges'][0]['ac_value'] = 10.54;
    $purchaseReturnInput['is_round_off_not_changed'] = 1;

    $roundOffMethods = [
        ['method' => 1, 'correct_amount' => 0],
        ['method' => 2, 'correct_amount' => -0.54],
        ['method' => 3, 'correct_amount' => 0.46],
        ['method' => 4, 'correct_amount' => 0.46],
    ];
    foreach ($roundOffMethods as $roundOffMethod) {
        $purchaseReturnInput['voucher_number'] = 'V-'.$roundOffMethod['method'];
        $purchaseReturnData = StorePurchaseReturnTransactionAction::run($purchaseReturnInput);
        $purchaseReturnInput['round_off_method'] = 0;
        ExpenseReturnTransactionMaster::whereCompanyId(1)->update(['round_off_method' => $roundOffMethod['method']]);
        $purchaseReturnInput['rounding_amount'] = -10.50;

         //act
         $response = postAPI($user->token, $user->company->id, 'api/v2/purchase-return/'.$purchaseReturnData['transaction_id'], $purchaseReturnInput);

        //assert
        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Purchase Return Transaction updated Successfully',
            ]);
        $this->assertDatabaseHas('purchase_return_transactions', [
            'id' => $response->json('data.purchase_return_transaction.id'),
            'company_id' => 1,
            'voucher_number' => $response->json('data.purchase_return_transaction.voucher_number'),
            'round_off_method' => $roundOffMethod,
            'rounding_amount' => $roundOffMethod['correct_amount'],
        ]);
    }
});
