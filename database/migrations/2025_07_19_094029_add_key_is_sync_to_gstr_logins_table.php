<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('gstr_logins', function (Blueprint $table) {
            $table->dropColumn('synced');
            $table->timestamp('sync_started_at')->nullable();
            $table->timestamp('sync_completed_at')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('gstr_logins', function (Blueprint $table) {
            $table->boolean('synced')->default(false);
            $table->dropColumn('sync_started_at');
            $table->dropColumn('sync_completed_at');
        });
    }
};
