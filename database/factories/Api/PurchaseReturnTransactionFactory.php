<?php

namespace Database\Factories\Api;

use App\Models\AdditionalChargesForPurchaseReturnTransaction;
use App\Models\Ledger;
use App\Models\Master\Income;
use App\Models\Master\ItemMaster;
use App\Models\PurchaseReturnTransaction;
use Barryvdh\Reflection\DocBlock\Type\Collection;
use Carbon\Carbon;
use Closure;
use Faker\Factory as Faker;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Model>
 */
class PurchaseReturnTransactionFactory extends Factory
{
    protected $model = PurchaseReturnTransaction::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $attributes = $this->getAttributes(); // for purchase return item type get value from test data
        $gstTaxes = getGstTaxes();
        $ledgerId = Ledger::whereCompanyId(getCurrentCompany()->id)->whereModelType(Income::class)->first()->id;
        $purchaseReturnInputs = [
            'voucher_number' => 'V-'.rand(1, 5000),
            'voucher_date' => $this->getFinancialYearDate(),
            'supplier_purchase_return_date' => $this->getFinancialYearDate(),
            'original_inv_date' => $this->getFinancialYearDate(),
            'supplier_id' => array_rand(getAllChildTransactionLedgers([\App\Models\Master\Customer::class, \App\Models\Master\Supplier::class])),
            'pr_item_type' => $attributes['pr_item_type'] ?? 2, // 2: Item Invoice, 1: accounting invoice
            'grand_total' => 600,
            'is_cgst_sgst_igst_calculated' => false,
            'is_gst_na' => false,
            'total' => 100,
            'narration' => 'test',
            'term_and_condition' => 'test',
            'submit_button_value' => PurchaseReturnTransaction::SAVE_BUTTON,
            'main_classification_nature_type' => 'Intrastate Purchase Taxable',
            'is_gst_enabled' => 0,
            'is_round_off_not_changed' => 0,
            'round_off_method' => 3,
            'rounding_amount' => 0.0,
            'taxable_value' => 0,
            'gross_value' => 600,
            'billing_address' => [
                'address_1' => $this->faker->address(),
                'country_id' => 1,
                'state_id' => 1,
                'city_id' => 1,
            ],
            'shipping_address' => [
                'shipping_name' => $this->faker->name(),
                'country_id' => 1,
                'state_id' => 1,
                'city_id' => 1,
            ],
            'additional_charges' => [
                [
                    'ac_ledger_id' => $ledgerId,
                    'ac_type' => AdditionalChargesForPurchaseReturnTransaction::AMOUNT,
                    'ac_value' => 10,
                    'ac_total' => 10,
                    'ac_total_without_tax' => 10,
                ],
            ],
            'add_less' => [
                [
                    'al_ledger_id' => $ledgerId,
                    'al_is_show_in_print' => true,
                    'al_type' => 1,
                    'al_value' => 10,
                    'al_total' => 10,
                ],
            ],
            'payment_details' => [
                [
                    'pd_ledger_id' => $ledgerId,
                    'pd_date' => date('d-m-Y'),
                    'pd_amount' => 100,
                ],
            ],
            'is_rcm_applicable' => 0,
            'cgst' => 0,
            'sgst' => 0,
            'igst' => 0,
            'cess' => 0,
        ];

        if ($purchaseReturnInputs['pr_item_type'] == 2) {
            return array_merge($this->getItemInvoiceInputs($gstTaxes), $purchaseReturnInputs);
        } elseif ($purchaseReturnInputs['pr_item_type'] == 1) {
            return array_merge($this->getAccountingInvoiceInputs($gstTaxes), $purchaseReturnInputs);
        }

        return $purchaseReturnInputs;
    }

    private function getItemInvoiceInputs(array $gstTaxes): array
    {
        $ids = array_keys(getItemMasters());
        $items = ItemMaster::whereIn('id', $ids)->get()->keyBy('id');

        $purchaseReturnItemsInputs = [];
        foreach ($items as $id => $item) {
            $gstID = array_rand($gstTaxes);
            $gstPercentage = in_array($gstTaxes[$gstID], ['0%', 'NA', 'Exempt']) ? 0 : $gstTaxes[$gstID];
            $gstPercentage = str_replace('%', '', $gstPercentage);
            $ledgerId = Ledger::whereCompanyId(getCurrentCompany()->id)->whereName(Ledger::PURCHASE)->first()->id;

            $purchaseReturnItemsInputs[] = [
                'item_id' => $id,
                'additional_description' => $this->faker->sentence(),
                'rpu' => 100,
                'rpu_with_gst' => 100,
                'rpu_without_gst' => 100,
                'mrp' => 100,
                'with_tax' => true,
                'discount_type' => 1,
                'discount_value' => 0,
                'total_discount_amount' => 0,
                'gst_id' => $gstID,
                'gst_tax' => $gstID,
                'gst_tax_percentage' => $gstPercentage,
                'total' => 100,
                'cess' => 0,
                'ledger_id' => $ledgerId,
                'unit_id' => $item->model->unit_of_measurement,
                'quantity' => 1,
                'classification_igst_tax' => 0,
                'classification_cgst_tax' => 0,
                'classification_sgst_tax' => 0,
            ];
        }

        return ['items' => $purchaseReturnItemsInputs];
    }

    private function getAccountingInvoiceInputs(array $gstTaxes): array
    {
        $ids = array_keys(getAccountInvoiceLedgerLists());
        $ledgers = Ledger::where('company_id', getCurrentCompany()->id)->whereIn('id', $ids)->get()->keyBy('id');

        $purchaseReturnLedgerInputs = [];
        foreach ($ledgers as $id => $ledger) {
            $gstID = array_rand($gstTaxes);
            $gstPercentage = in_array($gstTaxes[$gstID], ['0%', 'NA', 'Exempt']) ? 0 : $gstTaxes[$gstID];
            $gstPercentage = str_replace('%', '', $gstPercentage);

            $purchaseReturnLedgerInputs[] = [
                'ledger_id' => $id,
                'additional_description' => $this->faker->sentence(),
                'rpu' => 100,
                'rpu_without_gst' => 100,
                'rpu_with_gst' => 100,
                'with_tax' => true,
                'discount_type' => 1,
                'discount_value' => 0,
                'gst_id' => $gstID,
                'gst_tax' => $gstID,
                'gst_tax_percentage' => $gstPercentage,
                'total' => 100,
                'classification_igst_tax' => 0,
                'classification_cgst_tax' => 0,
                'classification_sgst_tax' => 0,
            ];
        }

        return ['ledgers' => $purchaseReturnLedgerInputs];
    }

    /**
     * Generate a random date within the current financial year.
     */
    private function getFinancialYearDate(): string
    {
        $faker = Faker::create();

        $currentYear = now()->year;
        $financialYearStart = Carbon::createFromDate($currentYear, 4, 1)->format('d-m-Y'); // Assuming financial year starts from April 1st
        $financialYearEnd = Carbon::createFromDate($currentYear + 1, 3, 31)->format('d-m-Y'); // Ends on March 31st of the next year

        return $faker->dateTimeBetween($financialYearStart, $financialYearEnd)->format('d-m-Y');
    }

    public function getAttributes()
    {
        $parent = null;

        return $this->states->pipe(function ($states) {
            return $this->for->isEmpty() ? $states : new Collection(array_merge([function () {
                return $this->parentResolvers();
            }], $states->all()));
        })->reduce(function ($carry, $state) use ($parent) {
            if ($state instanceof Closure) {
                $state = $state->bindTo($this);
            }

            return array_merge($carry, $state($carry, $parent));
        }, []);

    }
}
