<?php

namespace App\Http\Livewire;

use App\Actions\Report\GstDashboard\GetTransaction;
use App\Actions\Report\Gstr1\PrepareGstDashboardTableData;
use App\Models\GstDashboardData;
use App\Models\GstrLogin;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

class GstDashboardTable extends CustomLivewireTableComponent
{
    public $financialYear = '';

    protected $listeners = [
        'refresh' => '$refresh',
        'financialYear',
        'sortColumn',
    ];

    public function render()
    {
        $gstDashboardCacheKey = generateCacheKey('gst_dashboard');
        $data = Cache::get($gstDashboardCacheKey);
        Cache::forget($gstDashboardCacheKey);
        $this->financialYear = empty($this->financialYear) ? $data['financialYears'] ?? getCurrentFinancialYearDetails()['currentFinancialYear'] : $this->financialYear;

        $startYear = explode(' - ', $this->financialYear)[0];
        $endYear = explode(' - ', $this->financialYear)[1];

        $rows = [];
        $gstDashboardTransactionsCacheKey = generateCacheKey('gst_transactions_'.$startYear.'_'.$endYear);
        if (empty(Cache::get($gstDashboardTransactionsCacheKey))) {
            $transactions = GetTransaction::run($startYear, $endYear);
        } else {
            $transactions = Cache::get($gstDashboardTransactionsCacheKey);
        }
        $data = GstDashboardData::with(['saleGstR1Data', 'saleGstR3BData', 'saleGstR2AData'])
            ->where(function ($query) use ($startYear, $endYear) {
                $query->where(function ($q) use ($startYear) {
                    // For the start year, include months from April (04) to December (12)
                    $q->where(DB::raw('SUBSTRING(month, 3, 4)'), $startYear)
                        ->whereBetween(DB::raw('SUBSTRING(month, 1, 2)'), ['04', '12']);
                })
                    ->orWhere(function ($q) use ($endYear) {
                        // For the end year, include months from January (01) to March (03)
                        $q->where(DB::raw('SUBSTRING(month, 3, 4)'), $endYear)
                            ->whereBetween(DB::raw('SUBSTRING(month, 1, 2)'), ['01', '03']);
                    });
            })
            ->orderBy(DB::raw("STR_TO_DATE(CONCAT('01', month), '%d%m%Y')"))
            ->get();

        $data = PrepareGstDashboardTableData::run($data, $transactions);
        $data = $this->prepareAllDataWithSearchingAndSorting($data);
        $gstrLogin = GstrLogin::first();
        $syncCompleted = GstrLogin::SYNC_NOT_STARTED; // Not Completed
        if (! empty($gstrLogin->sync_started_at) && empty($gstrLogin->sync_completed_at)) {
            $syncCompleted = GstrLogin::SYNC_IN_PROGRESS; // Running
        } elseif (! empty($gstrLogin->sync_started_at) && ! empty($gstrLogin->sync_completed_at)) {
            $syncCompleted = GstrLogin::SYNC_COMPLETED; // Completed
        }
        $rows['data'] = $data;
        $rows['financialYears'] = $this->financialYear;
        $rows['fields'] = $this->getFields();
        Cache::put($gstDashboardCacheKey, $rows);

        return view('livewire.gst-dashboard-table', compact('rows', 'syncCompleted'));
    }

    public function getFields(): array
    {
        return [
            [
                'name' => 'Month',
                'column_name' => 'month',
                'is_sortable' => true,
                'is_sorting' => array_key_exists('month', $this->sortColumn),
                'sorting_direction' => $this->sortColumn['month'] ?? '',
                'is_searchable' => true,
                'is_show' => true,
                'show_total' => false,
                'is_number' => false,
            ],
            [
                'name' => 'GSTR1 Status',
                'column_name' => 'transaction_type',
                'is_sortable' => true,
                'is_sorting' => array_key_exists('transaction_type', $this->sortColumn),
                'sorting_direction' => $this->sortColumn['transaction_type'] ?? '',
                'is_searchable' => true,
                'is_show' => true,
                'show_total' => false,
                'is_number' => false,
            ],
            [
                'name' => 'GSTR 3B Status',
                'column_name' => 'invoice_number',
                'is_sortable' => true,
                'is_sorting' => array_key_exists('invoice_number', $this->sortColumn),
                'sorting_direction' => $this->sortColumn['invoice_number'] ?? '',
                'is_searchable' => true,
                'is_show' => true,
                'show_total' => false,
                'is_number' => false,
            ],
            [
                'name' => 'Sales as per Books',
                'column_name' => 'sales',
                'is_sortable' => true,
                'is_sorting' => array_key_exists('sales', $this->sortColumn),
                'sorting_direction' => $this->sortColumn['sales'] ?? '',
                'is_searchable' => true,
                'is_show' => true,
                'show_total' => true,
                'is_number' => false,
            ],
            [
                'name' => 'Sales as per GSTR 1',
                'column_name' => 'gstr_1_sum',
                'is_sortable' => true,
                'is_sorting' => array_key_exists('gstr_1_sum', $this->sortColumn),
                'sorting_direction' => $this->sortColumn['gstr_1_sum'] ?? '',
                'is_searchable' => true,
                'is_show' => true,
                'show_total' => true,
                'is_number' => false,
            ],
            [
                'name' => 'Sales as per GSTR 3B',
                'column_name' => 'gstr_3b_sum',
                'is_sortable' => true,
                'is_sorting' => array_key_exists('gstr_3b_sum', $this->sortColumn),
                'sorting_direction' => $this->sortColumn['gstr_3b_sum'] ?? '',
                'is_searchable' => true,
                'is_show' => true,
                'show_total' => true,
                'is_number' => false,
            ],
            [
                'name' => 'ITC as per Books',
                'column_name' => 'itc',
                'is_sortable' => true,
                'is_sorting' => array_key_exists('itc', $this->sortColumn),
                'sorting_direction' => $this->sortColumn['itc'] ?? '',
                'is_searchable' => true,
                'is_show' => true,
                'show_total' => true,
                'is_number' => false,
            ],
            [
                'name' => 'ITC as per GSTR 2A',
                'column_name' => 'itc_gstr_2a_sum',
                'is_sortable' => true,
                'is_sorting' => array_key_exists('itc_gstr_2a_sum', $this->sortColumn),
                'sorting_direction' => $this->sortColumn['itc_gstr_2a_sum'] ?? '',
                'is_searchable' => true,
                'is_show' => true,
                'show_total' => true,
                'is_number' => false,
            ],
            [
                'name' => 'ITC as per GSTR 3B',
                'column_name' => 'itc_gstr_3b_sum',
                'is_sortable' => true,
                'is_sorting' => array_key_exists('itc_gstr_3b_sum', $this->sortColumn),
                'sorting_direction' => $this->sortColumn['itc_gstr_3b_sum'] ?? '',
                'is_searchable' => true,
                'is_show' => true,
                'show_total' => true,
                'is_number' => false,
            ],
        ];
    }

    public function financialYear($year)
    {
        $this->financialYear = $year;
    }
}
