<?php

namespace App\Http\Controllers;

use App\Models\Company;
use App\Models\CompanySubscriptionList;
use App\Models\TeamManagement;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Contracts\View\Factory;
use \Illuminate\Contracts\View\View;

/**
 * Class FranchiseDashboardController
 */
class FranchiseDashboardController extends Controller
{
    /**
     * @return View
     */
    public function index(): View
    {
        $data['clientTotal'] = $this->clientTotalDetails();
        $data['frequency'] = $this->franchiseFrequencyDetail();
        $data['revenuePerMonth'] = $this->revenuePerMonth();
        $this->revenuePerMonth();

        return view('franchise.dashboard.dashboard')->with($data);
    }

    public function clientTotalDetails(): array
    {
        $data = [];
        $franchiseId = getFranchiseId();
        $data['activeClients'] = Company::whereFranchiseId($franchiseId)->whereHas('user', function ($q) {
            $q->where('status', true);
        })->count();

        $data['inactiveClients'] = Company::whereFranchiseId($franchiseId)->whereHas('user', function ($q) {
            $q->where('status', false);
        })->count();

        $data['totalClients'] = Company::whereFranchiseId($franchiseId)->count();

        return $data;
    }

    public function franchiseFrequencyDetail(): array
    {

        $frequency = Company::whereFranchiseId(getFranchiseId())->whereHas('user', function ($q) {
            $q->where('status', true);
        })
            ->withSum([
                'frequencyDetail as countOfWeeklyVisit1' => function ($query) {
                    $query->where('name', '!=', CompanySubscriptionList::MONTHLY)->wherePhysicalFrequencyWeekMonth(1);
                },
            ], 'physical_frequency_week_month')
            ->withSum([
                'frequencyDetail as countOfWeeklyVisit2' => function ($query) {
                    $query->where('name', '!=', CompanySubscriptionList::MONTHLY)->wherePhysicalFrequencyWeekMonth(2);
                },
            ], 'physical_frequency_week_month')
            ->withSum([
                'frequencyDetail as countOfWeeklyVisit3' => function ($query) {
                    $query->where('name', '!=', CompanySubscriptionList::MONTHLY)->wherePhysicalFrequencyWeekMonth(3);
                },
            ], 'physical_frequency_week_month')
            ->withSum([
                'frequencyDetail as countOfWeeklyVisit4' => function ($query) {
                    $query->where('name', '!=', CompanySubscriptionList::MONTHLY)->wherePhysicalFrequencyWeekMonth(4);
                },
            ], 'physical_frequency_week_month')
            ->withSum([
                'frequencyDetail as countOfWeeklyVisit5' => function ($query) {
                    $query->where('name', '!=', CompanySubscriptionList::MONTHLY)->wherePhysicalFrequencyWeekMonth(5);
                },
            ], 'physical_frequency_week_month')
            ->withSum([
                'frequencyDetail as countOfWeeklyVisit6' => function ($query) {
                    $query->where('name', '!=', CompanySubscriptionList::MONTHLY)->wherePhysicalFrequencyWeekMonth(6);
                },
            ], 'physical_frequency_week_month')
            ->withSum([
                'frequencyDetail as countOfMonthlyVisit1' => function ($query) {
                    $query->where('name', CompanySubscriptionList::MONTHLY)->wherePhysicalFrequencyWeekMonth(1);
                },
            ], 'physical_frequency_week_month')
            ->withSum([
                'frequencyDetail as countOfMonthlyVisit2' => function ($query) {
                    $query->where('name', CompanySubscriptionList::MONTHLY)->wherePhysicalFrequencyWeekMonth(2);
                },
            ], 'physical_frequency_week_month')
            ->withSum([
                'frequencyDetail as countOfMonthlyVisit3' => function ($query) {
                    $query->where('name', CompanySubscriptionList::MONTHLY)->wherePhysicalFrequencyWeekMonth(3);
                },
            ], 'physical_frequency_week_month')
            ->get();

        $data = [];
        $data['countOfWeeklyVisit1'] = $frequency->sum('countOfWeeklyVisit1');
        $data['countOfWeeklyVisit2'] = $frequency->sum('countOfWeeklyVisit2');
        $data['countOfWeeklyVisit3'] = $frequency->sum('countOfWeeklyVisit3');
        $data['countOfWeeklyVisit4'] = $frequency->sum('countOfWeeklyVisit4');
        $data['countOfWeeklyVisit5'] = $frequency->sum('countOfWeeklyVisit5');
        $data['countOfWeeklyVisit6'] = $frequency->sum('countOfWeeklyVisit6');
        $data['countOfMonthlyVisit1'] = $frequency->sum('countOfMonthlyVisit1');
        $data['countOfMonthlyVisit2'] = $frequency->sum('countOfMonthlyVisit2');
        $data['countOfMonthlyVisit3'] = $frequency->sum('countOfMonthlyVisit3');

        return $data;
    }

    /**
     * @return mixed
     */
    public function juniorAccountantClientList($id)
    {
        return Company::whereHas('user', function ($q) {
            $q->where('status', true);
        })
            ->whereHas('juniorAccountant', function ($q) {
                $q->where('designation', TeamManagement::JUNIOR_ACCOUNTANT);
            })
            ->where('junior_accountant_id', $id)
            ->select('trade_name')
            ->get();
    }

    /**
     * @return int|mixed
     */
    public function revenuePerMonth()
    {
        $franchiseId = getFranchiseId();
        $revenuePerMonth = Company::whereFranchiseId($franchiseId)->whereHas('user', function ($q) {
            $q->where('status', true);
        })
            ->select('monthly_fees')
            ->get();

        return $revenuePerMonth->sum('monthly_fees') ?? 0;
    }
}