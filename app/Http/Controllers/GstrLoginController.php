<?php

namespace App\Http\Controllers;

use App\Actions\CommonAction\GetGstInformationAction;
use App\Actions\Reports\Gstr1\GenerateGstr1Summary;
use App\Exports\Gstr1ReportErrorExport;
use App\Models\GstDashboardData;
use App\Models\GstFiling;
use App\Models\GstrLogin;
use App\Services\FileGstService;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Facades\Excel;

class GstrLoginController extends AppBaseController
{
    /** @var FileGstService */
    public mixed $fileGstService;

    public function __construct()
    {
        $this->fileGstService = new FileGstService();
    }

    public function index()
    {
        return view('gstr-login');
    }

    public function login(Request $request)
    {
        $input = $request->all();
        $company = getCurrentCompany();
        $gstin = $company->companyTax->gstin;
        $gstrLogin = GstrLogin::whereCompanyId($company->id)->first();
        $statecd = substr($gstin, 0, 2);
        $headers = [
            'username' => $input['user_name'] ?? $gstrLogin->username ?? null,
            'otp' => $input['otp'] ?? '000000',
            'gstin' => $gstin,
            'action' => 'B2B',
            'statecd' => $statecd,
            'retperiod' => '032025',
        ];
        $fileGst = $this->fileGstService;
        $data = [];
        $data['hideModal'] = false;
        if (! empty($gstrLogin)) {
            if (! isset($input['resend'])) {
                if (! empty($input['otp'])) {
                    $data['api'] = $fileGst->getGstR1Data($headers);
                    if ($data['api']['errorCode'] == 'RETOTPREQUEST') {
                        $message = 'Invalid OTP. Login unsuccessful.';

                        return $this->sendError($message);
                    }
                    $gstrLogin = $gstrLogin->update([
                        'username' => $input['user_name'],
                        'status' => GstrLogin::CONNECTED,
                        'otp' => $input['otp'],
                    ]);
                    $data['hideModal'] = true;
                }
            }
        } else {
            $gstrLogin = GstrLogin::create([
                'company_id' => $company->id,
                'username' => $input['user_name'],
                'status' => GstrLogin::DISCONNECTED,
            ]);
        }
        $data['api'] = $fileGst->getGstR1Data($headers);
        Log::channel('gstr1')->alert('GSTR1Login', $data['api']);
        if (isset($data['api']) && isset($data['api']['errorCode']) && ($data['api']['errorCode'] == 'RETOTPREQUEST')) {
            $message = 'OTP sent to your registered mobile/email with GST Portal';

            return $this->sendResponse($data, $message);
        }

        if (! empty($data['api']) && isset($data['api']['errorCode'])) {
            $message = $data['api']['message'];

            return $this->sendError($message);
        }

        if ($data['hideModal'] || ! empty($data['api'])) {
            $dashboardData = [
                'company_id' => $company->id,
                'gstin' => $gstin,
            ];
            $gstrLogin->update([
                'status' => GstrLogin::CONNECTED,
                'username' => $input['user_name'],
                'otp' => $input['otp'] ?? 000000,
            ]);
            $data['hideModal'] = true;
            $getCurrentFinancialYearDetails = getCurrentFinancialYearDetails();
            $fullYear = $getCurrentFinancialYearDetails['currentFinancialYear']; // e.g. "2025 - 2026"

            [$start, $end] = explode(' - ', $fullYear);
            $shortFormat = $start.'-'.substr($end, -2);
            $headers = [
                'gstin' => $gstin,
                'year' => $shortFormat,
            ];

            $fillingType = $fileGst->getGstr1Preference($headers);
            Log::info('fillingType', $fillingType);
            if ($fillingType['success'] == 'true') {
                $gstrLogin->update([
                    'filing_type' => $fillingType['result']['response'][0]['preference'],
                ]);
            }

            return $this->sendResponse($data, 'Connected to GST Portal successfully.');
        }

        $message = $data['api']['message'];

        return $this->sendError($message);
    }

    public function viewGstr1Summary(Request $request)
    {
        $input = $request->all();
        $company = getCurrentCompany();
        $gstr1Data = GstDashboardData::whereId($input['id'])->with('saleGstR1Data')->first();
        $gstr1File = json_decode($gstr1Data->saleGstR1Data->sales_gstr_1);
        $gstInformation = GetGstInformationAction::run($company->companyTax->gstin);
        $gstInformation = json_decode($gstInformation);
        $gstrData = [];

        if (isset($gstr1File->sec_sum)) {
            foreach ($gstr1File->sec_sum as $key => $value) {
                $gstrData[$value->sec_nm] = $value;
            }
        }

        $month = substr($gstr1File->ret_period, 0, 2); // "05"
        $year = substr($gstr1File->ret_period, 2, 4);  // "2024"

        if ($month >= '04') {
            $startYear = $year;
            $endYear = $year + 1;
        } else {
            $startYear = $year - 1;
            $endYear = $year;
        }
        $financialYear = $startYear.'-'.substr($endYear, -2);

        $dateObj = Carbon::createFromFormat('!m', $month);
        $monthName = $dateObj->format('F'); // Full month name (e.g., "May")
        $gstrData['year'] = $financialYear;
        $gstrData['month'] = $monthName;

        $html = view('company.gst-dashboard.view-summary-gstr1', compact('gstrData', 'company', 'gstr1Data', 'gstInformation'))->render();

        return $this->sendResponse($html, 'Gstr1 summary fetched successfully');
    }

    public function viewGstr1Detailed(Request $request)
    {
        $section = $request->section;
        $gstrCacheKey = generateCacheKey('gst_dashboard');
        $data = Cache::get($gstrCacheKey);

        return view('company.gst-dashboard.view-detailed-gstr1', compact('data', 'section'));
    }

    public function viewGstr3BSummary(Request $request)
    {
        $input = $request->all();
        $gstr3BData = GstDashboardData::whereId($input['id'])->with('saleGstR3BData')->first();
        $gstr3bFile = json_decode($gstr3BData->saleGstR3BData->sales_gstr_3b);
        $gstrData = [];
        $company = getCurrentCompany();
        $gstInformation = GetGstInformationAction::run($company->companyTax->gstin);
        $gstInformation = json_decode($gstInformation);
        $month = substr($gstr3bFile->ret_period, 0, 2); // "05"
        $year = substr($gstr3bFile->ret_period, 2, 4);  // "2024"

        if ($month >= '04') {
            $startYear = $year;
            $endYear = $year + 1;
        } else {
            $startYear = $year - 1;
            $endYear = $year;
        }
        $financialYear = $startYear.'-'.substr($endYear, -2);

        $dateObj = Carbon::createFromFormat('!m', $month);
        $monthName = $dateObj->format('F'); // Full month name (e.g., "May")
        $gstrData['year'] = $financialYear;
        $gstrData['month'] = $monthName;
        $gstrData['gstr3bFile'] = $gstr3bFile;

        $html = view('company.gst-dashboard.view-summary-gstr3b', compact('gstrData', 'company', 'gstr3BData', 'gstInformation'))->render();

        return $this->sendResponse($html, 'Gstr3B summary fetched successfully');
    }

    public function reload(Request $request)
    {
        $input = $request->all();
        $filingPeriod = ! empty($input['end_date']) ? Carbon::parse($input['end_date'])->format('mY') : null;
        $fileGst = $this->fileGstService;
        $gstr1File = GstFiling::where('return_period', $filingPeriod)->where('company_id', getCurrentCompany()->id)->first();
        $gstLogin = GstrLogin::latest()->first();
        $company = getCurrentCompany();
        $gstin = $company->companyTax->gstin;
        $statecd = substr($gstin, 0, 2);

        if (! empty($gstr1File)) {
            $headers = [
                'gstin' => $gstin,
                'username' => $gstLogin->username,
                'otp' => $gstLogin->otp,
                'statecd' => $statecd,
                'retperiod' => $filingPeriod,
                'requestid' => $gstr1File->requestId,
            ];
            $filingStatus = $fileGst->checkGstFilingStatus($headers);

            $invoiceType = [
                'B2B',
                'B2CL',
                'B2CS',
                'CDNRU',
                'DOC ISSUES',
                'HSNSUM',
            ];

            foreach ($invoiceType as $key => $value) {
                $headers['action'] = $value;
                $gstGetInvoice[$value] = $fileGst->getGstr1Invoice($headers);
            }

            if (isset($filingStatus) && isset($filingStatus['errorCode']) && ($filingStatus['errorCode'] == 'RETOTPREQUEST')) {
                $message = 'OTP sent to your registered mobile/email with GST Portal';
                $gstLogin->update([
                    'status' => GstrLogin::DISCONNECTED,
                    'otp' => null,
                ]);
                $data = [
                    'showModal' => true,
                    'data' => $gstLogin,
                ];

                return $this->sendErrorResponse($data, $message);
            }

            if (isset($filingStatus) && isset($filingStatus['errorCode'])) {
                Log::channel('gstr1')->error('Filing GSTR1 Report', [
                    'company_id' => $company->id,
                    'error' => $filingStatus['message'],
                    'requestId' => $gstr1File->requestId,
                    'return_period' => $filingPeriod,
                ]);

                return $this->sendError($filingStatus['message']);
            }

            $gstr1File->update([
                'status' => GstFiling::STATUS[$filingStatus['status_cd']],
                'meta_data' => json_encode($gstGetInvoice),
            ]);

            if ($filingStatus['status_cd'] == 'ER') {
                Log::channel('gstr1')->error('Filing GSTR1 Report', [
                    'company_id' => $company->id,
                    'error' => $filingStatus['error_report'],
                    'requestId' => $gstr1File->requestId,
                    'return_period' => $filingPeriod,
                ]);

                return $this->sendError($filingStatus['error_report']['error_msg']);
            }
            if ($filingStatus['status_cd'] == 'PE') {
                return $this->sendError('GSTR 1 data uploaded with errors. Check the error details and fix them for a smooth filing.');
            }
            if ($filingStatus['status_cd'] == 'P') {
                $data = GenerateGstr1Summary::run($input);

                if ($data['success'] == true) {
                    $gstr1File->update([
                        'generate_summary' => true,
                    ]);
                }

                return $this->sendSuccess('GSTR 1 data uploaded successfully!', $data);
            }

            return $this->sendSuccess('GSTR 1 data reset successfully! Fresh start, here we go!');
        }

        return $this->sendError('No filed GSTR 1 found for this period.');
    }

    public function resetGstDataForPortal(Request $request)
    {
        $input = $request->all();
        $filingPeriod = ! empty($input['end_date']) ? Carbon::parse($input['end_date'])->format('mY') : null;
        $fileGst = $this->fileGstService;
        $gstr1File = GstFiling::where('return_period', $filingPeriod)->where('company_id', getCurrentCompany()->id)->first();
        $gstLogin = GstrLogin::latest()->first();
        $company = getCurrentCompany();
        $gstin = $company->companyTax->gstin;
        $statecd = substr($gstin, 0, 2);

        if (! empty($gstr1File)) {
            $headers = [
                'gstin' => $gstin,
                'username' => $gstLogin->username,
                'otp' => $gstLogin->otp,
                'statecd' => $statecd,
                'retperiod' => $filingPeriod,
            ];

            $filingStatus = $fileGst->gstr1DataReset($headers);

            Log::alert('Rest GST Data', [$filingStatus]);
            if (isset($filingStatus) && isset($filingStatus['errorCode']) && ($filingStatus['errorCode'] == 'RETOTPREQUEST')) {
                $message = 'OTP sent to your registered mobile/email with GST Portal';
                $gstLogin->update([
                    'status' => GstrLogin::DISCONNECTED,
                    'otp' => null,
                ]);
                $data = [
                    'showModal' => true,
                    'data' => $gstLogin,
                ];

                return $this->sendErrorResponse($data, $message);
            }
            if (isset($filingStatus) && isset($filingStatus['success']) && ($filingStatus['success'] == false)) {
                Log::channel('gstr1')->error('Reset GSTR1 Report', [
                    'company_id' => $company->id,
                    'error' => $filingStatus['message'],
                    'requestId' => $gstr1File->requestId,
                    'return_period' => $filingPeriod,
                ]);

                return $this->sendError($filingStatus['message']);
            }
            $gstr1File->delete();

            return $this->sendSuccess('GSTR 1 data reset successfully! Fresh start, here we go!');
        }

        return $this->sendError('No filed GSTR 1 found for this period.');
    }

    public function getGstr1ReportPreview(Request $request)
    {
        $startDate = $request->start_date;
        $endDate = $request->end_date;
        $filingPeriod = ! empty($endDate) ? Carbon::parse($endDate)->format('mY') : null;
        $company = getCurrentCompany();
        $gstin = $company->companyTax->gstin;
        $gstrLogin = GstrLogin::whereCompanyId($company->id)->first();
        $statecd = substr($gstin, 0, 2);
        $headers = [
            'username' => $gstrLogin->username,
            'otp' => $gstrLogin->otp,
            'gstin' => $gstin,
            'action' => 'B2B',
            'statecd' => $statecd,
            'retperiod' => $filingPeriod,
        ];
        $fileGst = $this->fileGstService;

        $data = [];
        $data['api'] = $fileGst->getGstR1Data($headers);

        if (! empty($gstrLogin)) {
            if (! empty($input['resend'])) {
                $data['api'] = $fileGst->getGstR1Data($headers);
                if ($data['api']['errorCode'] == 'RETOTPREQUEST') {
                    return response()->json([
                        'status' => false,
                        'message' => 'Invalid OTP. A new OTP has been sent to your registered mobile number or email.',
                    ]);
                }
                if (isset($data['api']) && isset($data['api']['errorCode']) && ($data['api']['errorCode'] == 'RETOTPREQUEST')) {
                    return response()->json([
                        'status' => true,
                        'message' => 'OTP sent to your registered mobile/email with GST Portal',
                    ]);
                }
            } else {
                $data['api'] = $fileGst->getGstR1Data($headers);
            }
        } else {
            return response()->json([
                'status' => false,
                'message' => 'Please login to GST portal first.',
            ]);
        }

        return response()->json([
            'status' => true,
            'data' => $data['api'],
        ]);
    }

    public function gstr1DetailedExcelExport(Request $request)
    {
        $input = $request->all();
        $company = getCurrentCompany();

        $filingPeriod = isset($input['end_date']) ? Carbon::parse($input['end_date'])->format('mY') : null;

        $gstr1File = GstFiling::where('return_period', $filingPeriod)
            ->where('company_id', $company->id)
            ->first();

        if (! $gstr1File) {
            return $this->sendError('No filed GSTR 1 found for this period.');
        }

        $gstLogin = GstrLogin::latest()->first();
        if (! $gstLogin) {
            return $this->sendError('No GST Login found.');
        }

        if (! $company->companyTax || ! $company->companyTax->gstin) {
            return $this->sendError('GSTIN not found for the current company.');
        }

        $gstin = $company->companyTax->gstin;
        $statecd = substr($gstin, 0, 2);

        $headers = [
            'gstin' => $gstin,
            'username' => $gstLogin->username,
            'otp' => $gstLogin->otp,
            'statecd' => $statecd,
            'retperiod' => $filingPeriod,
            'requestid' => $gstr1File->requestId,
        ];

        $fileGst = $this->fileGstService;
        $filingStatus = $fileGst->checkGstFilingStatus($headers);

        $response = Excel::download(new Gstr1ReportErrorExport($filingStatus), 'Error sheet.xlsx');

        ob_end_clean(); // Clean output buffer for file download

        return $response;
    }
}
