<?php

namespace App\Models;

use App\Actions\ItemMaster\GetItemClosingStock;
use App\Actions\Ledger\GetLedgerClosingBalanceAndType;
use App\Models\Master\Broker;
use App\Models\Master\Transport;
use App\Traits\HasCompany;
use App\Traits\HasJsonResourcefulData;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\Relations\MorphOne;
use Illuminate\Support\Arr;
use OwenIt\Auditing\Contracts\Auditable;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;

/**
 * App\Models\PurchaseOrderTransaction
 *
 * @property string|null $voucher_number
 * @property string|null $sale_number
 * @property Carbon $date_of_invoice
 * @property Carbon $voucher_date
 */
class PurchaseOrderTransaction extends Model implements Auditable, HasMedia
{
    use HasCompany, HasFactory, HasJsonResourcefulData, InteractsWithMedia;
    use \OwenIt\Auditing\Auditable;

    /**
     * Attributes to exclude from the Audit.
     *
     * @var array
     */
    protected $auditExclude = [
        'status',
        'deleted_by',
        'taxable_value',
    ];

    public $table = 'purchase_order_transactions';

    protected $fillable = [
        'company_id',
        'created_by',
        'title',
        'order_number',
        'full_order_number',
        'order_date',
        'valid_for',
        'valid_for_type',
        'valid_till_date',
        'party_ledger_id',
        'gstin',
        'broker_id',
        'brokerage',
        'brokerage_on_value_type',
        'shipping_name',
        'shipping_gstin',
        'address_name',
        'party_name_same_as_address_name',
        'transport_id',
        'transporter_document_number',
        'transporter_document_date',
        'transporter_vehicle_number',
        'order_type',
        'shipping_freight',
        'shipping_freight_with_gst',
        'shipping_freight_sgst_amount',
        'shipping_freight_cgst_amount',
        'shipping_freight_igst_amount',
        'packing_charge',
        'packing_charge_with_gst',
        'packing_charge_sgst_amount',
        'packing_charge_cgst_amount',
        'packing_charge_igst_amount',
        'cgst',
        'sgst',
        'igst',
        'cess',
        'tcs_rate',
        'tcs_amount',
        'round_off_amount',
        'total',
        'grand_total',
        'narration',
        'shipping_address_id',
        'same_as_billing',
        'taxable_value',
        'gross_value',
        'status',
        'tcs_tax_id',
        'is_gst_enabled',
        'is_gst_na',
        'is_cgst_sgst_igst_calculated',
        'via_api',
        'is_import',
        'is_round_off_not_changed',
        'created_at',
        'updated_at',
        'round_off_method',
    ];

    public $casts = [
        'party_name_same_as_address_name' => 'boolean',
        'same_as_billing' => 'boolean',
    ];

    public const PURCHASE_ORDER_INVOICE = 'purhase_order_invoice';

    public const TRANSACTION_TYPE = 'purchaseOrderTransaction';

    public const INVOICE_ATTACHMENT = 'invoice_attachment';

    public const SAVE_BUTTON = 1;

    public const SAVE_AND_NEW_BUTTON = 2;

    public const SAVE_AND_PRINT_BUTTON = 3;

    public const BILLING_ADDRESS = 1;

    public const SHIPPING_ADDRESS = 2;

    public const VALID_FOR_TYPE_MONTH = 1;

    public const VALID_FOR_TYPE_DAY = 2;

    public const CREDIT_PERIOD_TYPE_MONTH = 1;

    public const CREDIT_PERIOD_TYPE_DAY = 2;

    public const CREDIT_PERIOD_TYPE = [
        self::CREDIT_PERIOD_TYPE_DAY => 'Days',
        self::CREDIT_PERIOD_TYPE_MONTH => 'Month',
    ];

    public const ACCOUNTING_INVOICE = 1;

    public const ITEM_INVOICE = 2;

    public const INVOICE_TYPE = [
        self::ACCOUNTING_INVOICE => 'Accounting Invoice',
        self::ITEM_INVOICE => 'Item Invoice',
    ];

    public const OPEN = 'Open';

    public const EXPIRED = 'Expired';

    public const FULLY_INVOICED = 'Fully Invoiced';

    public const PARTIALLY_INVOICED = 'Partially Invoiced';

    public const STATUS_ARR = [
        self::OPEN => self::OPEN,
        self::EXPIRED => self::EXPIRED,
        self::FULLY_INVOICED => self::FULLY_INVOICED,
        self::PARTIALLY_INVOICED => self::PARTIALLY_INVOICED,
    ];

    public const BROKERAGE_ON_INVOICE_VALUE = 1;

    public const BROKERAGE_ON_TAXABLE_VALUE = 2;

    public const BROKERAGE_ON_GROSS_VALUE = 3;

    public const BROKERAGE_ON_VALUE_TYPE = [
        self::BROKERAGE_ON_INVOICE_VALUE => 'Invoice Value',
        self::BROKERAGE_ON_TAXABLE_VALUE => 'Taxable Value',
        self::BROKERAGE_ON_GROSS_VALUE => 'Gross Value',
    ];

    public const DISCOUNT_TYPE_AMOUNT = 1;

    public const DISCOUNT_TYPE_PERCENTAGE = 2;

    public const DISCOUNT_TYPES = [
        self::DISCOUNT_TYPE_AMOUNT => '₹',
        self::DISCOUNT_TYPE_PERCENTAGE => '%',
    ];

    public function transformAudit(array $data): array
    {
        if ($data['event'] == 'created') {
            Arr::set($data, 'title', '<b>Purchase Order</b> Invoice <b>'.$this->getAttribute('full_order_number').'</b> was created.');
        } elseif ($data['event'] == 'updated') {
            Arr::set($data, 'title', '<b>Purchase Order</b> Invoice <b>'.$this->getAttribute('full_order_number').'</b> was edited.');
        } elseif ($data['event'] == 'deleted') {
            Arr::set($data, 'title', '<b>Purchase Order</b> Invoice <b>'.$this->getAttribute('full_order_number').'</b> was deleted.');
        } elseif ($data['event'] == 'restored') {
            Arr::set($data, 'title', '<b>Purchase Order</b> Invoice <b>'.$this->getAttribute('full_order_number').'</b> was restored from recycle bin.');
        }

        return $data;
    }

    public function scopeFinancialYearDate(Builder $query)
    {
        $getCurrentFinancialYearDetails = getCurrentFinancialYearDetails();
        $query->whereBetween('order_date', [$getCurrentFinancialYearDetails['yearStartDate'], $getCurrentFinancialYearDetails['yearEndDate']]);
    }


    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class, 'company_id');
    }


    public function party(): BelongsTo
    {
        return $this->belongsTo(Ledger::class, 'party_ledger_id', 'id');
    }

    public function addresses(): MorphMany
    {
        return $this->morphMany(Address::class, 'model');
    }

    public function transactionItems(): HasMany
    {
        return $this->hasMany(PurchaseOrderItemInvoice::class, 'transactions_id');
    }

    public function transactionLedgers(): HasMany
    {
        return $this->hasMany(PurchaseOrderAccountingInvoice::class, 'transactions_id');
    }

    public function brokerDetails(): BelongsTo
    {
        return $this->belongsTo(Broker::class, 'broker_id', 'id');
    }

    public function transportDetails(): BelongsTo
    {
        return $this->belongsTo(Transport::class, 'transport_id', 'id');
    }

    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by', 'id');
    }

    public function billingAddress(): MorphOne
    {
        return $this->morphOne(Address::class, 'model')->where('address_type', self::BILLING_ADDRESS);
    }

    public function shippingAddress(): MorphOne
    {
        return $this->morphOne(Address::class, 'model')->where('address_type', self::SHIPPING_ADDRESS);
    }

    public function transactionTitle(): HasOne
    {
        return $this->hasOne(PurchaseOrderTitle::class, 'id', 'title');
    }

    public function additionalCharges(): HasMany
    {
        return $this->hasMany(AdditionalChargesForPurchaseOrderTransaction::class, 'purchase_order_id');
    }

    public function addLess(): HasMany
    {
        return $this->hasMany(AddLessForPurchaseOrderTransaction::class, 'purchase_order_id');
    }

    public function customFieldValues(): MorphMany
    {
        return $this->morphMany(TransactionCustomFieldValue::class, 'model');
    }

    public function getPurchaseOrderFileAttribute(): array|string
    {

        $mediaUrl = [];
        /** @var Media $medias */
        $medias = $this->getMedia(self::PURCHASE_ORDER_INVOICE);

        foreach ($medias as $media) {
            $mediaUrl[] = $media->getFullUrl();
        }

        if (! empty($mediaUrl)) {
            return $mediaUrl;
        }

        return '';
    }

    public function getInvoiceAttachmentAttribute(): array|string
    {
        $mediaUrl = [];
        /** @var Media $medias */
        $medias = $this->getMedia(self::INVOICE_ATTACHMENT);

        foreach ($medias as $media) {
            $mediaUrl[] = $media->getFullUrl();
        }

        if (! empty($mediaUrl)) {
            return $mediaUrl;
        }

        return '';
    }

    public function purchaseTransactions(): HasMany
    {
        return $this->hasMany(PurchaseTransaction::class, 'purchase_order_no', 'id');
    }

    public function getPrimaryPhone()
    {
        $phone = null;
        if (isset($this->party->model->phone_1)) {
            $phone = $this->party->model->region_code_1.$this->party->model->phone_1;
        } elseif (isset($this->party->model->phone_2)) {
            $phone = $this->party->model->region_code_2.$this->party->model->phone_2;
        }

        return $phone;
    }

    public function prepareData()
    {
        return [
            'transaction_id' => $this->id,
            'party_name' => $this->party->name,
            'party_id' => $this->party_ledger_id,
            'phone_no' => $this->getPrimaryPhone(),
            'invoice_number' => $this->full_order_number,
            'date' => Carbon::parse($this->order_date)->format('d-m-Y'),
            'invoice_amount' => $this->grand_total,
            'valid_for' => $this->valid_for,
            'valid_for_type' => $this->valid_for_type ? $this::CREDIT_PERIOD_TYPE[$this->valid_for_type] : null,
            'status' => $this->status,
            'transaction_type' => 'Purchase Order',
        ];
    }

    public function getStatus(): Attribute
    {
        $purchaseTransactions = PurchaseTransaction::with(['purchaseTransactionItems', 'purchaseTransactionLedger'])->whereRaw('FIND_IN_SET(?, purchase_order_no)', [$this->id])->get();
        if ($this->order_type == PurchaseOrderTransaction::ACCOUNTING_INVOICE) {
            $billedAmount = getAccountingInvoiceTotalCountForPurchaseOrder($purchaseTransactions, 'purchase');
            $totalAmount = getAccountingInvoiceTotalCountForPurchaseOrder($this->transactionLedgers ?? [], 'purchase-order');
            $pendingAmount = ($totalAmount - $billedAmount);
            $pendingAmount = $pendingAmount > 0 ? $pendingAmount : 0;

            if ($billedAmount == 0) {
                $status = PurchaseOrderTransaction::OPEN;
            } elseif ($pendingAmount != 0) {
                $status = PurchaseOrderTransaction::PARTIALLY_INVOICED;
            } else {
                $status = PurchaseOrderTransaction::FULLY_INVOICED;
            }
        } else {
            $billedQuantity = getQuantityCountForPurchaseOrder($purchaseTransactions, 'purchase');
            $totalQuantity = getQuantityCountForPurchaseOrder($this->transactionItems ?? [], 'purchase-order');
            $pendingQuantity = ($totalQuantity - $billedQuantity);
            $pendingQuantity = $pendingQuantity > 0 ? $pendingQuantity : 0;

            if ($billedQuantity == 0) {
                $status = PurchaseOrderTransaction::OPEN;
            } elseif ($pendingQuantity != 0) {
                $status = PurchaseOrderTransaction::PARTIALLY_INVOICED;
            } else {
                $status = PurchaseOrderTransaction::FULLY_INVOICED;
            }
        }

        if (! empty($this->valid_for)) {
            if ($this->valid_for_type == PurchaseOrderTransaction::CREDIT_PERIOD_TYPE_DAY) {
                $validTillTime = Carbon::parse($this->order_date)->addDays($this->valid_for)->endOfDay();
            } else {
                $validTillTime = Carbon::parse($this->order_date)->addMonths($this->valid_for)->endOfDay();
            }
            if ($validTillTime < Carbon::now()) {
                $status = PurchaseOrderTransaction::EXPIRED;
            }
        }

        return Attribute::make(
            get: static fn ($value) => $status,
        );
    }

    public function prepareShippingAmount(): array
    {
        $prepareData = [];
        $prepareNewData = [];

        if ($this->shipping_freight != 0 && empty($this->shipping_freight)) {
            return $prepareNewData;
        }

        if ($this->order_type == self::ITEM_INVOICE) {

            $transactionItems = $this->transactionItems;
            $totalTaxableAmount = $this->transactionItems->sum('total') ?? 0;

            if ($totalTaxableAmount == 0) {
                return $prepareNewData;
            }

            if ($transactionItems->count() >= 1) {
                foreach ($transactionItems as $purchaseItem) {
                    $shippingAmount = ($totalTaxableAmount != 0) ? ($purchaseItem->total * $this->shipping_freight) / $totalTaxableAmount : 0;
                    $prepareData[] = [
                        'gst_tax' => $purchaseItem->gst_tax_percentage ?? 0,
                        'shipping_amount' => $shippingAmount,
                        'shipping_tax_amount' => ($shippingAmount * 18) / 100,
                    ];
                }
            }
        } else {
            $transactionLedgers = $this->transactionLedgers;
            $totalTaxableAmount = $this->transactionLedgers->sum('total') ?? 0;
            if ($totalTaxableAmount == 0) {

                return $prepareNewData;
            }

            if ($transactionLedgers->count() >= 1) {
                foreach ($transactionLedgers as $purchaseLedger) {

                    $shippingAmount = ($totalTaxableAmount != 0) ? ($purchaseLedger->total * $this->shipping_freight) / $totalTaxableAmount : 0;
                    $prepareData[] = [
                        'gst_tax' => $purchaseLedger->gst_tax_percentage ?? 0,
                        'shipping_amount' => $shippingAmount,
                        'shipping_tax_amount' => ($shippingAmount * 18) / 100,
                    ];
                }
            }
        }

        $gstTaxArray = [];
        $totalShippingAmount = 0;
        $totalShippingTaxAmount = 0;
        foreach ($prepareData as $data) {
            if (in_array($data['gst_tax'], $gstTaxArray)) {
                foreach ($prepareData as $checkGstTax) {
                    if ($checkGstTax['gst_tax'] == $data['gst_tax']) {
                        $totalShippingAmount += $checkGstTax['shipping_amount'];
                        $totalShippingTaxAmount += $checkGstTax['shipping_tax_amount'];
                    }
                }
                $prepareNewData[$data['gst_tax']] = [
                    'party_name' => $this->party->name,
                    'ledger_id' => $this->party_ledger_id,
                    'gstin' => $this->gstin ?? '',
                    'voucher_number' => $this->voucher_number,
                    'invoice_number' => $this->sale_number,
                    'voucher_date' => Carbon::parse($this->voucher_date)->format('d-m-Y'),
                    'invoice_date' => Carbon::parse($this->date_of_invoice)->format('d-m-Y'),
                    'transaction_type' => 'Purchase',
                    'ledger_name' => 'Shipping Charge',
                    'item_name' => 'Shipping Charge',
                    'quantity' => 0,
                    'unit_of_measurement' => '',
                    'rate_per_unit' => 0,
                    'hsn_code' => '996812', // Fix HSN code for shipping and freight after discussion with Gopal bhai change it here
                    // 'hsn_code' => ! empty($this->transactionItems->first()->items->model->hsn_sac_code) ? $this->transactionItems->first()->items->model->hsn_sac_code : '',
                    'taxable_value' => $totalShippingAmount,
                    'rate_of_gst' => 18,
                    'gst_rate' => $data['gst_tax'],
                    'cgst' => (! empty($this->cgst) && $this->cgst != 0) ? ($totalShippingTaxAmount / 2) : '0',
                    'sgst' => (! empty($this->cgst) && $this->sgst != 0) ? ($totalShippingTaxAmount / 2) : '0',
                    'igst' => (! empty($this->igst) && $this->igst != 0) ? ($totalShippingTaxAmount) : '0',
                    'cess_amount' => ! empty($this->cess_amount) ? $this->cess_amount : '0',
                    'itc' => $this->transactionItems->first()->classification_is_itc_applicable ?? 0,
                    'rcm_yes_or_no' => $this->transactionItems->first()->classification_is_rcm_applicable ?? 0,
                    'invoice_amount' => $this->grand_total,
                    'invoice_type' => PurchaseTransaction::PURCHASES,
                ];
                $totalShippingAmount = 0;
                $totalShippingTaxAmount = 0;
            } else {
                $gstTaxArray[] = $data['gst_tax'];
                $prepareNewData[$data['gst_tax']] = [
                    'party_name' => $this->party->name,
                    'ledger_id' => $this->party_ledger_id,
                    'gstin' => $this->gstin ?? '',
                    'voucher_number' => $this->order_number,
                    'invoice_number' => $this->sale_number,
                    'voucher_date' => Carbon::parse($this->order_date)->format('d-m-Y'),
                    'invoice_date' => Carbon::parse($this->date_of_invoice)->format('d-m-Y'),
                    'transaction_type' => 'Purchase',
                    'ledger_name' => 'Shipping Charge',
                    'item_name' => 'Shipping Charge',
                    'quantity' => 0,
                    'unit_of_measurement' => '',
                    'rate_per_unit' => 0,
                    'hsn_code' => '996812', // Fix HSN code for shipping and freight after discussion with Gopal bhai change it here
                    // 'hsn_code' => ! empty($this->transactionItems->first()->items->model->hsn_sac_code) ? $this->transactionItems->first()->items->model->hsn_sac_code : '',
                    'taxable_value' => $data['shipping_amount'],
                    'rate_of_gst' => 18,
                    'gst_rate' => $data['gst_tax'],
                    'cgst' => (! empty($this->cgst) || $this->cgst != 0) ? ($data['shipping_tax_amount'] / 2) : '0',
                    'sgst' => (! empty($this->cgst) || $this->sgst != 0) ? ($data['shipping_tax_amount'] / 2) : '0',
                    'igst' => (! empty($this->igst) || $this->igst != 0) ? ($data['shipping_tax_amount']) : '0',
                    'cess_amount' => ! empty($this->cess_amount) ? $this->cess_amount : '0',
                    'itc' => $this->transactionItems->first()->classification_is_itc_applicable ?? 0,
                    'rcm_yes_or_no' => $this->transactionItems->first()->classification_is_rcm_applicable ?? 0,
                    'invoice_amount' => $this->grand_total,
                    'invoice_type' => PurchaseTransaction::PURCHASES,
                ];
            }
        }

        return $prepareNewData;
    }

    public function preparePackingAmount()
    {
        $prepareData = [];
        $prepareNewData = [];
        if ($this->packing_charge != 0 && empty($this->packing_charge)) {
            return $prepareNewData;
        }

        if ($this->order_type == self::ITEM_INVOICE) {
            $transactionItems = $this->transactionItems;
            $totalTaxableAmount = $this->transactionItems->sum('total') ?? 0;
            if ($totalTaxableAmount == 0) {
                return $prepareNewData;
            }

            if ($transactionItems->count() >= 1) {
                foreach ($transactionItems as $purchaseItem) {
                    $PackingAmount = ($totalTaxableAmount != 0) ? ($purchaseItem->total * $this->packing_charge) / $totalTaxableAmount : 0;
                    $prepareData[] = [
                        'gst_tax' => $purchaseItem->gst_tax_percentage ?? 0,
                        'packing_amount' => $PackingAmount,
                        'packing_tax_amount' => ($PackingAmount * 18) / 100,
                    ];
                }
            }
        } else {
            $transactionLedger = $this->transactionLedgers;
            $totalTaxableAmount = $this->transactionLedgers->sum('total') ?? 0;
            if ($totalTaxableAmount == 0) {
                return $prepareNewData;
            }

            if ($transactionLedger->count() >= 1) {
                foreach ($transactionLedger as $purchaseLedger) {
                    $PackingAmount = ($totalTaxableAmount != 0) ? ($purchaseLedger->total * $this->packing_charge) / $totalTaxableAmount : 0;
                    $prepareData[] = [
                        'gst_tax' => $purchaseLedger->gst_tax_percentage ?? 0,
                        'packing_amount' => $PackingAmount,
                        'packing_tax_amount' => ($PackingAmount * 18) / 100,
                    ];
                }
            }
        }

        $gstTaxArray = [];
        $totalPackingAmount = 0;
        $totalPackingTaxAmount = 0;
        foreach ($prepareData as $data) {
            if (in_array($data['gst_tax'], $gstTaxArray)) {
                foreach ($prepareData as $checkGstTax) {
                    if ($checkGstTax['gst_tax'] == $data['gst_tax']) {
                        $totalPackingAmount += $checkGstTax['packing_amount'];
                        $totalPackingTaxAmount += $checkGstTax['packing_tax_amount'];
                    }
                }
                $prepareNewData[$data['gst_tax']] = [
                    'party_name' => $this->party->name,
                    'ledger_id' => $this->party_ledger_id,
                    'gstin' => $this->gstin ?? '',
                    'voucher_number' => $this->order_number,
                    'invoice_number' => $this->sale_number,
                    'voucher_date' => Carbon::parse($this->order_date)->format('d-m-Y'),
                    'invoice_date' => Carbon::parse($this->order_date)->format('d-m-Y'),
                    'transaction_type' => 'Purchase',
                    'ledger_name' => 'Packing Charge',
                    'item_name' => 'Packing Charge',
                    'quantity' => 0,
                    'unit_of_measurement' => '',
                    'rate_per_unit' => 0,
                    'hsn_code' => '998541', // Fix HSN code for packing charge after discussion with Gopal bhai change it here
                    // 'hsn_code' => ! empty($this->transactionItems->first()->items->model->hsn_sac_code) ? $this->transactionItems->first()->items->model->hsn_sac_code : '',
                    'taxable_value' => $totalPackingAmount,
                    'rate_of_gst' => 18,
                    'gst_rate' => $data['gst_tax'],
                    'cgst' => (! empty($this->cgst) && $this->cgst != 0) ? ($totalPackingTaxAmount / 2) : '0',
                    'sgst' => (! empty($this->cgst) && $this->sgst != 0) ? ($totalPackingTaxAmount / 2) : '0',
                    'igst' => (! empty($this->igst) && $this->igst != 0) ? ($totalPackingTaxAmount) : '0',
                    'cess_amount' => ! empty($this->cess_amount) ? $this->cess_amount : '0',
                    'itc' => $this->transactionItems->first()->classification_is_itc_applicable ?? 0,
                    'rcm_yes_or_no' => $this->transactionItems->first()->classification_is_rcm_applicable ?? 0,
                    'invoice_amount' => $this->grand_total,
                    'invoice_type' => PurchaseTransaction::PURCHASES,
                ];
                $totalPackingAmount = 0;
                $totalPackingTaxAmount = 0;
            } else {
                $gstTaxArray[] = $data['gst_tax'];
                $prepareNewData[$data['gst_tax']] = [
                    'party_name' => $this->party->name,
                    'ledger_id' => $this->party_ledger_id,
                    'gstin' => $this->gstin ?? '',
                    'voucher_number' => $this->order_number,
                    'invoice_number' => $this->sale_number,
                    'voucher_date' => Carbon::parse($this->order_date)->format('d-m-Y'),
                    'invoice_date' => Carbon::parse($this->order_date)->format('d-m-Y'),
                    'transaction_type' => 'Purchase',
                    'ledger_name' => 'Packing Charge',
                    'item_name' => 'Packing Charge',
                    'quantity' => 0,
                    'unit_of_measurement' => '',
                    'rate_per_unit' => 0,
                    'hsn_code' => '998541', // Fix HSN code for packing charge after discussion with Gopal bhai change it here
                    // 'hsn_code' => ! empty($this->transactionItems->first()->items->model->hsn_sac_code) ? $this->transactionItems->first()->items->model->hsn_sac_code : '',
                    'taxable_value' => $data['packing_amount'],
                    'rate_of_gst' => 18,
                    'gst_rate' => $data['gst_tax'],
                    'cgst' => (! empty($this->cgst) || $this->cgst != 0) ? ($data['packing_tax_amount'] / 2) : '0',
                    'sgst' => (! empty($this->cgst) || $this->sgst != 0) ? ($data['packing_tax_amount'] / 2) : '0',
                    'igst' => (! empty($this->igst) || $this->igst != 0) ? ($data['packing_tax_amount']) : '0',
                    'cess_amount' => ! empty($this->cess_amount) ? $this->cess_amount : '0',
                    'itc' => $this->transactionItems->first()->classification_is_itc_applicable ?? 0,
                    'rcm_yes_or_no' => $this->transactionItems->first()->classification_is_rcm_applicable ?? 0,
                    'invoice_amount' => $this->grand_total,
                    'invoice_type' => PurchaseTransaction::PURCHASES,
                ];
            }
        }

        return $prepareNewData;
    }

    public function scopeSorting($query, $sorting)
    {
        $sorting = getSortingValues($sorting);
        foreach ($sorting as $key => $value) {
            $query->orderBy($key, $value);
        }

        return $query;
    }

    public function prepareAttributes()
    {
        $data = [];
        $data = [
            'id' => $this->id,
            'party_name' => $this->party->name,
            'grand_total' => $this->grand_total,
            'invoice_number' => $this->full_order_number,
            'status' => $this->status,
            'phone_no' => $this->getPrimaryPhone(),
            'date' => Carbon::parse($this->order_date)->format('d-m-Y'),
            'taxable_value' => $this->taxable_value,
            'cgst' => $this->cgst,
            'sgst' => $this->sgst,
            'igst' => $this->igst,
            'rounding_amount' => $this->round_off_amount,
            'total' => $this->total,
            'shipping_freight' => $this->shipping_freight,
            'shipping_freight_with_gst' => $this->shipping_freight_with_gst,
            'packing_charge' => $this->packing_charge,
            'packing_charge_with_gst' => $this->packing_charge_with_gst,
        ];

        if ($this->order_type == self::ITEM_INVOICE) {
            $transactionItems = $this->transactionItems;
            foreach ($transactionItems as $item) {
                $data['items'][] = [
                    'id' => $item->id,
                    'item_name' => $item->items->item_name,
                    'ledger_name' => $item->ledgers?->name,
                    'quantity' => $item->quantity,
                    'rpu_without_gst' => $item->rpu_without_gst,
                    'gst_tax_percentage' => $item->gst_tax_percentage,
                    'total_discount_amount' => $item->total_discount_amount,
                    'rpu_with_gst' => $item->rpu_with_gst,
                    'cgst' => $item->classification_cgst_tax ?? 0,
                    'sgst' => $item->classification_sgst_tax ?? 0,
                    'igst' => $item->classification_igst_tax ?? 0,
                    'total' => $item->total,
                ];
            }
        } else {
            $transactionLedgers = $this->transactionLedgers;
            foreach ($transactionLedgers as $ledger) {
                $data['ledgers'][] = [
                    'id' => $ledger->id,
                    'ledger_name' => $ledger->ledgers->name,
                    'rpu_with_gst' => $ledger->rpu_with_gst,
                    'rpu_without_gst' => $ledger->rpu_without_gst,
                    'total_discount_amount' => $ledger->total_discount_amount,
                    'gst_tax_percentage' => $ledger->gst_tax_percentage,
                    'cgst' => $ledger->classification_cgst_tax ?? 0,
                    'sgst' => $ledger->classification_sgst_tax ?? 0,
                    'igst' => $ledger->classification_igst_tax ?? 0,
                ];
            }
        }

        return $data;
    }

    public function editPrepareData()
    {
        $billingAddress = $this->billingAddress;
        $shippingsAddress = $this->shippingAddress;
        $closingBalance = GetLedgerClosingBalanceAndType::run($this->party_ledger_id);
        $media = $this->media;
        $result = [];

        /** @var Media $attachment */
        foreach ($media as $key => $attachment) {
            $url = $attachment->getFullUrl();
            $id = $attachment->id;
            $result[] = ['id' => $id, 'url' => $url];
        }
        $data = [
            'id' => $this->id,
            'customer_ledger_id' => $this->party_ledger_id,
            'is_gst_enabled' => $this->is_gst_enabled,
            'title' => $this->title,
            'full_invoice_number' => $this->full_order_number,
            'document_number' => $this->order_number,
            'document_date' => Carbon::parse($this->order_date)->format('d-m-Y'),
            'party_name' => $this->party->name,
            'customer_closing_bal' => $closingBalance['closing_bal'],
            'customer_closing_bal_type' => $closingBalance['closing_bal_type'],
            'gstin' => $this->gstin,
            'valid_for' => $this->valid_for,
            'valid_for_type' => $this->valid_for_type,
            'valid_till_date' => $this->valid_till_date,
            'billing_address' => [
                'address_1' => $billingAddress['address_1'],
                'address_2' => $billingAddress['address_2'],
                'country_id' => $billingAddress['country_id'],
                'state_id' => $billingAddress['state_id'],
                'city_id' => $billingAddress['city_id'],
                'pin_code' => $billingAddress['pin_code'],
            ],
            'shippings_address' => [
                'address_1' => $shippingsAddress['address_1'],
                'address_2' => $shippingsAddress['address_2'],
                'country_id' => $shippingsAddress['country_id'],
                'state_id' => $shippingsAddress['state_id'],
                'city_id' => $shippingsAddress['city_id'],
                'pin_code' => $shippingsAddress['pin_code'],
            ],
            'broker_detail' => [
                'broker_id' => $this->broker_id ?? null,
                'broker_name' => $this->brokerDetails->broker_name ?? null,
                'brokerage_for_sale' => $this->brokerage ?? null,
                'brokerage_on_value_type' => $this->brokerage_on_value_type ?? null,
            ],
            'transport_details' => [
                'transport_id' => $this->transport_id,
                'transport_name' => $this->transportDetails?->transporter_name,
                'transporter_document_number' => $this->transporter_document_number,
                'transporter_document_date' => ! empty($this->transporter_document_date) ? Carbon::parse($this->transporter_document_date)->format('d-m-Y') : null,
                'po_no' => $this->po_no ?? null,
                'po_date' => ! empty($this->po_date) ? Carbon::parse($this->po_date)->format('d-m-Y') : null,
            ],
            'credit_period' => $this->credit_period ?? null,
            'credit_period_type' => $this->credit_period_type ?? null,
            'invoice_type' => $this->order_type,
            'is_gst_na' => $this->is_gst_na,
            'shipping_freight' => $this->shipping_freight,
            'packing_charge' => $this->packing_charge,
            'cess' => $this->cess,
            'cgst' => $this->cgst,
            'sgst' => $this->sgst,
            'igst' => $this->igst,
            'shipping_freight_with_gst' => $this->shipping_freight_with_gst,
            'packing_charge_with_gst' => $this->packing_charge_with_gst,
            'shipping_freight_sgst_amount' => $this->shipping_freight_sgst_amount,
            'shipping_freight_cgst_amount' => $this->shipping_freight_cgst_amount,
            'shipping_freight_igst_amount' => $this->shipping_freight_igst_amount,
            'packing_charge_sgst_amount' => $this->packing_charge_sgst_amount,
            'packing_charge_cgst_amount' => $this->packing_charge_cgst_amount,
            'packing_charge_igst_amount' => $this->packing_charge_igst_amount,
            'round_off_amount' => $this->round_off_amount,
            'is_cgst_sgst_igst_calculated' => $this->is_cgst_sgst_igst_calculated,
            'total' => $this->total,
            'grand_total' => $this->grand_total,
            'narration' => $this->narration,
            'media' => $result,
        ];

        if ($this->order_type == self::ITEM_INVOICE) {
            $items = $this->transactionItems;
            foreach ($items as $item) {

                $currentBalance = GetLedgerClosingBalanceAndType::run($item->ledgers->id);
                $currentStock = GetItemClosingStock::run($item->items->id);

                $data['items'][] = [
                    'item_id' => $item->items->id,
                    'item_name' => $item->items->item_name,
                    'current_stock' => $currentStock['opening_balance_qty'],
                    'unit_id' => $item->unit->id,
                    'unit_name' => $item->unit->name,
                    'quantity' => $item->quantity,
                    'rpu_without_gst' => $item->rpu_without_gst,
                    'gst_id' => $item->gst_id,
                    'gst_tax_percentage' => $item->gst_tax_percentage,
                    'gst_total' => $item->total,
                    'mrp' => $item->mrp ?? null,
                    'discount_type' => $item->discount_type ?? null,
                    'discount_value' => $item->discount_value ?? null,
                    'total_discount_amount' => $item->total_discount_amount,
                    'rpu_with_gst' => $item->rpu_with_gst,
                    'is_additional_item_description' => $item->is_additional_item_description ?? false,
                    'additional_description' => $item->additional_description,
                    'classification_nature_type' => $item->classification_nature_type ?? null,
                    'classification_is_rcm_applicable' => $item->classification_is_rcm_applicable ?? false,
                    'classification_igst_tax' => $item->classification_igst_tax ?? null,
                    'classification_cgst_tax' => $item->classification_cgst_tax ?? null,
                    'classification_sgst_tax' => $item->classification_sgst_tax ?? null,
                    'classification_cess_tax' => $item->classification_cess_tax ?? null,
                    'consolidating_items_to_invoice' => $item->consolidating_items_to_invoice ?? null,
                    'cess_rate' => $item->cess_rate ?? null,
                    'cess_amount' => $item->cess_amount ?? null,
                    'taxable_amount' => $item->taxable_amount ?? null,
                    'ledger_id' => $item->ledgers->id,
                    'ledger_name' => $item->ledgers->name,
                    // 'closing_bal' => $currentBalance['closing_bal'],
                    // 'closing_bal_type' => $currentBalance['closing_bal_type'],
                    'total' => $item->total,
                ];
            }
        } else {
            $saleLedgers = $this->transactionLedgers;
            foreach ($saleLedgers as $ledger) {
                $currentBalance = GetLedgerClosingBalanceAndType::run($ledger->ledgers->id);
                $data['ledgers'][] = [
                    'id' => $ledger->id,
                    'ledger_name' => $ledger->ledgers->name,
                    'closing_bal' => $currentBalance['closing_bal'],
                    'closing_bal_type' => $currentBalance['closing_bal_type'],
                    'rpu_with_gst' => $ledger->rpu_with_gst,
                    'rpu_without_gst' => $ledger->rpu_without_gst,
                    'discount_type' => $ledger->discount_type ?? null,
                    'discount_value' => $ledger->discount_value ?? null,
                    'total_discount_amount' => $ledger->total_discount_amount,
                    'gst_id' => $ledger->gst_id,
                    'gst_tax_percentage' => $ledger->gst_tax_percentage,
                    'is_additional_ledger_description' => $ledger->is_additional_ledger_description ?? false,
                    'additional_description' => $ledger->additional_description,
                    'classification_nature_type' => $ledger->classification_nature_type ?? null,
                    'classification_is_rcm_applicable' => $ledger->classification_is_rcm_applicable ?? false,
                    'classification_igst_tax' => $ledger->classification_igst_tax ?? null,
                    'classification_cgst_tax' => $ledger->classification_cgst_tax ?? null,
                    'classification_sgst_tax' => $ledger->classification_sgst_tax ?? null,
                    'classification_cess_tax' => $ledger->classification_cess_tax ?? null,
                    'total' => $ledger->total,
                ];
            }
        }

        return $data;
    }
}
