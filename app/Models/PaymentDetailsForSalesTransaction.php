<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class PaymentDetailsForSalesTransaction extends Model
{
    use HasFactory;

    public $fillable = [
        'sale_id',
        'ledger_id',
        'date',
        'amount',
        'mode',
        'reference_no',
        'receipt_id',
    ];


    public function ledger(): BelongsTo
    {
        return $this->belongsTo(Ledger::class);
    }


    public function sale(): BelongsTo
    {
        return $this->belongsTo(SaleTransaction::class);
    }
}
