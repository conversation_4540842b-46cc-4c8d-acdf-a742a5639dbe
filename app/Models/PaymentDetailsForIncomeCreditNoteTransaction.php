<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class PaymentDetailsForIncomeCreditNoteTransaction extends Model
{
    use HasFactory;

    public $table = 'payment_details_for_income_credit_note_transactions';

    public $fillable = [
        'income_cn_id',
        'ledger_id',
        'date',
        'amount',
        'mode',
        'reference_no',
        'payment_id',
    ];


    public function ledger(): BelongsTo
    {
        return $this->belongsTo(Ledger::class);
    }

    public function incomeCreditNote(): BelongsTo
    {
        return $this->belongsTo(IncomeCreditNoteTransaction::class, 'income_cn_id', 'id');
    }
}
