<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class PaymentDetailsForPurchaseTransaction extends Model
{
    use HasFactory;

    public $table = 'payment_details_for_purchase_transactions';

    public $fillable = [
        'purchase_transaction_id',
        'ledger_id',
        'date',
        'amount',
        'mode',
        'reference_no',
        'payment_id',
    ];


    public function ledger(): BelongsTo
    {
        return $this->belongsTo(Ledger::class);
    }


    public function purchase(): BelongsTo
    {
        return $this->belongsTo(PurchaseTransaction::class, 'purchase_transaction_id', 'id');
    }
}
