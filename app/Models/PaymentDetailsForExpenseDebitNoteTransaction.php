<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class PaymentDetailsForExpenseDebitNoteTransaction extends Model
{
    use HasFactory;

    public $table = 'payment_details_for_expense_debit_note_transactions';

    public $fillable = [
        'expense_debit_note_id',
        'ledger_id',
        'date',
        'amount',
        'mode',
        'reference_no',
        'receipt_id',
    ];


    public function ledger(): BelongsTo
    {
        return $this->belongsTo(Ledger::class);
    }


    public function expenseDebitNote(): BelongsTo
    {
        return $this->belongsTo(ExpenseDebitNoteTransaction::class);
    }
}
