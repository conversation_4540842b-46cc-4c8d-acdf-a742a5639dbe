<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class PaymentDetailsForIncomeDebitNoteTransaction extends Model
{
    use HasFactory;

    protected $table = 'payment_details_for_income_debit_note_transactions';

    public $fillable = [
        'income_debit_note_id',
        'ledger_id',
        'date',
        'amount',
        'mode',
        'reference_no',
        'receipt_id',
    ];


    public function ledger(): BelongsTo
    {
        return $this->belongsTo(Ledger::class);
    }


    public function incomeDebitNote(): BelongsTo
    {
        return $this->belongsTo(IncomeDebitNoteTransaction::class);
    }
}
