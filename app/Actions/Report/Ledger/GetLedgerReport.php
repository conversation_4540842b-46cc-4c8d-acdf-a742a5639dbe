<?php

namespace App\Actions\Report\Ledger;

use App\Models\ExpenseCreditNoteItemTransaction;
use App\Models\ExpenseCreditNoteLedgerTransaction;
use App\Models\ExpenseDebitNoteItemTransaction;
use App\Models\ExpenseDebitNoteLedgerTransaction;
use App\Models\IncomeCreditNoteItemTransaction;
use App\Models\IncomeCreditNoteLedgerTransaction;
use App\Models\IncomeDebitNoteItemTransaction;
use App\Models\IncomeDebitNoteLedgerTransaction;
use App\Models\PurchaseItemTransaction;
use App\Models\PurchaseLedgerTransaction;
use App\Models\PurchaseReturnItemTransaction;
use App\Models\PurchaseReturnLedgerTransaction;
use App\Models\SaleReturnItemTransaction;
use App\Models\SaleReturnLedgerTransaction;
use App\Models\SaleTransactionItem;
use App\Models\SaleTransactionLedger;
use Lorisleiva\Actions\Concerns\AsAction;

class GetLedgerReport
{
    use AsAction;

    public function handle($transaction, $transactionType)
    {
        switch ($transactionType) {
            case SaleTransactionItem::class:
                return $this->saleItem($transaction);
                break;

            case SaleTransactionLedger::class:
                return $this->saleLedger($transaction);
                break;

            case SaleReturnItemTransaction::class:
                return $this->saleReturnItem($transaction);
                break;

            case SaleReturnLedgerTransaction::class:
                return $this->saleReturnLedger($transaction);
                break;

            case IncomeCreditNoteItemTransaction::class:
                return $this->incomeCreditNoteItem($transaction);
                break;

            case IncomeCreditNoteLedgerTransaction::class:
                return $this->incomeCreditNoteLedger($transaction);
                break;

            case IncomeDebitNoteItemTransaction::class:
                return $this->incomeDebitNoteItem($transaction);
                break;

            case IncomeDebitNoteLedgerTransaction::class:
                return $this->incomeDebitNoteLedger($transaction);
                break;

            case PurchaseItemTransaction::class:
                return $this->purchaseItem($transaction);
                break;

            case PurchaseLedgerTransaction::class:
                return $this->purchaseLedger($transaction);
                break;

            case PurchaseReturnItemTransaction::class:
                return $this->purchaseReturnItem($transaction);
                break;

            case PurchaseReturnLedgerTransaction::class:
                return $this->purchaseReturnLedger($transaction);
                break;

            case ExpenseCreditNoteLedgerTransaction::class:
                return $this->expenseCreditNoteLedger($transaction);
                break;

            case ExpenseCreditNoteItemTransaction::class:
                return $this->expenseCreditNoteItem($transaction);
                break;

            case ExpenseDebitNoteItemTransaction::class:
                return $this->expenseDebitNoteItem($transaction);
                break;

            case ExpenseDebitNoteLedgerTransaction::class:
                return $this->expenseDebitNoteLedger($transaction);
                break;

        }
    }

    public function saleItem($transaction)
    {
        $saleTransaction = $transaction->first()->saleTransaction;
        // Calculate the sum of 'taxable_value' for each sale transaction item
        $sum = $transaction->sum('taxable_value');

        return [
            'transaction_id' => $saleTransaction->id,
            'date' => $saleTransaction->date->format('d-m-y'),
            'ledger_name' => $saleTransaction->customer->name,
            'transaction_type' => 'Sale',
            'voucher_no' => $saleTransaction->full_invoice_number,
            'invoice_no' => '',
            'debit_amount' => 0,
            'narration' => $saleTransaction->narration ?? null,
            'credit_amount' => round($sum, 2), // Sum of 'taxable_value' for each sale transaction item
            'balance' => 0,
        ];
    }

    public function saleLedger($transaction)
    {
        $saleTransaction = $transaction->first()->saleTransaction;
        // Calculate the sum of 'taxable_value' for each sale transaction ledger
        $sum = $transaction->sum('taxable_value');

        return [
            'transaction_id' => $saleTransaction->id,
            'date' => $saleTransaction->date->format('d-m-y'),
            'ledger_name' => $saleTransaction->customer->name,
            'transaction_type' => 'Sale',
            'voucher_no' => $saleTransaction->full_invoice_number,
            'invoice_no' => '',
            'debit_amount' => 0,
            'narration' => $saleTransaction->narration ?? null,
            'credit_amount' => round($sum, 2),
            'balance' => 0,
        ];
    }

    public function saleReturnItem($transaction)
    {
        $saleReturnTransaction = $transaction->first()->saleReturnTransaction;
        $sum = $transaction->sum('taxable_value');

        return [
            'transaction_id' => $saleReturnTransaction->id,
            'date' => $saleReturnTransaction->date->format('d-m-y'),
            'ledger_name' => $saleReturnTransaction->customer->name,
            'transaction_type' => 'Sale Return',
            'voucher_no' => $saleReturnTransaction->full_invoice_number,
            'invoice_no' => '',
            'debit_amount' => round($sum, 2),
            'narration' => $saleReturnTransaction->narration ?? null,
            'credit_amount' => 0,
            'balance' => 0,
        ];
    }

    public function saleReturnLedger($transaction)
    {

        $saleReturnTransaction = $transaction->first()->saleReturnTransaction;
        $sum = $transaction->sum('taxable_value');

        return [
            'transaction_id' => $saleReturnTransaction->id,
            'date' => $saleReturnTransaction->date->format('d-m-y'),
            'ledger_name' => $saleReturnTransaction->customer->name,
            'transaction_type' => 'Sale Return',
            'voucher_no' => $saleReturnTransaction->full_invoice_number,
            'invoice_no' => '',
            'debit_amount' => round($sum, 2),
            'narration' => $saleReturnTransaction->narration ?? null,
            'credit_amount' => 0,
            'balance' => 0,
        ];
    }

    public function incomeCreditNoteItem($transaction)
    {
        $incomeCreditNoteTransaction = $transaction->first()->incomeCreditNoteTransaction;
        $sum = $transaction->sum('taxable_value');

        return [
            'transaction_id' => $incomeCreditNoteTransaction->id,
            'date' => $incomeCreditNoteTransaction->date->format('d-m-y'),
            'ledger_name' => $incomeCreditNoteTransaction->customer->name,
            'transaction_type' => 'Sale Cr. Note',
            'voucher_no' => $incomeCreditNoteTransaction->full_invoice_number,
            'invoice_no' => '',
            'debit_amount' => round($sum, 2),
            'credit_amount' => 0,
            'narration' => $incomeCreditNoteTransaction->narration ?? null,
            'balance' => 0,
        ];
    }

    public function incomeCreditNoteLedger($transaction)
    {
        $incomeCreditNoteTransaction = $transaction->first()->incomeCreditNoteTransaction;
        $sum = $transaction->sum('taxable_value');

        return [
            'transaction_id' => $incomeCreditNoteTransaction->id,
            'date' => $incomeCreditNoteTransaction->date->format('d-m-y'),
            'ledger_name' => $incomeCreditNoteTransaction->customer->name,
            'transaction_type' => 'Sale Cr. Note',
            'voucher_no' => $incomeCreditNoteTransaction->full_invoice_number,
            'invoice_no' => '',
            'debit_amount' => round($sum, 2),
            'credit_amount' => 0,
            'narration' => $incomeCreditNoteTransaction->narration ?? null,
            'balance' => 0,
        ];
    }

    public function incomeDebitNoteItem($transaction)
    {
        $incomeDebitNoteItemTransaction = $transaction->first()->incomeDebitNoteTransaction;
        $sum = $transaction->sum('taxable_value');

        return [
            'transaction_id' => $incomeDebitNoteItemTransaction->id,
            'date' => $incomeDebitNoteItemTransaction->date->format('d-m-y'),
            'ledger_name' => $incomeDebitNoteItemTransaction->customer->name,
            'transaction_type' => 'Sale Dr. Note',
            'voucher_no' => $incomeDebitNoteItemTransaction->full_invoice_number,
            'invoice_no' => '',
            'debit_amount' => 0,
            'narration' => $incomeDebitNoteItemTransaction->narration ?? null,
            'credit_amount' => round($sum, 2),
            'balance' => 0,
        ];
    }

    public function incomeDebitNoteLedger($transaction)
    {
        $incomeDebitNoteTransaction = $transaction->first()->incomeDebitNoteTransaction;
        $sum = $transaction->sum('taxable_value');

        return [
            'transaction_id' => $incomeDebitNoteTransaction->id,
            'date' => $incomeDebitNoteTransaction->date->format('d-m-y'),
            'ledger_name' => $incomeDebitNoteTransaction->customer->name,
            'transaction_type' => 'Sale Dr. Note',
            'voucher_no' => $incomeDebitNoteTransaction->full_invoice_number,
            'invoice_no' => '',
            'debit_amount' => 0,
            'narration' => $incomeDebitNoteTransaction->narration ?? null,
            'credit_amount' => round($sum, 2),
            'balance' => 0,
        ];
    }

    public function purchaseItem($transaction)
    {
        $purchaseTransaction = $transaction->first()->purchaseTransaction;
        $sum = $transaction->sum('taxable_value');

        return [
            'transaction_id' => $purchaseTransaction->id,
            'date' => $purchaseTransaction->voucher_date->format('d-m-y'),
            'ledger_name' => $purchaseTransaction->supplier->name,
            'transaction_type' => 'Purchase',
            'voucher_no' => $purchaseTransaction->voucher_number,
            'invoice_no' => $purchaseTransaction->sale_number,
            'narration' => $purchaseTransaction->narration ?? null,
            'debit_amount' => round($sum, 2),
            'credit_amount' => 0,
            'balance' => 0,
        ];
    }

    public function purchaseLedger($transaction)
    {
        $purchaseTransaction = $transaction->first()->purchaseTransaction;
        $sum = $transaction->sum('taxable_value');

        return [
            'transaction_id' => $purchaseTransaction->id,
            'date' => $purchaseTransaction->voucher_date->format('d-m-y'),
            'ledger_name' => $purchaseTransaction->supplier->name,
            'transaction_type' => 'Purchase',
            'voucher_no' => $purchaseTransaction->voucher_number,
            'invoice_no' => $purchaseTransaction->sale_number,
            'narration' => $purchaseTransaction->narration ?? null,
            'debit_amount' => round($sum, 2),
            'credit_amount' => 0,
            'balance' => 0,
        ];
    }

    public function purchaseReturnItem($transaction)
    {
        $purchaseTransaction = $transaction->first()->purchaseReturnTransaction;
        $sum = $transaction->sum('taxable_value');

        return [
            'transaction_id' => $purchaseTransaction->id,
            'date' => $purchaseTransaction->voucher_date->format('d-m-y'),
            'ledger_name' => $purchaseTransaction->supplier->name,
            'transaction_type' => 'Purchase Return',
            'voucher_no' => $purchaseTransaction->voucher_number,
            'invoice_no' => '',
            'debit_amount' => 0,
            'narration' => $purchaseTransaction->narration ?? null,
            'credit_amount' => round($sum, 2),
            'balance' => 0,
        ];
    }

    public function purchaseReturnLedger($transaction)
    {
        $purchaseReturnTransaction = $transaction->first()->purchaseReturnTransaction;
        $sum = $transaction->sum('taxable_value');

        return [
            'transaction_id' => $purchaseReturnTransaction->id,
            'date' => $purchaseReturnTransaction->voucher_date->format('d-m-y'),
            'ledger_name' => $purchaseReturnTransaction->supplier->name,
            'transaction_type' => 'Purchase Return',
            'voucher_no' => $purchaseReturnTransaction->voucher_number,
            'invoice_no' => '',
            'debit_amount' => 0,
            'narration' => $purchaseReturnTransaction->narration ?? null,
            'credit_amount' => round($sum, 2),
            'balance' => 0,
        ];
    }

    public function expenseCreditNoteLedger($transaction)
    {
        $expenseCreditTransaction = $transaction->first()->expenseCreditNotesTransaction;
        $sum = $transaction->sum('taxable_value');

        return [
            'transaction_id' => $expenseCreditTransaction->id,
            'date' => $expenseCreditTransaction->voucher_date->format('d-m-y'),
            'ledger_name' => $expenseCreditTransaction->supplier->name,
            'transaction_type' => 'Expense Cr. Note',
            'voucher_no' => $expenseCreditTransaction->voucher_number,
            'invoice_no' => '',
            'narration' => $expenseCreditTransaction->narration ?? null,
            'debit_amount' => round($sum, 2),
            'credit_amount' => 0,
            'balance' => 0,
        ];
    }

    public function expenseCreditNoteItem($transaction)
    {

        $expenseCreditTransaction = $transaction->first()->expenseCreditNotesTransaction;
        $sum = $transaction->sum('taxable_value');

        return [
            'transaction_id' => $expenseCreditTransaction->id,
            'date' => $expenseCreditTransaction->voucher_date->format('d-m-y'),
            'ledger_name' => $expenseCreditTransaction->supplier->name,
            'transaction_type' => 'Expense Cr. Note',
            'voucher_no' => $expenseCreditTransaction->voucher_number,
            'invoice_no' => '',
            'narration' => $expenseCreditTransaction->narration ?? null,
            'debit_amount' => round($sum, 2),
            'credit_amount' => 0,
            'balance' => 0,
        ];
    }

    public function expenseDebitNoteItem($transaction)
    {
        $expenseDebitTransaction = $transaction->first()->expenseDebitNotesTransaction;
        $sum = $transaction->sum('taxable_value');

        return [
            'transaction_id' => $expenseDebitTransaction->id,
            'date' => $expenseDebitTransaction->voucher_date->format('d-m-y'),
            'ledger_name' => $expenseDebitTransaction->supplier->name,
            'transaction_type' => 'Expense Dr. Note',
            'voucher_no' => $expenseDebitTransaction->voucher_number,
            'invoice_no' => '',
            'debit_amount' => 0,
            'narration' => $expenseDebitTransaction->narration ?? null,
            'credit_amount' => round($sum, 2),
            'balance' => 0,
        ];
    }

    public function expenseDebitNoteLedger($transaction)
    {
        $expenseDebitTransaction = $transaction->first()->expenseDebitNotesTransaction;
        $sum = $transaction->sum('taxable_value');

        return [
            'transaction_id' => $expenseDebitTransaction->id,
            'date' => $expenseDebitTransaction->voucher_date->format('d-m-y'),
            'ledger_name' => $expenseDebitTransaction->supplier->name,
            'transaction_type' => 'Expense Dr. Note',
            'voucher_no' => $expenseDebitTransaction->voucher_number,
            'invoice_no' => '',
            'debit_amount' => 0,
            'narration' => $expenseDebitTransaction->narration ?? null,
            'credit_amount' => round($sum, 2),
            'balance' => 0,
        ];
    }
}
