<?php

namespace App\Jobs;

use App\Actions\Report\GstDashboard\API\GetApiForGSTR1Data;
use App\Actions\Report\GstDashboard\API\GetApiForGSTR2AData;
use App\Actions\Report\GstDashboard\API\GetGSTReturnSubmittedMonth;
use App\Models\Company;
use App\Models\GstrLogin;
use App\Services\FileGstService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class GetGstDashboardData implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $timeout = 3600000;

    public $dashboardData;

    public $currentCompany;

    /** @var FileGstService */
    public mixed $fileGstService;

    /**
     * Create a new job instance.
     */
    public function __construct($data)
    {
        $this->dashboardData = $data;
        $this->fileGstService = new FileGstService();
        $this->currentCompany = Company::find($this->dashboardData['company_id']);
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            session(['current_company' => $this->currentCompany]);
            $gstin = $this->dashboardData['gstin'];
            $companyId = $this->dashboardData['company_id'];

            $gstLogin = GstrLogin::whereCompanyId($companyId)->first()?->toArray();

            if (empty($gstLogin)) {
                throw new \Exception("GST login not found for company_id: {$companyId}");
            }

            $gstService = $this->fileGstService;
            $gstData = $gstService->getGstInformation($gstin);

            $startDate = $gstData['result']['rgdt'] ?? null;

            if (! $startDate) {
                throw new \Exception("Start date (rgdt) not found in GST data response for GSTIN: {$gstin}");
            }

            [$returnPeriodsGstr1, $returnPeriodsGstr3BData] = GetGSTReturnSubmittedMonth::run($startDate, $gstin, $companyId);

            GetGstr3BDashboardDataJob::dispatch($this->dashboardData, $returnPeriodsGstr3BData);

            $gstActionTypes = ['B2B', 'CDN', 'CDNA'];

            // foreach ($returnPeriodsGstr1 as $month) {
            //     $data = GetApiForGSTR1Data::run($this->dashboardData, $gstLogin, $month);

            //     if (! $data) {
            //         GstrLogin::whereCompanyId($companyId)->update(['status' => false, 'synced' => false]);
            //         Log::warning("Failed to fetch GSTR1 data for month: {$month['ret_prd']}, company_id: {$companyId}");
            //     }

            //     $data = GetApiForGSTR2AData::run($this->dashboardData, $gstLogin, $gstActionTypes, $month);
            //     if (! $data) {
            //         GstrLogin::whereCompanyId($companyId)->update(['status' => false, 'synced' => false]);
            //         Log::warning("Failed to fetch GSTR2A data for month: {$month['ret_prd']}, company_id: {$companyId}");
            //     }
            // }
        } catch (\Throwable $e) {
            Log::error("Error processing GST dashboard data for company_id: {$companyId}, GSTIN: {$gstin}. Message: ".$e->getMessage(), [
                'exception' => $e,
                'dashboard_data' => $this->dashboardData ?? [],
            ]);

        }
    }
}
